@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: hsl(0, 0%, 0%);
  --foreground: hsl(0, 0%, 100%);
  --muted: hsl(0, 0%, 10%);
  --muted-foreground: hsl(0, 0%, 70%);
  --popover: hsl(0, 0%, 5%);
  --popover-foreground: hsl(0, 0%, 95%);
  --card: hsl(0, 0%, 5%);
  --card-foreground: hsl(0, 0%, 95%);
  --border: hsl(340, 100%, 35%);
  --input: hsl(0, 0%, 10%);
  --primary: hsl(340, 100%, 35%);
  --primary-foreground: hsl(0, 0%, 100%);
  --secondary: hsl(150, 100%, 35%);
  --secondary-foreground: hsl(0, 0%, 0%);
  --accent: hsl(150, 100%, 35%);
  --accent-foreground: hsl(0, 0%, 0%);
  --destructive: hsl(0, 84%, 60%);
  --destructive-foreground: hsl(0, 100%, 100%);
  --ring: hsl(340, 100%, 35%);
  --radius: 0.5rem;
  
  /* Squid Game Colors */
  --squid-pink: rgb(255, 0, 85);
  --squid-green: hsl(150, 100%, 35%);
  --squid-red: hsl(348, 83%, 47%);
  --electric-blue: hsl(195, 100%, 50%);
}

.flex.space-x-6.mb-12 {
    display: none !important;
}

.flex.space-x-4.text-squid-pink.text-2xl.font-bold {
    font-size: 40px !important;
}
p.text-squid-green.font-medium.text-lg.mt-4.animate-float {
    color: white !important;
    margin-top: 20 !important;
}


.dark {
  --background: hsl(0, 0%, 5%);
  --foreground: hsl(0, 0%, 90%);
  --muted: hsl(0, 0%, 15%);
  --muted-foreground: hsl(0, 0%, 60%);
  --popover: hsl(0, 0%, 10%);
  --popover-foreground: hsl(0, 0%, 85%);
  --card: hsl(0, 0%, 10%);
  --card-foreground: hsl(0, 0%, 85%);
  --border: hsl(0, 0%, 20%);
  --input: hsl(0, 0%, 15%);
  --primary: hsl(0, 0%, 20%);
  --primary-foreground: hsl(340, 100%, 35%);
  --secondary: hsl(0, 0%, 15%);
  --secondary-foreground: hsl(150, 100%, 35%);
  --accent: hsl(0, 0%, 15%);
  --accent-foreground: hsl(150, 100%, 35%);
  --destructive: hsl(0, 62%, 30%);
  --destructive-foreground: hsl(0, 0%, 98%);
  --ring: hsl(0, 0%, 30%);
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
    font-family: 'Poppins', sans-serif;
    cursor: none;
  }
  
  h1, h2, h3, h4, h5, h6 {
    font-family: 'Bebas Neue', cursive;
  }
}

@layer components {
  .squid-cursor {
    position: fixed;
    width: 20px;
    height: 20px;
    background: var(--squid-pink);
    border-radius: 50%;
    pointer-events: none;
    z-index: 9999;
    mix-blend-mode: difference;
    transition: transform 0.1s ease;
  }
  

  
  .scroll-progress {
    position: fixed;
    top: 0;
    left: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--squid-pink), var(--squid-green));
    z-index: 1000;
    transition: width 0.1s ease;
  }
  
  .card-3d {
    transform-style: preserve-3d;
    transition: transform 0.6s;
  }
  
  .card-3d:hover {
    transform: rotateY(180deg);
  }
  
  .card-face {
    position: absolute;
    width: 100%;
    height: 100%;
    backface-visibility: hidden;
  }
  
  .card-back {
    transform: rotateY(180deg);
  }
  
  .nfc-pulse {
    animation: pulse-glow 1s ease-in-out infinite;
  }
  
  .guard-figure {
    width: 8rem;
    height: 12rem;
    position: relative;
  }
  
.guard-body {
    width: 100%;
    height: 100%;
    background: rgb(255, 0, 85);
    border-radius: 4rem 4rem 0rem 0rem !important;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}
  
.guard-mask-triangular {
    position: absolute;
    top: 4rem;
    left: 50%;
    transform: translateX(-50%);
    width: 4rem;
    height: 4rem;
    background: black;
    border-radius: 2.5rem;
    display: flex
;
    align-items: center;
    justify-content: center;
}

  .guard-mask-triangular::before {
    content: '';
    width: 1.5rem;
    height: 1.5rem;
    background: white;
    clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
    border: 2px solid white;
  }

.guard-mask-round {
    position: absolute;
    top: 4rem;
    left: 50%;
    transform: translateX(-50%);
    width: 4rem;
    height: 4rem;
    background: black;
    border-radius: 2.5rem;
    display: flex
;
    align-items: center;
    justify-content: center;
}

  .guard-mask-round::before {
    content: '';
    width: 1.5rem;
    height: 1.5rem;
    background: white;
    border-radius: 50%;
  }

  .guard-eyes {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    gap: 0.3rem;
    z-index: 10;
  }

  .guard-eye-left, .guard-eye-right {
    width: 0.4rem;
    height: 0.4rem;
    background: radial-gradient(circle, rgb(255, 0, 85), #cc0044);
    border-radius: 50%;
    animation: red-light-bloom 2s ease-in-out infinite;
    box-shadow:
      0 0 8px rgb(255, 0, 85),
      0 0 16px rgb(255, 0, 85),
      0 0 24px rgb(255, 0, 85);
  }
  
  .squid-shape {
    width: 4rem;
    height: 4rem;
    background: var(--squid-pink);
    border-radius: 50% 50% 0 50%;
    position: relative;
    transform: rotate(45deg);
  }
  
  .squid-shape::before {
    content: '';
    position: absolute;
    width: 1rem;
    height: 2rem;
    background: var(--squid-pink);
    border-radius: 50%;
    top: -0.5rem;
    left: 50%;
    transform: translateX(-50%) rotate(-45deg);
  }
  
  .squid-shape::after {
    content: '';
    position: absolute;
    width: 0.5rem;
    height: 0.5rem;
    background: white;
    border-radius: 50%;
    top: 1rem;
    left: 1rem;
    transform: rotate(-45deg);
  }
}

@layer utilities {
  @keyframes glitch {
    0% { transform: translate(0) }
    20% { transform: translate(-2px, 2px) }
    40% { transform: translate(-2px, -2px) }
    60% { transform: translate(2px, 2px) }
    80% { transform: translate(2px, -2px) }
    100% { transform: translate(0) }
  }
  
  @keyframes pulse-glow {
    0% { box-shadow: 0 0 5px var(--squid-pink), 0 0 10px var(--squid-pink), 0 0 15px var(--squid-pink); }
    100% { box-shadow: 0 0 10px var(--squid-pink), 0 0 20px var(--squid-pink), 0 0 30px var(--squid-pink); }
  }
  
  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
  }
  
  @keyframes liquid {
    0% { border-radius: 50%; transform: scale(1); }
    50% { border-radius: 60% 40% 30% 70%; transform: scale(1.1); }
    100% { border-radius: 50%; transform: scale(1); }
  }
  
  @keyframes guard-sway {
    0%, 100% { transform: rotate(-1deg); }
    50% { transform: rotate(1deg); }
  }
  
  @keyframes number-flip {
    0% { transform: rotateY(0deg); }
    50% { transform: rotateY(90deg); }
    100% { transform: rotateY(0deg); }
  }
  
  @keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
  }
  
  .animate-glitch {
    animation: glitch 0.3s ease-in-out infinite alternate;
  }
  
  .animate-pulse-glow {
    animation: pulse-glow 2s ease-in-out infinite alternate;
  }
  
  .animate-float {
    animation: float 3s ease-in-out infinite;
  }
  
  .animate-liquid {
    animation: liquid 0.6s ease-out;
  }
  
  .animate-guard-sway {
    animation: guard-sway 3s ease-in-out infinite;
  }

  .animate-number-flip {
    animation: number-flip 0.8s ease-out;
  }

  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .digital-display {
    font-family: 'Courier New', 'Lucida Console', monospace;
    font-weight: 900;
    color: #ff0000 !important;
    text-align: center;
    display: block;
    letter-spacing: 0.2em;
    text-shadow:
      0 0 2px #ff0000,
      0 0 4px #ff0000,
      0 0 8px #ff0000,
      0 0 12px #ff0000,
      0 0 16px #ff0000,
      0 0 20px #ff0000;
    filter: brightness(1.3) contrast(1.2);
    animation: digital-flicker 3s ease-in-out infinite;
    position: relative;
  }

  .digital-display::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: repeating-linear-gradient(
      0deg,
      transparent,
      transparent 1px,
      rgba(255, 0, 0, 0.03) 1px,
      rgba(255, 0, 0, 0.03) 2px
    );
    pointer-events: none;
  }

  .digital-display::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: repeating-linear-gradient(
      90deg,
      transparent,
      transparent 1px,
      rgba(255, 0, 0, 0.03) 1px,
      rgba(255, 0, 0, 0.03) 2px
    );
    pointer-events: none;
  }

  @keyframes digital-flicker {
    0%, 98% {
      opacity: 1;
      text-shadow:
        0 0 2px #ff0000,
        0 0 4px #ff0000,
        0 0 8px #ff0000,
        0 0 12px #ff0000,
        0 0 16px #ff0000,
        0 0 20px #ff0000;
    }
    99% {
      opacity: 0.95;
      text-shadow:
        0 0 1px #ff0000,
        0 0 2px #ff0000,
        0 0 4px #ff0000,
        0 0 8px #ff0000;
    }
    100% {
      opacity: 1;
      text-shadow:
        0 0 2px #ff0000,
        0 0 4px #ff0000,
        0 0 8px #ff0000,
        0 0 12px #ff0000,
        0 0 16px #ff0000,
        0 0 20px #ff0000;
    }
  }

  /* Glitch Text Effect */
  .glitch-text {
    position: relative;
    animation: glitch-main 2s infinite;
  }

  .glitch-text::before,
  .glitch-text::after {
    content: attr(data-text);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }

  .glitch-text::before {
    animation: glitch-1 0.5s infinite;
    color: rgb(255, 0, 85);
    z-index: -1;
  }

  .glitch-text::after {
    animation: glitch-2 0.5s infinite;
    color: #00ff88;
    z-index: -2;
  }

  /* Mobile Guard Styles */
 .guard-figure-mobile {
    width: 8rem;
    height: 10rem;
    position: relative;
}

  .guard-body-mobile {
    width: 100%;
    height: 100%;
    background: rgb(255, 0, 85);
     border-radius: 4rem 4rem 0rem 0rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.3);
    position: relative;
  }


 .guard-mask-triangular-mobile {
    position: absolute;
    top: 3rem;
    left: 50%;
    transform: translateX(-50%);
    width: 4rem;
    height: 4rem;
    background: black;
    border-radius: 3.3rem
52.8px
;
    display: flex
;
    align-items: center;
    justify-content: center;
}
 


.guard-mask-triangular-mobile::before {
    content: '';
    width: 2rem;
    height: 2rem;
    background: white;
    clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
}

  .guard-mask-round-mobile {
    position: absolute;
    top: 3rem;
    left: 50%;
    transform: translateX(-50%);
    width: 4rem;
    height: 4rem;
    background: black;
    border-radius: 2.3rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.guard-mask-round-mobile::before {
    content: '';
    width: 2rem;
    height: 2rem;
    background: white;
    border-radius: 50%;
}

  .guard-eyes-mobile {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    gap: 0.2rem;
    z-index: 10;
  }

  .guard-eye-left-mobile, .guard-eye-right-mobile {
    width: 0.25rem;
    height: 0.25rem;
    background: radial-gradient(circle, rgb(255, 0, 85), #cc0044);
    border-radius: 50%;
    animation: red-light-bloom 2s ease-in-out infinite;
    box-shadow:
      0 0 4px rgb(255, 0, 85),
      0 0 8px rgb(255, 0, 85),
      0 0 12px rgb(255, 0, 85);
  }

  /* Anime-style Guard Figures */
  .anime-guard-figure {
    width: 10rem;
    height: 14rem;
    position: relative;
    filter: drop-shadow(0 10px 20px rgba(0,0,0,0.5));
  }

  .anime-guard-body {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--squid-red), rgb(255, 0, 85));
    border-radius: 4rem 4rem 0rem 0rem;
    position: relative;
    border: 3px solid rgba(255,255,255,0.1);
  }

  .anime-guard-head {
    position: absolute;
    top: 0.5rem;
    left: 50%;
    transform: translateX(-50%);
    width: 4rem;
    height: 4rem;
    background: linear-gradient(135deg, var(--squid-red), #cc1144);
    border-radius: 50%;
    border: 2px solid rgba(255,255,255,0.2);
  }

  .anime-guard-mask-triangular {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 2.5rem;
    height: 2.5rem;
    background: linear-gradient(135deg, #000, #333);
    clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
    border: 2px solid white;
    box-shadow: inset 0 0 10px rgba(255,0,85,0.3);
  }

  .anime-guard-mask-round {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 2.5rem;
    height: 2.5rem;
    background: linear-gradient(135deg, #000, #333);
    border-radius: 50%;
    border: 2px solid white;
    box-shadow: inset 0 0 10px rgba(0,255,85,0.3);
  }

  .mask-inner-glow {
    position: absolute;
    inset: 2px;
    background: radial-gradient(circle, rgba(255,255,255,0.1), transparent);
    border-radius: inherit;
    clip-path: inherit;
  }

  .anime-guard-eyes {
    position: absolute;
    top: 60%;
    left: 50%;
    transform: translateX(-50%);
    width: 100%;
    height: 0.5rem;
    display: flex;
    justify-content: space-around;
    align-items: center;
  }

  .eye-left, .eye-right {
    width: 0.3rem;
    height: 0.3rem;
    background: rgba(255,255,255,0.8);
    border-radius: 50%;
    animation: blink 3s ease-in-out infinite;
  }

  .anime-guard-arms {
    position: absolute;
    top: 4rem;
    width: 100%;
    height: 3rem;
  }

  .arm-left, .arm-right {
    position: absolute;
    width: 1.5rem;
    height: 3rem;
    background: linear-gradient(135deg, var(--squid-red), #cc1144);
    border-radius: 0.8rem;
    border: 2px solid rgba(255,255,255,0.1);
  }

  .arm-left {
    left: -0.5rem;
    transform: rotate(-15deg);
  }

  .arm-right {
    right: -0.5rem;
    transform: rotate(15deg);
  }

  .anime-guard-legs {
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 4rem;
  }

  .leg-left, .leg-right {
    position: absolute;
    width: 2rem;
    height: 4rem;
    background: linear-gradient(135deg, var(--squid-red), #cc1144);
    border-radius: 0 0 1rem 1rem;
    border: 2px solid rgba(255,255,255,0.1);
  }

  .leg-left {
    left: 1rem;
  }

  .leg-right {
    right: 1rem;
  }

  .anime-guard-shadow {
    position: absolute;
    bottom: -1rem;
    left: 50%;
    transform: translateX(-50%);
    width: 8rem;
    height: 1rem;
    background: radial-gradient(ellipse, rgba(0,0,0,0.3), transparent);
    border-radius: 50%;
  }
  
  .animate-shake {
    animation: shake 0.5s ease-in-out;
  }
  
  .text-squid-pink {
    color: var(--squid-pink);
  }
  
  .text-squid-green {
    color: var(--squid-green);
  }
  
  .text-squid-red {
    color: var(--squid-red);
  }
  
  .bg-squid-pink {
    background-color: var(--squid-pink);
  }
  
  .bg-squid-green {
    background-color:rgb(8 106 83);
  }

  .bg-squid-red {
    background-color: var(--squid-red);
  }
  
  .border-squid-pink {
    border-color: var(--squid-pink);
  }
  
  .border-squid-green {
    border-color: var(--squid-green);
  }
  
  .border-squid-red {
    border-color: var(--squid-red);
  }

  /* Mobile anime guard styles */
  .anime-guard-figure-small {
    width: 4rem;
    height: 6rem;
    position: relative;
    filter: drop-shadow(0 5px 10px rgba(0,0,0,0.3));
  }

  .anime-guard-body-small {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--squid-red), rgb(255, 0, 85));
     border-radius: 4rem 4rem 0rem 0rem;
    position: relative;
    border: 2px solid rgba(255,255,255,0.1);
  }

  .anime-guard-mask-triangular-small {
    position: absolute;
    top: 0.5rem;
    left: 50%;
    transform: translateX(-50%);
    width: 1.5rem;
    height: 1.5rem;
    background: linear-gradient(135deg, #000, #333);
    clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
    border: 1px solid white;
    box-shadow: inset 0 0 5px rgba(255,0,85,0.3);
  }

  .anime-guard-mask-round-small {
    position: absolute;
    top: 0.5rem;
    left: 50%;
    transform: translateX(-50%);
    width: 1.5rem;
    height: 1.5rem;
    background: linear-gradient(135deg, #000, #333);
    border-radius: 50%;
    border: 1px solid white;
    box-shadow: inset 0 0 5px rgba(0,255,85,0.3);
  }

  .mask-inner-glow-small {
    position: absolute;
    inset: 1px;
    background: radial-gradient(circle, rgba(255,255,255,0.1), transparent);
    border-radius: inherit;
    clip-path: inherit;
  }

  /* Additional keyframes */
  @keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-10px) rotate(1deg); }
    66% { transform: translateY(-5px) rotate(-1deg); }
  }

  @keyframes blink {
    0%, 90%, 100% { opacity: 1; }
    95% { opacity: 0.3; }
  }
}

@keyframes red-light-bloom {
  0%, 100% {
    box-shadow:
      0 0 5px rgb(255, 0, 85),
      0 0 10px rgb(255, 0, 85),
      0 0 15px rgb(255, 0, 85);
    opacity: 0.8;
  }
  50% {
    box-shadow:
      0 0 10px rgb(255, 0, 85),
      0 0 20px rgb(255, 0, 85),
      0 0 30px rgb(255, 0, 85),
      0 0 40px rgb(255, 0, 85);
    opacity: 1;
  }
}

@keyframes glitch-main {
  0%, 100% { transform: translate(0); }
  20% { transform: translate(-2px, 2px); }
  40% { transform: translate(-2px, -2px); }
  60% { transform: translate(2px, 2px); }
  80% { transform: translate(2px, -2px); }
}

@keyframes glitch-1 {
  0%, 100% {
    transform: translate(0);
    clip-path: polygon(0 0, 100% 0, 100% 5%, 0 5%);
  }
  20% {
    transform: translate(-2px, 2px);
    clip-path: polygon(0 15%, 100% 15%, 100% 25%, 0 25%);
  }
  40% {
    transform: translate(-2px, -2px);
    clip-path: polygon(0 35%, 100% 35%, 100% 45%, 0 45%);
  }
  60% {
    transform: translate(2px, 2px);
    clip-path: polygon(0 55%, 100% 55%, 100% 65%, 0 65%);
  }
  80% {
    transform: translate(2px, -2px);
    clip-path: polygon(0 75%, 100% 75%, 100% 85%, 0 85%);
  }
}

@keyframes glitch-2 {
  0%, 100% {
    transform: translate(0);
    clip-path: polygon(0 10%, 100% 10%, 100% 20%, 0 20%);
  }
  20% {
    transform: translate(2px, -2px);
    clip-path: polygon(0 30%, 100% 30%, 100% 40%, 0 40%);
  }
  40% {
    transform: translate(2px, 2px);
    clip-path: polygon(0 50%, 100% 50%, 100% 60%, 0 60%);
  }
  60% {
    transform: translate(-2px, -2px);
    clip-path: polygon(0 70%, 100% 70%, 100% 80%, 0 80%);
  }
  80% {
    transform: translate(-2px, 2px);
    clip-path: polygon(0 90%, 100% 90%, 100% 100%, 0 100%);
  }
}

@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
