<?php
// SquidLink PHP Deployment Script
// Run this after uploading to check everything is working

echo "<!DOCTYPE html>";
echo "<html><head><title>SquidLink Deployment Check</title>";
echo "<style>body{font-family:Arial,sans-serif;max-width:800px;margin:50px auto;padding:20px;background:#000;color:#fff;}";
echo ".success{color:#00ffaa;} .error{color:#ff0055;} .warning{color:#ffaa00;} h1{color:#ff0055;}</style></head><body>";

echo "<h1>🦑 SquidLink PHP Deployment Check</h1>";

// Check PHP version
echo "<h2>PHP Version Check</h2>";
$phpVersion = phpversion();
if (version_compare($phpVersion, '8.0.0', '>=')) {
    echo "<p class='success'>✅ PHP $phpVersion (Compatible)</p>";
} else {
    echo "<p class='error'>❌ PHP $phpVersion (Requires PHP 8.0+)</p>";
}

// Check required extensions
echo "<h2>PHP Extensions</h2>";
$extensions = ['json', 'session', 'filter', 'fileinfo'];
foreach ($extensions as $ext) {
    if (extension_loaded($ext)) {
        echo "<p class='success'>✅ $ext extension loaded</p>";
    } else {
        echo "<p class='error'>❌ $ext extension missing</p>";
    }
}

// Check file permissions
echo "<h2>File Permissions</h2>";
$checkDirs = ['logs', 'assets'];
foreach ($checkDirs as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
        echo "<p class='success'>✅ Created directory: $dir</p>";
    } else {
        echo "<p class='success'>✅ Directory exists: $dir</p>";
    }
    
    if (is_writable($dir)) {
        echo "<p class='success'>✅ $dir is writable</p>";
    } else {
        echo "<p class='warning'>⚠️ $dir may not be writable</p>";
    }
}

// Check .htaccess
echo "<h2>Apache Configuration</h2>";
if (file_exists('.htaccess')) {
    echo "<p class='success'>✅ .htaccess file exists</p>";
    
    if (function_exists('apache_get_modules')) {
        $modules = apache_get_modules();
        $required = ['mod_rewrite', 'mod_headers'];
        foreach ($required as $mod) {
            if (in_array($mod, $modules)) {
                echo "<p class='success'>✅ $mod enabled</p>";
            } else {
                echo "<p class='warning'>⚠️ $mod may not be enabled</p>";
            }
        }
    } else {
        echo "<p class='warning'>⚠️ Cannot check Apache modules</p>";
    }
} else {
    echo "<p class='error'>❌ .htaccess file missing</p>";
}

// Check mail function
echo "<h2>Email Functionality</h2>";
if (function_exists('mail')) {
    echo "<p class='success'>✅ PHP mail() function available</p>";
} else {
    echo "<p class='warning'>⚠️ PHP mail() not available - contact form will log to files</p>";
}

// Test API endpoints
echo "<h2>API Endpoints</h2>";
$apiTests = [
    'contact' => 'api/contact.php',
    'nfc' => 'api/nfc.php'
];

foreach ($apiTests as $name => $file) {
    if (file_exists($file)) {
        echo "<p class='success'>✅ $name API file exists</p>";
    } else {
        echo "<p class='error'>❌ $name API file missing</p>";
    }
}

// Check assets
echo "<h2>Assets</h2>";
$assetDirs = ['css', 'js', 'assets/sounds', 'assets/icons'];
foreach ($assetDirs as $dir) {
    if (is_dir($dir)) {
        echo "<p class='success'>✅ $dir directory exists</p>";
    } else {
        echo "<p class='warning'>⚠️ $dir directory missing</p>";
    }
}

// Security check
echo "<h2>Security</h2>";
$securityFiles = ['.env', 'composer.json', 'package.json'];
foreach ($securityFiles as $file) {
    if (file_exists($file)) {
        echo "<p class='warning'>⚠️ $file exists - ensure it's protected</p>";
    } else {
        echo "<p class='success'>✅ No sensitive $file found</p>";
    }
}

// Final status
echo "<h2>Deployment Status</h2>";
$errors = substr_count(ob_get_contents(), 'class="error"');
$warnings = substr_count(ob_get_contents(), 'class="warning"');

if ($errors == 0 && $warnings == 0) {
    echo "<p class='success'>🎉 Perfect! Your SquidLink site is ready to go!</p>";
    echo "<p><a href='/' style='color:#00ffaa;'>Visit Your Site</a></p>";
} elseif ($errors == 0) {
    echo "<p class='warning'>⚠️ Site is functional but has some warnings</p>";
    echo "<p><a href='/' style='color:#00ffaa;'>Visit Your Site</a></p>";
} else {
    echo "<p class='error'>❌ Please fix the errors above before going live</p>";
}

echo "<hr><p><small>Delete this deploy.php file after checking</small></p>";
echo "</body></html>";
?>
