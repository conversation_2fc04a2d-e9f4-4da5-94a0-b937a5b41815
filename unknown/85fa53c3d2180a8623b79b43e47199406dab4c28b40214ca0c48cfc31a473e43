<?php
// NFC API - PHP 8.4.7
// Handles NFC tap events and logging

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET');
header('Access-Control-Allow-Headers: Content-Type');

// Get request method
$method = $_SERVER['REQUEST_METHOD'];

if ($method === 'POST') {
    // Handle NFC tap event
    handleNFCTap();
} elseif ($method === 'GET') {
    // Get NFC statistics
    getNFCStats();
} else {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
}

function handleNFCTap() {
    // Get NFC data
    $input = json_decode(file_get_contents('php://input'), true);
    
    $nfc_data = [
        'timestamp' => date('Y-m-d H:i:s'),
        'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
        'tag_data' => $input['tag_data'] ?? null,
        'session_id' => session_id(),
        'player_number' => 456
    ];
    
    // Log NFC tap
    $log_dir = __DIR__ . '/../logs';
    if (!is_dir($log_dir)) {
        mkdir($log_dir, 0755, true);
    }
    
    $log_file = $log_dir . '/nfc_' . date('Y-m-d') . '.log';
    file_put_contents($log_file, json_encode($nfc_data, JSON_PRETTY_PRINT) . "\n\n", FILE_APPEND | LOCK_EX);
    
    // Update session
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    
    $_SESSION['nfc_taps'] = ($_SESSION['nfc_taps'] ?? 0) + 1;
    $_SESSION['last_nfc_tap'] = time();
    
    // Response
    echo json_encode([
        'success' => true,
        'message' => 'NFC tap detected and logged',
        'player_number' => 456,
        'tap_count' => $_SESSION['nfc_taps'],
        'animation' => 'squid_game_activation'
    ]);
}

function getNFCStats() {
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    
    // Get today's NFC logs
    $log_file = __DIR__ . '/../logs/nfc_' . date('Y-m-d') . '.log';
    $total_taps_today = 0;
    
    if (file_exists($log_file)) {
        $log_content = file_get_contents($log_file);
        $total_taps_today = substr_count($log_content, '"timestamp"');
    }
    
    echo json_encode([
        'success' => true,
        'stats' => [
            'session_taps' => $_SESSION['nfc_taps'] ?? 0,
            'total_taps_today' => $total_taps_today,
            'last_tap' => $_SESSION['last_nfc_tap'] ?? null,
            'nfc_supported' => true,
            'player_number' => 456
        ]
    ]);
}
?>
