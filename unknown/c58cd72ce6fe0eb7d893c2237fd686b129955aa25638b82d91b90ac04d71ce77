@echo off
echo Starting SquidLink Server...
echo.

REM Get the directory where this batch file is located
set SCRIPT_DIR=%~dp0

REM Change to the SquidLink directory
cd /d "%SCRIPT_DIR%SquidLink"

REM Check if node_modules exists
if not exist "node_modules" (
    echo Installing dependencies...
    npm install
    echo.
)

REM Set environment variable and start the server
set NODE_ENV=development
echo Starting development server on http://localhost:5000
echo Press Ctrl+C to stop the server
echo.
npx tsx server/index.ts

pause
