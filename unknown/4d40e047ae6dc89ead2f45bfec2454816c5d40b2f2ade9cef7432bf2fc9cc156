#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🦑 Starting SquidLink Server...\n');

// Get the directory where this script is located
const scriptDir = __dirname;
const squidLinkDir = path.join(scriptDir, 'SquidLink');

// Change to the SquidLink directory
process.chdir(squidLinkDir);

// Check if node_modules exists
if (!fs.existsSync('node_modules')) {
    console.log('📦 Installing dependencies...');
    const installProcess = spawn('npm', ['install'], { 
        stdio: 'inherit',
        shell: true 
    });
    
    installProcess.on('close', (code) => {
        if (code === 0) {
            console.log('✅ Dependencies installed successfully\n');
            startServer();
        } else {
            console.error('❌ Failed to install dependencies');
            process.exit(1);
        }
    });
} else {
    startServer();
}

function startServer() {
    console.log('🚀 Starting development server on http://localhost:5000');
    console.log('📝 Press Ctrl+C to stop the server\n');
    
    // Set environment variable
    process.env.NODE_ENV = 'development';
    
    // Start the server
    const serverProcess = spawn('npx', ['tsx', 'server/index.ts'], {
        stdio: 'inherit',
        shell: true,
        env: { ...process.env, NODE_ENV: 'development' }
    });
    
    serverProcess.on('close', (code) => {
        if (code !== 0) {
            console.error(`❌ Server exited with code ${code}`);
        }
        console.log('👋 Server stopped');
    });
    
    // Handle Ctrl+C gracefully
    process.on('SIGINT', () => {
        console.log('\n🛑 Stopping server...');
        serverProcess.kill('SIGINT');
    });
}
