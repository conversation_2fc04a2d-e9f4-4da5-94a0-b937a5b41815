# SquidLink Server - Easy Startup Guide

This guide provides multiple ways to start the SquidLink server, making it easy to run from anywhere on your system.

## 🚀 Quick Start Options

### Option 1: Windows Batch File (Recommended for Windows)
Double-click `start-server.bat` or run from command prompt:
```cmd
start-server.bat
```

### Option 2: PowerShell Script
Right-click `start-server.ps1` and select "Run with PowerShell" or run:
```powershell
.\start-server.ps1
```

### Option 3: Cross-Platform Node.js Script
```bash
node start-server.js
```

### Option 4: NPM Scripts (from SquidLink directory)
```bash
# Cross-platform (recommended)
npm run dev:cross

# Windows specific
npm run dev:win

# Unix/Linux/Mac
npm run dev
```

## 📁 File Structure
```
SquidLink2/
├── start-server.bat      # Windows batch file
├── start-server.ps1      # PowerShell script
├── start-server.js       # Cross-platform Node.js script
├── README.md            # This file
└── SquidLink/           # Main application directory
    ├── package.json     # Node.js dependencies and scripts
    ├── server/          # Server code
    └── client/          # Frontend code
```

## 🌐 Accessing the Application

Once the server starts successfully, you can access the application at:
- **http://localhost:5000**

The server will automatically:
- Install dependencies if needed
- Start both the API server and frontend
- Enable hot-reload for development

## 🛑 Stopping the Server

Press `Ctrl+C` in the terminal/command prompt to stop the server.

## 🔧 Troubleshooting

### Port Already in Use
If port 5000 is already in use, you'll see an error. Stop any other applications using port 5000 or modify the port in `SquidLink/server/index.ts`.

### Dependencies Not Installed
The startup scripts will automatically install dependencies, but you can manually install them:
```bash
cd SquidLink
npm install
```

### Permission Issues (PowerShell)
If you get execution policy errors with PowerShell, run:
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

## 📝 Making It Portable

To make the server easy to run from anywhere:

1. **Create Desktop Shortcut**: Right-click `start-server.bat` → "Create shortcut" → Move to Desktop
2. **Add to PATH**: Add the folder containing these scripts to your system PATH
3. **Pin to Taskbar**: Right-click the batch file → "Pin to taskbar"

## 🔄 Auto-Start Features

All startup scripts include:
- ✅ Automatic dependency installation
- ✅ Environment setup
- ✅ Error handling
- ✅ Clear status messages
- ✅ Cross-platform compatibility

## 💡 Development Notes

- The server runs in development mode with hot-reload enabled
- Frontend and backend are served from the same port (5000)
- Database migrations run automatically if needed
- Logs are displayed in the console for debugging

## 🎨 Design Features

- **Cute Squid Game Aesthetic**: Anime-style guards with triangular and round masks
- **Mobile Responsive**: Optimized layout for all screen sizes
- **Centered Content**: Contact form perfectly centered with guards on sides
- **Interactive Elements**: Floating animations, glowing effects, and smooth transitions
- **Emoji Integration**: Fun emojis in form placeholders and buttons
- **Dynamic Status**: Visual feedback for form submission states
