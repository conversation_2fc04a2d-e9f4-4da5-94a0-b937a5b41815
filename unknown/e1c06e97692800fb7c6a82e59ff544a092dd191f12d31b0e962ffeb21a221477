Design a mobile-first, ultra-responsive website that activates when an NFC card is tapped. The site must have:

1. **Squid Game Aesthetic**
   - Color scheme: Vibrant pink (#ff0055) + mint green (#00ffaa) + stark white + void black
   - Primary font: "Bebas Neue" (headers) with "Poppins" (body)
   - UI elements: Honeycomb patterns, geometric shapes, and subtle game references
   - Animations: Liquid hover effects, glitch transitions, and particle explosions

2. **Immersive Audio Experience**
   - Background: Subtle Squid Game OST remix (looping)
   - Sound effects: 
     • NFC tap: "doll detected" sound
     • Hover: Glass marble rolling
     • Click: Game controller confirmation beep

3. **Modern Layout (Single Page)**
   Section 1: Hero Zone
   - Animated Squid Game logo intro (dissolving into particles)
   - Your name with "Player 456" style ID badge hover effect
   - Social links as glowing hexagonal buttons (Instagram, GitHub, LinkedIn, etc.)

   Section 2: Link Hub
   - Interactive 3D carousel of your projects
   - Each card flips to reveal details on hover
   - "Red Light/Green Light" progress bar during loading

   Section 3: Project Showcase
   - Responsive grid of project screenshots
   - Each expands into animated case study with:
     • Squid Game-style number overlay
     • "Eliminated" hover animation for less relevant projects

   Section 4: Contact Arena
   - Animated phone/mail icons that transform into guards when clicked
   - Form with input fields that shake if invalid (like failed game)

4. **Technical Requirements**
   - Load time < 2s (optimized assets)
   - CSS-only animations (no heavy JS)
   - NFC detection via: 
     ```html
     <script>
     if (window.NDEFReader) {
       navigator.permissions.query({ name: "nfc" })
         .then(perm => {
           if (perm.state === "granted") {
             const nfc = new NDEFReader();
             nfc.scan().then(() => {
               playActivationSound(); 
               startParticleAnimation();
             });
           }
         });
     }
     </script>
     ```
   - Progressive Web App capable (offline fallback)

5. **Special Effects**
   - Particle.js background with player-number shapes
   - CSS keyframe glitches during section transitions
   - "Game Over" 404 page with guard mask animation
   - Dark mode toggle (switches to guard uniform colors)

6. **Microinteractions**
   - Social icons: Melt into liquid on hover
   - Scroll progress: Blood droplet fills as user scrolls
   - Cursor: Custom red circle that pulses near interactables

Include accessibility features (color contrast mode, reduced motion toggle) and GDPR-compliant analytics.
Key Implementation Notes:

NFC Setup:

Use NDEF standard (NFC Data Exchange Format)

Program NFC tags with https://yourdomain.com using apps like "NFC Tools"

Add <meta name="theme-color" content="#ff0055"> for mobile PWA

Performance Optimizations:

Compress background audio with WebM format

Use CSS @media (prefers-reduced-motion) for animations

Lazy-load project images with blurred placeholders

Squid Game Elements:

Header: Animated player number (changes on refresh)

Buttons: Tracksuit red/green color coding

Error states: "Eliminated!" animation with guard whistle

Font Alternatives:

css
@import url('https://fonts.googleapis.com/css2?family=Bebas+Neue&family=Poppins:wght@300;600&display=swap');
Animation Samples:

css
/* Honeycomb grid */
.social-grid {
  display: flex;
  flex-wrap: wrap;
  clip-path: url(#hexagon-mask);
}

/* Glitch effect */
@keyframes glitch {
  0% { transform: translate(0) }
  20% { transform: translate(-5px, 5px) }
  40% { transform: translate(-5px, -5px) }
  60% { transform: translate(5px, 5px) }
  80% { transform: translate(5px, -5px) }
  100% { transform: translate(0) }
}