import { useState, useCallback } from "react";

export function useNFC() {
  const [isNFCSupported, setIsNFCSupported] = useState(false);
  const [hasNFCPermission, setHasNFCPermission] = useState(false);

  const checkNFCSupport = useCallback(() => {
    const supported = 'NDEFReader' in window;
    setIsNFCSupported(supported);
    return supported;
  }, []);

  const requestNFCPermission = useCallback(async () => {
    if (!isNFCSupported) return false;

    try {
      const permission = await navigator.permissions.query({ name: 'nfc' as PermissionName });
      const granted = permission.state === 'granted' || permission.state === 'prompt';
      setHasNFCPermission(granted);
      return granted;
    } catch (error) {
      console.log('NFC permission request failed:', error);
      return false;
    }
  }, [isNFCSupported]);

  const scanNFC = useCallback(async () => {
    if (!isNFCSupported || !hasNFCPermission) {
      throw new Error('NFC not supported or permission denied');
    }

    try {
      const ndef = new (window as any).NDEFReader();
      await ndef.scan();
      console.log('NFC scan started successfully');
      
      return new Promise((resolve, reject) => {
        ndef.addEventListener('reading', (event: any) => {
          console.log('NFC tag detected:', event);
          resolve(event);
        });
        
        ndef.addEventListener('readingerror', (error: any) => {
          console.log('NFC reading error:', error);
          reject(error);
        });
        
        // Timeout after 10 seconds
        setTimeout(() => {
          reject(new Error('NFC scan timeout'));
        }, 10000);
      });
    } catch (error) {
      console.log('NFC scan failed:', error);
      throw error;
    }
  }, [isNFCSupported, hasNFCPermission]);

  // Initialize NFC support check
  useState(() => {
    checkNFCSupport();
  });

  return {
    isNFCSupported,
    hasNFCPermission,
    requestNFCPermission,
    scanNFC
  };
}
