import { useEffect } from "react";
import { useNFC } from "../hooks/useNFC";

interface NFCDetectorProps {
  onNFCDetected: () => void;
}

export default function NFCDetector({ onNFCDetected }: NFCDetectorProps) {
  const { isNFCSupported, requestNFCPermission } = useNFC();

  useEffect(() => {
    if (isNFCSupported) {
      requestNFCPermission();
    }
  }, [isNFCSupported, requestNFCPermission]);

  useEffect(() => {
    // Register service worker for PWA
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.register('/sw.js')
        .then((registration) => {
          console.log('Service Worker registered:', registration);
        })
        .catch((error) => {
          console.log('Service Worker registration failed:', error);
        });
    }
  }, []);

  return null; // This component doesn't render anything
}
