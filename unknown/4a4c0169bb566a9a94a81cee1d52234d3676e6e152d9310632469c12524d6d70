# Squid Game Portfolio Website

## Overview

This is a Squid Game-themed portfolio website featuring NFC activation, immersive audio, modern animations, and a mobile-first design. The application showcases projects and provides contact functionality with a unique gaming aesthetic inspired by the Netflix series "Squid Game."

## System Architecture

### Frontend Architecture
- **Framework**: React 18 with TypeScript
- **Styling**: Tailwind CSS with custom Squid Game color scheme (vibrant pink #ff0055, mint green #00ffaa, stark white, void black)
- **UI Components**: Shadcn/UI component library with Radix UI primitives
- **Routing**: Wouter for lightweight client-side routing
- **State Management**: React Query (TanStack Query) for server state management
- **Form Handling**: React Hook Form with Zod validation
- **Animations**: Framer Motion for smooth transitions and effects

### Backend Architecture  
- **Runtime**: Node.js with Express.js
- **Language**: TypeScript with ES modules
- **API**: RESTful endpoints for contact form and player statistics
- **Database**: PostgreSQL with Drizzle ORM
- **Session Management**: Express sessions with PostgreSQL store
- **Build Tool**: Vite for development and production builds

### PWA Features
- **Service Worker**: Offline caching and PWA functionality
- **NFC Support**: Web NFC API integration for card tap activation
- **Web Audio API**: Immersive sound effects and background music
- **Manifest**: PWA manifest for mobile app-like experience

## Key Components

### Core Frontend Components
1. **SquidPortfolio** - Main portfolio component with sections:
   - Hero zone with animated logo and player badge
   - Interactive project showcase with 3D carousel
   - Contact form with game-themed validation
   - Social links as hexagonal buttons

2. **ParticleBackground** - Particles.js integration for dynamic background effects

3. **AudioManager** - Web Audio API wrapper for sound effects:
   - NFC tap detection sounds
   - Hover effects (marble rolling)
   - Success/failure feedback sounds

4. **NFCDetector** - NFC Web API integration for card tap activation

### Backend API Endpoints
- `POST /api/contact` - Contact form submission with validation
- `GET /api/player/:number` - Player statistics and game data

### Database Schema
- **Users table**: Basic user authentication structure
- Extensible schema for additional game-related data

## Data Flow

1. **NFC Activation**: User taps NFC card → Web NFC API detects → Particle animations trigger → Audio feedback plays
2. **Contact Form**: User submits form → React Hook Form validates → API processes → Success/error toast displayed
3. **Project Showcase**: Static project data → Interactive 3D carousel → Hover effects with sound feedback
4. **Audio System**: User interaction → Web Audio API → Context-aware sound effects

## External Dependencies

### Frontend Libraries
- **UI Framework**: React, Wouter routing
- **Styling**: Tailwind CSS, Radix UI components
- **Animations**: Framer Motion, Particles.js
- **Forms**: React Hook Form, Zod validation
- **Data Fetching**: TanStack React Query

### Backend Dependencies
- **Web Framework**: Express.js
- **Database**: PostgreSQL, Drizzle ORM
- **Session**: Connect-pg-simple for session storage
- **Build Tools**: Vite, ESBuild for production

### External Services
- **Fonts**: Google Fonts (Bebas Neue, Poppins)
- **Particles**: Particles.js CDN
- **Database**: Neon serverless PostgreSQL

## Deployment Strategy

### Development
- Vite dev server with HMR
- Express API server with TypeScript compilation
- Database migrations via Drizzle Kit

### Production Build
1. Frontend: Vite builds React app to `dist/public`
2. Backend: ESBuild bundles server to `dist/index.js`
3. Database: Drizzle migrations applied via `db:push`

### Environment Variables
- `DATABASE_URL`: PostgreSQL connection string
- `NODE_ENV`: Environment mode (development/production)

### Hosting Considerations
- Supports Replit deployment with cartographer integration
- PWA features require HTTPS in production
- NFC functionality requires secure context

## User Preferences

Preferred communication style: Simple, everyday language.

## Changelog

Changelog:
- July 02, 2025. Initial setup