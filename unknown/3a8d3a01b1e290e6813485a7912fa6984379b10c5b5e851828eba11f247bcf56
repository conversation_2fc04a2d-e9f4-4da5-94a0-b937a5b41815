import { useEffect } from "react";

declare global {
  interface Window {
    particlesJS: any;
  }
}

export default function ParticleBackground() {
  useEffect(() => {
    if (typeof window !== 'undefined' && window.particlesJS) {
      window.particlesJS('particles-js', {
        particles: {
          number: { value: 50 },
          color: { value: ['#ff0055', '#00ffaa', '#ffffff'] },
          shape: {
            type: ['circle', 'triangle', 'polygon'],
            polygon: { nb_sides: 6 }
          },
          opacity: { 
            value: 0.3,
            random: true
          },
          size: { 
            value: 3, 
            random: true,
            anim: {
              enable: true,
              speed: 2,
              size_min: 0.3
            }
          },
          move: {
            enable: true,
            speed: 1,
            direction: 'top',
            random: true,
            straight: false,
            out_mode: 'out',
            bounce: false
          }
        },
        interactivity: {
          detect_on: 'canvas',
          events: {
            onhover: { 
              enable: true, 
              mode: 'repulse' 
            },
            onclick: { 
              enable: true, 
              mode: 'push' 
            }
          },
          modes: {
            repulse: {
              distance: 100,
              duration: 0.4
            },
            push: {
              particles_nb: 4
            }
          }
        },
        retina_detect: true
      });
    }
  }, []);

  return (
    <div 
      id="particles-js" 
      className="fixed inset-0 z-0 pointer-events-none"
      style={{ background: 'transparent' }}
    />
  );
}
