import { useEffect } from "react";
import { useAudio } from "../hooks/useAudio";

export default function AudioManager() {
  const { initializeAudio } = useAudio();

  useEffect(() => {
    // Initialize audio context on first user interaction
    const handleFirstInteraction = () => {
      initializeAudio();
      document.removeEventListener('click', handleFirstInteraction);
      document.removeEventListener('keydown', handleFirstInteraction);
    };

    document.addEventListener('click', handleFirstInteraction);
    document.addEventListener('keydown', handleFirstInteraction);

    return () => {
      document.removeEventListener('click', handleFirstInteraction);
      document.removeEventListener('keydown', handleFirstInteraction);
    };
  }, [initializeAudio]);

  return null; // This component doesn't render anything
}
