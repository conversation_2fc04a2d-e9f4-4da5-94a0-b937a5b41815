import { useState, useCallback, useRef } from "react";

export function useAudio() {
  const [isInitialized, setIsInitialized] = useState(false);
  const audioContextRef = useRef<AudioContext | null>(null);
  const backgroundMusicRef = useRef<HTMLAudioElement | null>(null);

  const initializeAudio = useCallback(() => {
    if (isInitialized) return;

    try {
      // Initialize AudioContext
      audioContextRef.current = new (window.AudioContext || (window as any).webkitAudioContext)();
      
      // Create background music element (placeholder for now)
      backgroundMusicRef.current = new Audio();
      backgroundMusicRef.current.loop = true;
      backgroundMusicRef.current.volume = 0.3;
      
      setIsInitialized(true);
      console.log('Audio initialized');
    } catch (error) {
      console.log('Audio initialization failed:', error);
    }
  }, [isInitialized]);

  const playSound = useCallback((type: 'nfc' | 'hover' | 'success' | 'fail') => {
    if (!isInitialized || !audioContextRef.current) return;

    try {
      // Create different sounds using Web Audio API
      const ctx = audioContextRef.current;
      const oscillator = ctx.createOscillator();
      const gainNode = ctx.createGain();
      
      oscillator.connect(gainNode);
      gainNode.connect(ctx.destination);
      
      // Different frequencies for different sound types
      const frequencies = {
        nfc: 800,
        hover: 400,
        success: 600,
        fail: 200
      };
      
      oscillator.frequency.setValueAtTime(frequencies[type], ctx.currentTime);
      oscillator.type = 'sine';
      
      gainNode.gain.setValueAtTime(0.1, ctx.currentTime);
      gainNode.gain.exponentialRampToValueAtTime(0.01, ctx.currentTime + 0.3);
      
      oscillator.start(ctx.currentTime);
      oscillator.stop(ctx.currentTime + 0.3);
    } catch (error) {
      console.log('Sound play failed:', error);
    }
  }, [isInitialized]);

  const toggleBackgroundMusic = useCallback(() => {
    if (!backgroundMusicRef.current) return;

    if (backgroundMusicRef.current.paused) {
      backgroundMusicRef.current.play().catch(console.log);
    } else {
      backgroundMusicRef.current.pause();
    }
  }, []);

  return {
    isInitialized,
    initializeAudio,
    playSound,
    toggleBackgroundMusic
  };
}
