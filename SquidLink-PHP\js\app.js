// SquidLink PHP - Main JavaScript
// Exact match to React version functionality

class SquidLinkApp {
    constructor() {
        this.currentName = 'FAHIM';
        this.nameIndex = 0;
        this.names = ['FAHIM', 'HASAN', 'SANTO'];
        this.isRedirecting = false;
        this.redirectingTo = '';
        this.playerNumber = 456;
        this.audioContext = null;
        this.sounds = {};
        this.socialUrls = {
            'Facebook': 'https://www.facebook.com/fahim.hasan.santo.2024',
            'LinkedIn': 'https://www.linkedin.com/in/fahim-hasan-santo-583987267/',
            'Instagram': 'https://www.instagram.com/_fahimsanto/'
        };

        this.init();
    }

    init() {
        this.setupCursor();
        this.setupScrollProgress();
        this.setupParticles();
        this.setupNameCycling();
        this.setupProjectRedirects();
        this.setupSocialRedirects();
        this.setupNFC();
        this.setupAudio();
        this.updatePlayerNumber();
        this.setupHoverSounds();
        this.setupNavigationSymbols();
    }

    setupCursor() {
        const cursor = document.createElement('div');
        cursor.className = 'squid-cursor';
        document.body.appendChild(cursor);

        document.addEventListener('mousemove', (e) => {
            cursor.style.left = e.clientX + 'px';
            cursor.style.top = e.clientY + 'px';
        });

        // Cursor hover effects
        const hoverElements = document.querySelectorAll('a, button, .card-3d, .skill-card');
        hoverElements.forEach(el => {
            el.addEventListener('mouseenter', () => {
                cursor.style.transform = 'scale(1.5)';
                this.playSound('hover');
            });
            el.addEventListener('mouseleave', () => {
                cursor.style.transform = 'scale(1)';
            });
        });
    }

    setupNavigationSymbols() {
        const symbols = document.querySelectorAll('nav .text-squid-pink span');
        symbols.forEach(symbol => {
            symbol.addEventListener('click', () => {
                this.playSound('hover');
                symbol.style.animation = 'glitch 0.5s ease-in-out';
                setTimeout(() => {
                    symbol.style.animation = '';
                }, 500);
            });
        });
    }

    setupScrollProgress() {
        const progressBar = document.createElement('div');
        progressBar.className = 'scroll-progress';
        document.body.appendChild(progressBar);

        window.addEventListener('scroll', () => {
            const scrollTop = window.pageYOffset;
            const docHeight = document.body.scrollHeight - window.innerHeight;
            const scrollPercent = (scrollTop / docHeight) * 100;
            progressBar.style.width = scrollPercent + '%';
        });
    }

    setupParticles() {
        if (typeof particlesJS !== 'undefined') {
            particlesJS('particles-js', {
                particles: {
                    number: {
                        value: 120,
                        density: { enable: true, value_area: 1000 }
                    },
                    color: {
                        value: ['#ff0055', '#00ff88', '#ffffff', '#ff6b6b']
                    },
                    shape: {
                        type: ['circle', 'triangle', 'polygon'],
                        polygon: { nb_sides: 6 }
                    },
                    opacity: {
                        value: 0.6,
                        random: true,
                        anim: { enable: true, speed: 1.5, opacity_min: 0.2 }
                    },
                    size: {
                        value: 4,
                        random: true,
                        anim: { enable: true, speed: 3, size_min: 0.5 }
                    },
                    line_linked: {
                        enable: true,
                        distance: 200,
                        color: '#ff0055',
                        opacity: 0.3,
                        width: 1.5
                    },
                    move: {
                        enable: true,
                        speed: 4,
                        direction: 'none',
                        random: true,
                        straight: false,
                        out_mode: 'bounce',
                        bounce: true,
                        attract: { enable: true, rotateX: 600, rotateY: 1200 }
                    }
                },
                interactivity: {
                    detect_on: 'canvas',
                    events: {
                        onhover: {
                            enable: true,
                            mode: ['repulse', 'bubble']
                        },
                        onclick: {
                            enable: true,
                            mode: ['push', 'remove']
                        },
                        resize: true
                    },
                    modes: {
                        repulse: { distance: 120, duration: 0.4 },
                        bubble: { distance: 250, size: 8, duration: 2, opacity: 0.8 },
                        push: { particles_nb: 6 },
                        remove: { particles_nb: 3 }
                    }
                },
                retina_detect: true
            });
        }
    }

    setupNameCycling() {
        const nameElement = document.querySelector('.glitch-text');
        if (nameElement) {
            setInterval(() => {
                this.nameIndex = (this.nameIndex + 1) % this.names.length;
                this.currentName = this.names[this.nameIndex];
                nameElement.textContent = this.currentName;
                nameElement.setAttribute('data-text', this.currentName);
            }, 3000);
        }
    }

    setupProjectRedirects() {
        const projectCards = document.querySelectorAll('.project-card');
        const projects = [
            { name: 'Jashore Sell Bazar', url: 'https://jashoresellbazar.com' },
            { name: 'Athlete Bazaar', url: 'https://athletebazaar.com' },
            { name: 'Shadhin Alo', url: 'https://shadhinalo.com' }
        ];

        projectCards.forEach((card, index) => {
            card.addEventListener('click', () => {
                if (projects[index]) {
                    this.handleProjectClick(projects[index]);
                }
            });
        });
    }

    handleProjectClick(project) {
        if (this.isRedirecting) return;

        this.isRedirecting = true;
        this.redirectingTo = project.name;
        this.playSound('hover');

        this.showPremiumLoading(project.name);

        setTimeout(() => {
            window.open(project.url, '_blank');
            this.hidePremiumLoading();
            this.isRedirecting = false;
        }, 2000);
    }

    setupSocialRedirects() {
        const socialLinks = document.querySelectorAll('.social-link');
        const socials = [
            { name: 'Instagram', url: 'https://www.instagram.com/_fahimsanto/' },
            { name: 'LinkedIn', url: 'https://www.linkedin.com/fahim-hasan-santo-583987267/' },
            { name: 'Facebook', url: 'https://www.facebook.com/fahim.hasan.santo.2024' }
        ];

        socialLinks.forEach((link, index) => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                if (socials[index]) {
                    this.handleSocialClick(socials[index]);
                }
            });
        });
    }

    handleSocialClick(social) {
        if (this.isRedirecting) return;

        this.isRedirecting = true;
        this.redirectingTo = social.name;
        this.playSound('hover');

        this.showPremiumLoading(social.name);

        setTimeout(() => {
            window.open(social.url, '_blank');
            this.hidePremiumLoading();
            this.isRedirecting = false;
        }, 2000);
    }

    showPremiumLoading(destination) {
        const overlay = document.createElement('div');
        overlay.className = 'premium-loading-overlay';
        overlay.innerHTML = `
            <div class="loading-content">
                <div class="loading-logo">
                    <svg viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="50" cy="50" r="45" stroke="rgb(255, 0, 85)" stroke-width="4"/>
                        <polygon points="50,20 35,65 65,65" fill="rgb(255, 0, 85)"/>
                        <circle cx="50" cy="75" r="8" fill="rgb(255, 0, 85)"/>
                    </svg>
                </div>
                <div class="loading-text">REDIRECTING TO ${destination.toUpperCase()}</div>
                <div class="loading-subtitle">Player 456 Access Granted</div>
                <div class="loading-progress">
                    <div class="loading-progress-bar"></div>
                </div>
                <div class="loading-subtitle">⚠️ PREMIUM SQUID GAME EXPERIENCE ⚠️</div>
            </div>
        `;
        document.body.appendChild(overlay);
    }

    hidePremiumLoading() {
        const overlay = document.querySelector('.premium-loading-overlay');
        if (overlay) {
            overlay.remove();
        }
    }

    setupNFC() {
        if ('NDEFReader' in window) {
            const nfcButton = document.querySelector('.nfc-button');
            if (nfcButton) {
                nfcButton.addEventListener('click', () => {
                    this.triggerNFCAnimation();
                });
            }
        }
    }

    triggerNFCAnimation() {
        // Create NFC animation overlay
        const nfcOverlay = document.createElement('div');
        nfcOverlay.className = 'nfc-animation-overlay';
        nfcOverlay.innerHTML = `
            <div class="nfc-content">
                <div class="nfc-card">📱 NFC CARD DETECTED</div>
                <div class="nfc-player">PLAYER 456 VERIFIED</div>
                <div class="nfc-status">ACCESS GRANTED</div>
            </div>
        `;
        document.body.appendChild(nfcOverlay);

        setTimeout(() => {
            nfcOverlay.remove();
        }, 4000);
    }

    setupAudio() {
        try {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
        } catch (e) {
            console.log('Web Audio API not supported');
        }
    }

    playSound(type) {
        if (!this.audioContext) return;

        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(this.audioContext.destination);

        if (type === 'hover') {
            oscillator.frequency.setValueAtTime(800, this.audioContext.currentTime);
            oscillator.frequency.exponentialRampToValueAtTime(400, this.audioContext.currentTime + 0.1);
        }

        gainNode.gain.setValueAtTime(0.1, this.audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 0.1);

        oscillator.start(this.audioContext.currentTime);
        oscillator.stop(this.audioContext.currentTime + 0.1);
    }

    setupHoverSounds() {
        const hoverElements = document.querySelectorAll('a, button, .card-3d, .skill-card, .project-card');
        hoverElements.forEach(el => {
            el.addEventListener('mouseenter', () => {
                this.playSound('hover');
            });
        });
    }

    updatePlayerNumber() {
        const playerNumberElement = document.querySelector('.digital-display');
        if (playerNumberElement) {
            playerNumberElement.textContent = `PLAYER ${this.playerNumber}`;
        }
    }
}

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new SquidLinkApp();
});

// Service Worker Registration
if ('serviceWorker' in navigator) {
    window.addEventListener('load', () => {
        navigator.serviceWorker.register('/sw.js')
            .then(registration => console.log('SW registered'))
            .catch(registrationError => console.log('SW registration failed'));
    });
}