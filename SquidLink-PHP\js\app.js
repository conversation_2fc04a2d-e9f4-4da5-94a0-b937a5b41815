// SquidLink PHP - Main JavaScript
// Converted from React to Vanilla JS while maintaining exact functionality

class SquidLinkApp {
    constructor() {
        this.currentNameIndex = 0;
        this.names = ['FAHIM', 'HASAN', 'SANTO'];
        this.isNFCSupported = 'NDEFReader' in window;
        this.audioContext = null;
        this.sounds = {};
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.initParticles();
        this.initAudio();
        this.startNameCycling();
        this.initScrollProgress();
        this.initCustomCursor();
        this.initNFC();
        this.initSkillBars();
        this.initCards();
    }
    
    setupEventListeners() {
        // Contact form
        document.getElementById('contact-form').addEventListener('submit', this.handleContactSubmit.bind(this));
        
        // Project cards
        document.querySelectorAll('.project-card').forEach(card => {
            card.addEventListener('click', this.handleProjectClick.bind(this));
            card.addEventListener('mouseenter', () => this.playSound('hover'));
        });
        
        // Skill cards
        document.querySelectorAll('.skill-card').forEach(card => {
            card.addEventListener('mouseenter', () => this.playSound('hover'));
        });
        
        // Hover sounds
        document.querySelectorAll('.hover-sound').forEach(element => {
            element.addEventListener('mouseenter', () => this.playSound('hover'));
        });
        
        // NFC zone
        const nfcZone = document.getElementById('nfc-zone');
        if (nfcZone && this.isNFCSupported) {
            nfcZone.style.display = 'flex';
            nfcZone.addEventListener('click', this.handleNFCTap.bind(this));
        }
        
        // Dark mode toggle
        document.getElementById('dark-mode-toggle').addEventListener('click', this.toggleDarkMode.bind(this));
    }
    
    initParticles() {
        if (typeof particlesJS !== 'undefined') {
            particlesJS('particles-js', {
                particles: {
                    number: { value: 80, density: { enable: true, value_area: 800 } },
                    color: { value: '#ff0055' },
                    shape: { type: 'circle' },
                    opacity: { value: 0.5, random: false },
                    size: { value: 3, random: true },
                    line_linked: {
                        enable: true,
                        distance: 150,
                        color: '#00ffaa',
                        opacity: 0.4,
                        width: 1
                    },
                    move: {
                        enable: true,
                        speed: 6,
                        direction: 'none',
                        random: false,
                        straight: false,
                        out_mode: 'out',
                        bounce: false
                    }
                },
                interactivity: {
                    detect_on: 'canvas',
                    events: {
                        onhover: { enable: true, mode: 'repulse' },
                        onclick: { enable: true, mode: 'push' },
                        resize: true
                    }
                },
                retina_detect: true
            });
        }
    }
    
    initAudio() {
        try {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
            
            // Load sound files
            this.loadSound('hover', 'assets/sounds/hover.mp3');
            this.loadSound('nfc', 'assets/sounds/nfc.mp3');
            this.loadSound('fail', 'assets/sounds/fail.mp3');
        } catch (error) {
            console.log('Audio not supported:', error);
        }
    }
    
    async loadSound(name, url) {
        try {
            const response = await fetch(url);
            const arrayBuffer = await response.arrayBuffer();
            const audioBuffer = await this.audioContext.decodeAudioData(arrayBuffer);
            this.sounds[name] = audioBuffer;
        } catch (error) {
            console.log(`Failed to load sound ${name}:`, error);
        }
    }
    
    playSound(name) {
        if (this.audioContext && this.sounds[name]) {
            const source = this.audioContext.createBufferSource();
            source.buffer = this.sounds[name];
            source.connect(this.audioContext.destination);
            source.start();
        } else {
            // Fallback to HTML5 audio
            const audio = document.getElementById(`${name}-sound`);
            if (audio) {
                audio.currentTime = 0;
                audio.play().catch(() => {});
            }
        }
    }
    
    startNameCycling() {
        const nameElement = document.getElementById('name-text');
        const glitchElement = document.getElementById('glitch-name');
        
        setInterval(() => {
            this.currentNameIndex = (this.currentNameIndex + 1) % this.names.length;
            const newName = this.names[this.currentNameIndex];
            
            nameElement.textContent = newName;
            glitchElement.setAttribute('data-text', newName);
        }, 3000);
    }
    
    initScrollProgress() {
        const progressBar = document.getElementById('scroll-progress');
        
        window.addEventListener('scroll', () => {
            const scrolled = (window.scrollY / (document.documentElement.scrollHeight - window.innerHeight)) * 100;
            progressBar.style.width = `${scrolled}%`;
        });
    }
    
    initCustomCursor() {
        const cursor = document.getElementById('cursor');
        
        document.addEventListener('mousemove', (e) => {
            cursor.style.left = `${e.clientX - 10}px`;
            cursor.style.top = `${e.clientY - 10}px`;
        });
        
        document.addEventListener('mousedown', () => {
            cursor.style.transform = 'scale(0.8)';
        });
        
        document.addEventListener('mouseup', () => {
            cursor.style.transform = 'scale(1)';
        });
    }
    
    initNFC() {
        if (this.isNFCSupported) {
            this.requestNFCPermission();
        }
    }
    
    async requestNFCPermission() {
        try {
            const ndef = new NDEFReader();
            await ndef.scan();
            
            ndef.addEventListener('reading', (event) => {
                console.log('NFC tag detected:', event);
                this.handleNFCTap();
            });
            
            ndef.addEventListener('readingerror', (error) => {
                console.log('NFC reading error:', error);
            });
        } catch (error) {
            console.log('NFC permission denied or not supported:', error);
        }
    }
    
    handleNFCTap() {
        this.playSound('nfc');
        this.showNFCAnimation();
    }
    
    showNFCAnimation() {
        const overlay = document.getElementById('nfc-overlay');
        overlay.classList.remove('hidden');
        
        setTimeout(() => {
            overlay.classList.add('hidden');
        }, 4000);
    }
    
    initSkillBars() {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const skillBar = entry.target.querySelector('.skill-bar');
                    if (skillBar) {
                        const width = skillBar.getAttribute('data-width');
                        setTimeout(() => {
                            skillBar.style.width = width;
                        }, 500);
                    }
                }
            });
        });
        
        document.querySelectorAll('.skill-card').forEach(card => {
            observer.observe(card);
        });
    }
    
    initCards() {
        // Add entrance animations
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        });
        
        document.querySelectorAll('.card-3d').forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(50px)';
            card.style.transition = `opacity 0.8s ease ${index * 0.2}s, transform 0.8s ease ${index * 0.2}s`;
            observer.observe(card);
        });
    }
    
    handleProjectClick(event) {
        const card = event.currentTarget;
        const url = card.getAttribute('data-url');
        const name = card.getAttribute('data-name');
        
        this.playSound('hover');
        this.showLoadingAnimation(name, url);
    }
    
    showLoadingAnimation(projectName, url) {
        const overlay = document.getElementById('loading-overlay');
        const loadingText = document.getElementById('loading-text');
        const progressBar = document.getElementById('loading-progress');
        
        loadingText.textContent = `Loading ${projectName}...`;
        overlay.classList.remove('hidden');
        
        let progress = 0;
        const interval = setInterval(() => {
            progress += Math.random() * 15;
            if (progress > 100) progress = 100;
            
            progressBar.style.width = `${progress}%`;
            
            if (progress >= 100) {
                clearInterval(interval);
                setTimeout(() => {
                    window.open(url, '_blank');
                    overlay.classList.add('hidden');
                    progressBar.style.width = '0%';
                }, 500);
            }
        }, 100);
    }
    
    async handleContactSubmit(event) {
        event.preventDefault();
        
        const form = event.target;
        const formData = new FormData(form);
        const successDiv = document.getElementById('contact-success');
        const errorDiv = document.getElementById('contact-error');
        
        // Hide previous messages
        successDiv.classList.add('hidden');
        errorDiv.classList.add('hidden');
        
        // Clear previous errors
        document.querySelectorAll('.error-message').forEach(error => {
            error.classList.add('hidden');
        });
        
        try {
            const response = await fetch('?action=contact', {
                method: 'POST',
                body: formData
            });
            
            const result = await response.json();
            
            if (result.success) {
                successDiv.classList.remove('hidden');
                form.reset();
                this.playSound('hover');
            } else {
                if (result.errors) {
                    Object.keys(result.errors).forEach(field => {
                        const input = form.querySelector(`[name="${field}"]`);
                        if (input) {
                            const errorDiv = input.parentNode.querySelector('.error-message');
                            if (errorDiv) {
                                errorDiv.textContent = result.errors[field];
                                errorDiv.classList.remove('hidden');
                            }
                        }
                    });
                } else {
                    errorDiv.classList.remove('hidden');
                }
                this.playSound('fail');
            }
        } catch (error) {
            console.error('Contact form error:', error);
            errorDiv.classList.remove('hidden');
            this.playSound('fail');
        }
    }
    
    toggleDarkMode() {
        // This is already a dark theme, so we'll just add a fun effect
        document.body.style.filter = document.body.style.filter === 'invert(1)' ? 'invert(0)' : 'invert(1)';
        this.playSound('hover');
    }
}

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new SquidLinkApp();
});

// Service Worker Registration for PWA
if ('serviceWorker' in navigator) {
    window.addEventListener('load', () => {
        navigator.serviceWorker.register('/sw.js')
            .then((registration) => {
                console.log('Service Worker registered:', registration);
            })
            .catch((error) => {
                console.log('Service Worker registration failed:', error);
            });
    });
}
