import { useState, useEffect, useRef } from "react";
import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Form, FormControl, FormField, FormItem, FormMessage } from "@/components/ui/form";
import { useToast } from "@/hooks/use-toast";
import ParticleBackground from "./ParticleBackground";
import AudioManager from "./AudioManager";
import NFCDetector from "./NFCDetector";
import { useAudio } from "../hooks/useAudio";
import { useNFC } from "../hooks/useNFC";

const contactSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  email: z.string().email("Invalid email address"),
  message: z.string().min(10, "Message must be at least 10 characters"),
});

type ContactForm = z.infer<typeof contactSchema>;

const projects = [
  {
    id: 1,
    name: "JASHORESELLBAZAR",
    description: "E-COMMERCE PLATFORM",
    playerNumber: "618",
    status: "active",
    details: "Full-stack e-commerce solution with modern UI/UX and payment integration.",
    url: "https://jashoresellbazar.com",
    color: "from-pink-600 to-red-600"
  },
  {
    id: 2,
    name: "ATHLETEBAZAAR",
    description: "SPORTS MARKETPLACE",
    playerNumber: "322",
    status: "active",
    details: "Sports equipment marketplace with real-time inventory and athlete profiles.",
    url: "https://athletebazaar.com",
    color: "from-green-400 to-blue-400"
  },
  {
    id: 3,
    name: "SHADHINALO",
    description: "ELIMINATED",
    playerNumber: "198",
    status: "eliminated",
    details: "This project has been eliminated from the current games.",
    url: "#",
    color: "from-red-600 to-black"
  }
];

const socialLinks = [
  { name: "Facebook", icon: "📘", url: "https://facebook.com/fahim" },
  { name: "Instagram", icon: "📷", url: "https://instagram.com/fahim" },
  { name: "LinkedIn", icon: "💼", url: "https://linkedin.com/in/fahim" },
];

export default function SquidPortfolio() {
  const [playerNumber, setPlayerNumber] = useState("456");
  const [darkMode, setDarkMode] = useState(false);
  const [scrollProgress, setScrollProgress] = useState(0);
  const [isContactFailed, setIsContactFailed] = useState(false);
  const cursorRef = useRef<HTMLDivElement>(null);
  const { toast } = useToast();
  const { playSound, toggleBackgroundMusic } = useAudio();
  const { isNFCSupported, scanNFC } = useNFC();

  const form = useForm<ContactForm>({
    resolver: zodResolver(contactSchema),
    defaultValues: {
      name: "",
      email: "",
      message: "",
    },
  });

  // Custom cursor
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (cursorRef.current) {
        cursorRef.current.style.left = `${e.clientX}px`;
        cursorRef.current.style.top = `${e.clientY}px`;
      }
    };

    document.addEventListener("mousemove", handleMouseMove);
    return () => document.removeEventListener("mousemove", handleMouseMove);
  }, []);

  // Scroll progress
  useEffect(() => {
    const handleScroll = () => {
      const scrolled = (window.scrollY / (document.documentElement.scrollHeight - window.innerHeight)) * 100;
      setScrollProgress(scrolled);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // Generate random player number on load
  useEffect(() => {
    const generatePlayerNumber = () => {
      return Math.floor(Math.random() * 456) + 1;
    };
    setPlayerNumber(generatePlayerNumber().toString().padStart(3, "0"));
  }, []);

  // Dark mode toggle
  const toggleDarkMode = () => {
    setDarkMode(!darkMode);
    document.documentElement.classList.toggle("dark");
  };

  // Handle NFC tap
  const handleNFCTap = async () => {
    try {
      await scanNFC();
      playSound("nfc");
      toast({
        title: "NFC Detected",
        description: "Welcome to Player 456's portfolio!",
      });
    } catch (error) {
      console.log("NFC scan failed:", error);
    }
  };

  // Handle contact form submission
  const onSubmit = async (data: ContactForm) => {
    try {
      // Simulate form validation failure for demonstration
      if (data.name.toLowerCase().includes("test")) {
        setIsContactFailed(true);
        playSound("fail");
        form.setError("name", { message: "Invalid name detected!" });
        return;
      }

      // In a real app, this would submit to your backend
      console.log("Contact form submitted:", data);
      playSound("success");
      setIsContactFailed(false);
      toast({
        title: "Message Sent!",
        description: "Your message has been successfully transmitted.",
      });
      form.reset();
    } catch (error) {
      setIsContactFailed(true);
      playSound("fail");
      toast({
        title: "Transmission Failed",
        description: "Please try again later.",
        variant: "destructive",
      });
    }
  };

  const handleHover = () => {
    playSound("hover");
  };

  return (
    <div className="min-h-screen bg-black text-white relative overflow-x-hidden">
      {/* Custom Cursor */}
      <div ref={cursorRef} className="squid-cursor" />

      {/* Scroll Progress */}
      <div 
        className="scroll-progress" 
        style={{ width: `${scrollProgress}%` }}
      />

      {/* Background Components */}
      <ParticleBackground />
      <AudioManager />
      <NFCDetector onNFCDetected={handleNFCTap} />

      {/* Navigation */}
      <nav className="fixed top-0 left-0 right-0 z-50 p-4">
        <div className="flex justify-between items-center max-w-7xl mx-auto">
          <div className="flex space-x-4 text-squid-pink text-2xl font-bold">
            <motion.span 
              className="hover:animate-glitch cursor-pointer"
              whileHover={{ scale: 1.1 }}
              onClick={handleHover}
            >
              ○
            </motion.span>
            <motion.span 
              className="hover:animate-glitch cursor-pointer"
              whileHover={{ scale: 1.1 }}
              onClick={handleHover}
            >
              △
            </motion.span>
            <motion.span 
              className="hover:animate-glitch cursor-pointer"
              whileHover={{ scale: 1.1 }}
              onClick={handleHover}
            >
              □
            </motion.span>
          </div>
          
          <Button
            onClick={toggleDarkMode}
            className="honeycomb bg-squid-pink hover:bg-squid-green w-12 h-12 text-white font-bold transition-all duration-300 hover:animate-liquid"
          >
            {darkMode ? "☀️" : "🌙"}
          </Button>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="min-h-screen flex flex-col justify-center items-center relative z-10 px-4">
        {/* Player Number */}
        <div className="absolute top-20 right-4 bg-squid-pink text-white px-4 py-2 rounded-lg font-bold text-xl animate-number-flip">
          PLAYER <span>{playerNumber}</span>
        </div>

        {/* Squid Shape */}
        <motion.div 
          className="squid-shape mb-8"
          animate={{ rotate: [45, 50, 45] }}
          transition={{ duration: 3, repeat: Infinity }}
        />

        {/* Main Name */}
        <div className="text-center mb-8">
          <motion.h1 
            className="font-bold text-6xl md:text-8xl lg:text-9xl text-squid-pink mb-4 hover:animate-glitch transition-all duration-300"
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1 }}
          >
            FAHIM
          </motion.h1>
          <motion.div 
            className="bg-squid-green text-black px-6 py-3 rounded-lg font-bold text-xl md:text-2xl inline-block hover:animate-pulse-glow"
            initial={{ opacity: 0, scale: 0 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.5, duration: 0.8 }}
          >
            PLAYER 456
          </motion.div>
          <motion.p 
            className="text-squid-green font-medium text-lg mt-4 animate-float"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1, duration: 1 }}
          >
            WEB DEVELOPER
          </motion.p>
        </div>

        {/* NFC Detection Zone */}
        {isNFCSupported && (
          <motion.div 
            className="nfc-pulse bg-squid-red rounded-full w-16 h-16 flex items-center justify-center mb-8 cursor-pointer hover:animate-bounce"
            onClick={handleNFCTap}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
          >
            <div className="w-8 h-8 bg-white rounded-full" />
          </motion.div>
        )}

        {/* Social Links */}
        <div className="flex space-x-6 mb-12">
          {socialLinks.map((social, index) => (
            <motion.a
              key={social.name}
              href={social.url}
              target="_blank"
              rel="noopener noreferrer"
              className="honeycomb bg-squid-pink hover:bg-squid-green w-16 h-16 flex items-center justify-center text-white text-2xl transition-all duration-300 hover:animate-liquid cursor-pointer"
              onMouseEnter={handleHover}
              whileHover={{ scale: 1.1 }}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1.2 + index * 0.1 }}
            >
              {social.icon}
            </motion.a>
          ))}
        </div>

        {/* Scroll Indicator */}
        <motion.div 
          className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 2 }}
        >
          <div className="w-6 h-10 border-2 border-squid-green rounded-full flex justify-center">
            <div className="w-1 h-3 bg-squid-green rounded-full mt-2 animate-pulse" />
          </div>
        </motion.div>
      </section>

      {/* Projects Section */}
      <section className="min-h-screen py-20 px-4 relative z-10">
        <div className="max-w-7xl mx-auto">
          <motion.h2 
            className="font-bold text-4xl md:text-6xl text-squid-green text-center mb-16 hover:animate-glitch"
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 1 }}
          >
            MY PROJECTS
          </motion.h2>

          {/* Main Project Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
            {projects.map((project, index) => (
              <motion.div
                key={project.id}
                className="card-3d h-80 relative cursor-pointer"
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.2, duration: 0.8 }}
                onMouseEnter={handleHover}
              >
                {/* Front Face */}
                <Card className={`card-face bg-gradient-to-br ${project.color} rounded-xl border-0`}>
                  <CardContent className="p-6 flex flex-col justify-center items-center text-center h-full relative">
                    <div className="absolute top-4 left-4 text-squid-green font-bold text-2xl">
                      PLAYER<br />{project.playerNumber}
                    </div>
                    <h3 className="font-bold text-3xl text-white mb-4">
                      {project.name}
                    </h3>
                    <p className="text-white font-medium">{project.description}</p>
                    <div className={`mt-4 w-full h-2 rounded-full ${
                      project.status === 'active' ? 'bg-squid-green' : 'bg-squid-red opacity-50'
                    }`} />
                  </CardContent>
                </Card>

                {/* Back Face */}
                <Card className={`card-face card-back bg-gradient-to-br ${
                  project.status === 'active' 
                    ? 'from-squid-green to-blue-400 text-black' 
                    : 'from-black to-gray-900 text-white border-squid-red border-2'
                } rounded-xl`}>
                  <CardContent className="p-6 flex flex-col justify-center items-center text-center h-full">
                    <h3 className="font-bold text-2xl mb-4">
                      {project.status === 'active' ? 'PROJECT DETAILS' : 'PROJECT ELIMINATED'}
                    </h3>
                    <p className="font-medium text-sm mb-4">
                      {project.details}
                    </p>
                    <Button
                      className={`${
                        project.status === 'active'
                          ? 'bg-black text-white hover:animate-pulse'
                          : 'bg-squid-red text-white opacity-50 cursor-not-allowed'
                      } px-4 py-2 rounded-lg font-bold`}
                      onClick={() => project.status === 'active' && window.open(project.url, '_blank')}
                      disabled={project.status !== 'active'}
                    >
                      {project.status === 'active' ? 'VIEW PROJECT' : 'ELIMINATED'}
                    </Button>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>

          {/* Additional Project Cards */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {[
              { number: "528", status: "active" },
              { number: "105", status: "active" },
              { number: "067", status: "eliminated" },
              { number: "194", status: "eliminated" },
            ].map((item, index) => (
              <motion.div
                key={item.number}
                className={`bg-black border-2 ${
                  item.status === 'active' ? 'border-squid-green' : 'border-squid-red'
                } rounded-lg p-4 text-center hover:animate-float ${
                  item.status === 'eliminated' ? 'opacity-60' : ''
                }`}
                initial={{ opacity: 0, scale: 0 }}
                whileInView={{ opacity: item.status === 'eliminated' ? 0.6 : 1, scale: 1 }}
                transition={{ delay: index * 0.1, duration: 0.5 }}
                onMouseEnter={handleHover}
              >
                <div className={`${
                  item.status === 'active' ? 'text-squid-green' : 'text-squid-red'
                } font-bold text-lg`}>
                  PLAYER<br />{item.number}
                </div>
                {item.status === 'eliminated' && (
                  <div className="text-squid-red font-medium text-xs mt-1">
                    ELIMINATED
                  </div>
                )}
                <div className={`mt-2 w-full h-1 ${
                  item.status === 'active' ? 'bg-squid-green' : 'bg-squid-red'
                } rounded-full`} />
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="min-h-screen py-20 px-4 relative z-10">
        <div className="max-w-7xl mx-auto">
          <motion.h2
            className="font-bold text-4xl md:text-6xl text-squid-green text-center mb-16 hover:animate-glitch"
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 1 }}
          >
            CONTACT
          </motion.h2>

          <div className="flex flex-col lg:flex-row items-start justify-center space-y-8 lg:space-y-0 lg:space-x-12 relative">
            {/* Left Guard */}
            <motion.div
              className="guard-figure animate-guard-sway"
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 1 }}
            >
              <div className="guard-body">
                <div className="guard-mask-triangular" />
              </div>
            </motion.div>

            {/* Contact Form - Centered */}
            <motion.div
              className="bg-gradient-to-br from-gray-900 to-black border-2 border-squid-pink rounded-2xl p-6 md:p-8 w-full max-w-md mx-auto shadow-2xl backdrop-blur-sm relative overflow-hidden"
              initial={{ opacity: 0, y: 50, scale: 0.9 }}
              whileInView={{ opacity: 1, y: 0, scale: 1 }}
              transition={{ duration: 1, delay: 0.2, type: "spring", bounce: 0.2 }}
            >
              {/* Form background glow */}
              <div className="absolute inset-0 bg-gradient-to-br from-squid-pink/10 to-squid-green/10 rounded-2xl" />

              <div className="relative z-10">
                <div className="text-center mb-6">
                  <motion.div
                    className={`${
                      isContactFailed ? 'bg-gradient-to-r from-squid-red to-red-600' : 'bg-gradient-to-r from-squid-green to-green-500'
                    } text-white px-6 py-3 rounded-xl font-bold text-lg md:text-xl inline-block shadow-lg transform transition-all duration-300 hover:scale-105`}
                    animate={isContactFailed ? {
                      boxShadow: ['0 0 20px rgba(255,0,85,0.5)', '0 0 40px rgba(255,0,85,0.8)', '0 0 20px rgba(255,0,85,0.5)']
                    } : {}}
                    transition={{ duration: 1, repeat: isContactFailed ? Infinity : 0 }}
                  >
                    {isContactFailed ? '❌ FAILED' : '✅ ACTIVE'}
                  </motion.div>
                </div>

                <Form {...form}>
                  <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 md:space-y-6">
                    <FormField
                      control={form.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <Input
                              placeholder="👤 Name"
                              className={`w-full bg-black/50 border-2 ${
                                form.formState.errors.name ? 'border-squid-red animate-shake' : 'border-squid-green/50 focus:border-squid-pink'
                              } rounded-xl px-4 py-3 text-white font-medium focus:outline-none transition-all duration-300 backdrop-blur-sm placeholder:text-gray-400`}
                              {...field}
                            />
                          </FormControl>
                          <FormMessage className="text-squid-red text-sm" />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="email"
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <Input
                              type="email"
                              placeholder="📧 Email"
                              className={`w-full bg-black/50 border-2 ${
                                form.formState.errors.email ? 'border-squid-red animate-shake' : 'border-squid-green/50 focus:border-squid-pink'
                              } rounded-xl px-4 py-3 text-white font-medium focus:outline-none transition-all duration-300 backdrop-blur-sm placeholder:text-gray-400`}
                              {...field}
                            />
                          </FormControl>
                          <FormMessage className="text-squid-red text-sm" />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="message"
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <Textarea
                              placeholder="💬 Message"
                              rows={4}
                              className={`w-full bg-black/50 border-2 ${
                                form.formState.errors.message ? 'border-squid-red animate-shake' : 'border-squid-green/50 focus:border-squid-pink'
                              } rounded-xl px-4 py-3 text-white font-medium focus:outline-none transition-all duration-300 resize-none backdrop-blur-sm placeholder:text-gray-400`}
                              {...field}
                            />
                          </FormControl>
                          <FormMessage className="text-squid-red text-sm" />
                        </FormItem>
                      )}
                    />

                    <Button
                      type="submit"
                      className="w-full bg-gradient-to-r from-squid-pink to-pink-600 hover:from-squid-green hover:to-green-500 text-white font-bold text-lg md:text-xl py-3 md:py-4 rounded-xl transition-all duration-300 transform hover:scale-105 hover:shadow-2xl disabled:opacity-50 disabled:cursor-not-allowed"
                      disabled={form.formState.isSubmitting}
                    >
                      {form.formState.isSubmitting ? '📤 SENDING...' : '🚀 SEND MESSAGE'}
                    </Button>
                  </form>
                </Form>
              </div>
            </motion.div>

            {/* Right Guard */}
            <motion.div
              className="guard-figure animate-guard-sway"
              style={{ animationDelay: '1s' }}
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 1, delay: 0.4 }}
            >
              <div className="guard-body">
                <div className="guard-mask-round" />
              </div>
            </motion.div>
          </div>


        </div>
      </section>

      {/* Footer */}
      <footer className="py-12 px-4 bg-black border-t-2 border-squid-pink relative z-10">
        <div className="max-w-7xl mx-auto text-center">
          <motion.div 
            className="flex justify-center items-center space-x-4 mb-8"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 1 }}
          >
            {/* Geometric shapes */}
            <div className="w-8 h-8 bg-squid-pink rounded-full" />
            <div className="w-8 h-8 bg-white" />
          </motion.div>
          <motion.p 
            className="text-squid-green font-medium text-lg mb-2"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ delay: 0.2, duration: 1 }}
          >
            © 2025 Fahim ~ Player 456. All games completed successfully.
          </motion.p>
          <motion.p 
            className="text-gray-400 text-sm"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ delay: 0.4, duration: 1 }}
          >
            Designed with Squid Game aesthetics for maximum impact.
          </motion.p>
        </div>
      </footer>
    </div>
  );
}
