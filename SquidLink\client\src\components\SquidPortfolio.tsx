import { useState, useEffect, useRef } from "react";
import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Form, FormControl, FormField, FormItem, FormMessage } from "@/components/ui/form";
import { useToast } from "@/hooks/use-toast";
import ParticleBackground from "./ParticleBackground";
import AudioManager from "./AudioManager";
import NFCDetector from "./NFCDetector";
import { useAudio } from "../hooks/useAudio";
import { useNFC } from "../hooks/useNFC";

const contactSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  email: z.string().email("Invalid email address"),
  message: z.string().min(10, "Message must be at least 10 characters"),
});

type ContactForm = z.infer<typeof contactSchema>;

const projects = [
  {
    id: 1,
    name: "JASHORESELLBAZAR",
    description: "E-COMMERCE PLATFORM",
    playerNumber: "618",
    status: "active",
    details: "Full-stack e-commerce solution with modern UI/UX and payment integration.",
    url: "https://jashoresellbazar.com/",
    color: "from-pink-600 to-red-600"
  },
  {
    id: 2,
    name: "ATHLETEBAZAAR",
    description: "SPORTS MARKETPLACE",
    playerNumber: "322",
    status: "active",
    details: "Sports equipment marketplace with real-time inventory and athlete profiles.",
    url: "https://athletebazaar.com/",
    color: "from-green-400 to-blue-400"
  },
  {
    id: 3,
    name: "SHADHINALO",
    description: "DIGITAL PLATFORM",
    playerNumber: "198",
    status: "active",
    details: "Digital content platform with user-generated content and community features.",
    url: "https://shadhinalo.com/",
    color: "from-purple-600 to-pink-600"
  }
];

const playerSkills = [
  {
    id: 1,
    name: "HTML & CSS",
    description: "FRONTEND FOUNDATION",
    playerNumber: "001",
    level: "MASTER",
    status: "active",
    specialties: ["Semantic HTML", "CSS Grid", "Flexbox", "Responsive Design"],
    color: "from-orange-500 to-red-600"
  },
  {
    id: 2,
    name: "JAVASCRIPT",
    description: "DYNAMIC PROGRAMMING",
    playerNumber: "002",
    level: "MASTER",
    status: "active",
    specialties: ["ES6+", "DOM Manipulation", "APIs", "Async/Await"],
    color: "from-yellow-500 to-orange-600"
  },
  {
    id: 3,
    name: "WORDPRESS",
    description: "CMS SPECIALIST",
    playerNumber: "003",
    level: "MASTER",
    status: "active",
    specialties: ["Custom Themes", "Plugins", "WooCommerce", "SEO"],
    color: "from-blue-500 to-purple-600"
  },
  {
    id: 4,
    name: "FULL STACK",
    description: "COMPLETE SOLUTION",
    playerNumber: "456",
    level: "MASTER",
    status: "active",
    specialties: ["Frontend", "Backend", "Database", "Deployment"],
    color: "from-squid-pink to-squid-green"
  },
  {
    id: 5,
    name: "LANGUAGES",
    description: "COMMUNICATION",
    playerNumber: "999",
    level: "FLUENT",
    status: "active",
    specialties: ["English", "Bangla", "Technical Writing", "Documentation"],
    color: "from-purple-500 to-pink-600"
  }
];

const socialLinks = [
  { name: "Facebook", icon: "📘", url: "https://www.facebook.com/fahim.hasan.santo.2024" },
  { name: "Instagram", icon: "📷", url: "https://www.instagram.com/_fahimsanto/" },
  { name: "LinkedIn", icon: "💼", url: "https://www.linkedin.com/in/fahim-hasan-santo-583987267/" },
];

export default function SquidPortfolio() {
  const [darkMode, setDarkMode] = useState(false);
  const [scrollProgress, setScrollProgress] = useState(0);
  const [isContactFailed, setIsContactFailed] = useState(false);
  const [isRedirecting, setIsRedirecting] = useState(false);
  const [redirectingTo, setRedirectingTo] = useState("");
  const cursorRef = useRef<HTMLDivElement>(null);
  const { toast } = useToast();
  const { playSound, toggleBackgroundMusic } = useAudio();
  const { isNFCSupported, scanNFC } = useNFC();

  const form = useForm<ContactForm>({
    resolver: zodResolver(contactSchema),
    defaultValues: {
      name: "",
      email: "",
      message: "",
    },
  });

  // Custom cursor
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (cursorRef.current) {
        cursorRef.current.style.left = `${e.clientX}px`;
        cursorRef.current.style.top = `${e.clientY}px`;
      }
    };

    document.addEventListener("mousemove", handleMouseMove);
    return () => document.removeEventListener("mousemove", handleMouseMove);
  }, []);

  // Scroll progress
  useEffect(() => {
    const handleScroll = () => {
      const scrolled = (window.scrollY / (document.documentElement.scrollHeight - window.innerHeight)) * 100;
      setScrollProgress(scrolled);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // Player number is fixed at 456

  // Handle project redirect with Squid Game animation
  const handleProjectClick = (project: typeof projects[0]) => {
    setIsRedirecting(true);
    setRedirectingTo(project.name);
    playSound('hover');

    // Show loading animation for 2 seconds then redirect
    setTimeout(() => {
      window.open(project.url, '_blank');
      setIsRedirecting(false);
      setRedirectingTo("");
    }, 2000);
  };

  // Handle social media redirect with Squid Game animation
  const handleSocialClick = (social: typeof socialLinks[0], event: React.MouseEvent) => {
    event.preventDefault();
    console.log('Social click:', social.name); // Debug log
    setIsRedirecting(true);
    setRedirectingTo(`${social.name.toUpperCase()}`);
    playSound('hover');

    // Show loading animation for 2 seconds then redirect
    setTimeout(() => {
      window.open(social.url, '_blank');
      setIsRedirecting(false);
      setRedirectingTo("");
    }, 2000);
  };

  // Dark mode toggle
  const toggleDarkMode = () => {
    setDarkMode(!darkMode);
    document.documentElement.classList.toggle("dark");
  };

  // Handle NFC tap
  const handleNFCTap = async () => {
    try {
      await scanNFC();
      playSound("nfc");
      toast({
        title: "NFC Detected",
        description: "Welcome to Player 456's portfolio!",
      });
    } catch (error) {
      console.log("NFC scan failed:", error);
    }
  };

  // Handle contact form submission
  const onSubmit = async (data: ContactForm) => {
    try {
      // Simulate form validation failure for demonstration
      if (data.name.toLowerCase().includes("test")) {
        setIsContactFailed(true);
        playSound("fail");
        form.setError("name", { message: "Invalid name detected!" });
        return;
      }

      // In a real app, this would submit to your backend
      console.log("Contact form submitted:", data);
      playSound("success");
      setIsContactFailed(false);
      toast({
        title: "Message Sent!",
        description: "Your message has been successfully transmitted.",
      });
      form.reset();
    } catch (error) {
      setIsContactFailed(true);
      playSound("fail");
      toast({
        title: "Transmission Failed",
        description: "Please try again later.",
        variant: "destructive",
      });
    }
  };

  const handleHover = () => {
    playSound("hover");
  };

  return (
    <div className="min-h-screen bg-black text-white relative overflow-x-hidden">
      {/* Custom Cursor */}
      <div ref={cursorRef} className="squid-cursor" />

      {/* Scroll Progress */}
      <div 
        className="scroll-progress" 
        style={{ width: `${scrollProgress}%` }}
      />

      {/* Background Components */}
      <ParticleBackground />
      <AudioManager />
      <NFCDetector onNFCDetected={handleNFCTap} />

      {/* Navigation */}
      <nav className="fixed top-0 left-0 right-0 z-50 p-4 bg-black/50">
        <div className="flex justify-between items-center max-w-7xl mx-auto">
          <div className="flex space-x-4 text-squid-pink text-2xl font-bold">
            <motion.span
              className="hover:animate-glitch cursor-pointer"
              whileHover={{ scale: 1.1 }}
              onClick={handleHover}
            >
              ○
            </motion.span>
            <motion.span
              className="hover:animate-glitch cursor-pointer"
              whileHover={{ scale: 1.1 }}
              onClick={handleHover}
            >
              △
            </motion.span>
            <motion.span
              className="hover:animate-glitch cursor-pointer"
              whileHover={{ scale: 1.1 }}
              onClick={handleHover}
            >
              □
            </motion.span>
          </div>

          {/* Digital Player Number Display */}
          <motion.div
            className="absolute left-1/2 transform -translate-x-1/2 bg-gradient-to-b from-blue-400 to-blue-600 p-1 rounded-lg shadow-lg border-2 border-blue-300"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            style={{ minWidth: '100px' }}
          >
            <div className="bg-black px-4 py-2 rounded-md border border-gray-700 relative overflow-hidden">
              {/* Digital grid background */}
              <div className="absolute inset-0 opacity-10">
                <div className="grid grid-cols-8 grid-rows-4 h-full w-full">
                  {Array.from({ length: 32 }).map((_, i) => (
                    <div key={i} className="border border-gray-600" style={{ borderWidth: '0.5px' }} />
                  ))}
                </div>
              </div>
              <div className="font-mono text-2xl font-bold text-red-500 tracking-wider digital-display text-center relative z-10">
                456
              </div>
            </div>
          </motion.div>

          <Button
            onClick={toggleDarkMode}
            className="bg-squid-pink hover:bg-squid-green w-12 h-12 text-white font-bold transition-all duration-300 hover:animate-liquid rounded-lg"
          >
            {darkMode ? "☀️" : "🌙"}
          </Button>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="min-h-screen flex flex-col justify-center items-center relative z-10 px-4">
        {/* Squid Shape */}
        <motion.div 
          className="squid-shape mb-8"
          animate={{ rotate: [45, 50, 45] }}
          transition={{ duration: 3, repeat: Infinity }}
        />

        {/* Main Name */}
        <div className="text-center mb-8">
          <motion.h1
            className="font-bold text-6xl md:text-8xl lg:text-9xl text-white mb-4 glitch-text relative"
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1 }}
            data-text="FAHIM"
          >
            FAHIM
          </motion.h1>
          <motion.div
            className="bg-squid-green text-black px-6 py-3 rounded-lg font-bold text-xl md:text-2xl inline-block hover:animate-pulse-glow mb-8"
            initial={{ opacity: 0, scale: 0 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.5, duration: 0.8 }}
          >
            PLAYER 456
          </motion.div>

          {/* Social Icons */}
          <motion.div
            className="flex justify-center space-x-6 mt-8"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 1, duration: 0.8 }}
          >
            <motion.a
              href="https://www.facebook.com/fahim.hasan.santo.2024"
              target="_blank"
              rel="noopener noreferrer"
              className="w-12 h-12 bg-squid-pink rounded-full flex items-center justify-center text-white text-xl hover:bg-squid-green transition-colors duration-300"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.95 }}
            >
              <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
              </svg>
            </motion.a>

            <motion.a
              href="https://www.linkedin.com/in/fahim-hasan-santo-583987267/"
              target="_blank"
              rel="noopener noreferrer"
              className="w-12 h-12 bg-squid-pink rounded-full flex items-center justify-center text-white text-xl hover:bg-squid-green transition-colors duration-300"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.95 }}
            >
              <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
              </svg>
            </motion.a>

            <motion.a
              href="https://www.instagram.com/_fahimsanto/"
              target="_blank"
              rel="noopener noreferrer"
              className="w-12 h-12 bg-squid-pink rounded-full flex items-center justify-center text-white text-xl hover:bg-squid-green transition-colors duration-300"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.95 }}
            >
              <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
              </svg>
            </motion.a>
          </motion.div>
          <motion.p 
            className="text-squid-green font-medium text-lg mt-4 animate-float"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1, duration: 1 }}
          >
            WEB DEVELOPER
          </motion.p>
        </div>

        {/* NFC Detection Zone */}
        {isNFCSupported && (
          <motion.div 
            className="nfc-pulse bg-squid-red rounded-full w-16 h-16 flex items-center justify-center mb-8 cursor-pointer hover:animate-bounce"
            onClick={handleNFCTap}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
          >
            <div className="w-8 h-8 bg-white rounded-full" />
          </motion.div>
        )}

        {/* Social Links */}
        <div className="flex space-x-6 mb-12">
          {socialLinks.map((social, index) => (
            <motion.a
              key={social.name}
              href={social.url}
              target="_blank"
              rel="noopener noreferrer"
              className="bg-squid-pink hover:bg-squid-green w-16 h-16 flex items-center justify-center text-white text-2xl transition-all duration-300 hover:animate-liquid cursor-pointer rounded-lg"
              onMouseEnter={handleHover}
              onClick={(e) => handleSocialClick(social, e)}
              whileHover={{ scale: 1.1 }}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1.2 + index * 0.1 }}
            >
              {social.icon}
            </motion.a>
          ))}
        </div>

        {/* Scroll Indicator */}
        <motion.div 
          className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 2 }}
        >
          <div className="w-6 h-10 border-2 border-squid-green rounded-full flex justify-center">
            <div className="w-1 h-3 bg-squid-green rounded-full mt-2 animate-pulse" />
          </div>
        </motion.div>
      </section>

      {/* Projects Section */}
      <section className="min-h-screen py-20 px-4 relative z-10">
        <div className="max-w-7xl mx-auto">
          <motion.h2 
            className="font-bold text-4xl md:text-6xl text-squid-green text-center mb-16 hover:animate-glitch"
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 1 }}
          >
            MY PROJECTS
          </motion.h2>

          {/* Main Project Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
            {projects.map((project, index) => (
              <motion.div
                key={project.id}
                className="card-3d h-80 relative cursor-pointer"
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.2, duration: 0.8 }}
                onMouseEnter={handleHover}
                onClick={() => handleProjectClick(project)}
              >
                {/* Front Face */}
                <Card className={`card-face bg-gradient-to-br ${project.color} rounded-xl border-0`}>
                  <CardContent className="p-6 flex flex-col justify-center items-center text-center h-full relative">
                    <div className="absolute top-4 left-4 text-squid-green font-bold text-2xl">
                      PLAYER<br />{project.playerNumber}
                    </div>
                    <h3 className="font-bold text-3xl text-white mb-4">
                      {project.name}
                    </h3>
                    <p className="text-white font-medium">{project.description}</p>
                    <div className={`mt-4 w-full h-2 rounded-full ${
                      project.status === 'active' ? 'bg-squid-green' : 'bg-squid-red opacity-50'
                    }`} />
                  </CardContent>
                </Card>

                {/* Back Face */}
                <Card className={`card-face card-back bg-gradient-to-br ${
                  project.status === 'active' 
                    ? 'from-squid-green to-blue-400 text-black' 
                    : 'from-black to-gray-900 text-white border-squid-red border-2'
                } rounded-xl`}>
                  <CardContent className="p-6 flex flex-col justify-center items-center text-center h-full">
                    <h3 className="font-bold text-2xl mb-4">
                      {project.status === 'active' ? 'PROJECT DETAILS' : 'PROJECT ELIMINATED'}
                    </h3>
                    <p className="font-medium text-sm mb-4">
                      {project.details}
                    </p>
                    <Button
                      className={`${
                        project.status === 'active'
                          ? 'bg-black text-white hover:animate-pulse'
                          : 'bg-squid-red text-white opacity-50 cursor-not-allowed'
                      } px-4 py-2 rounded-lg font-bold`}
                      onClick={() => project.status === 'active' && window.open(project.url, '_blank')}
                      disabled={project.status !== 'active'}
                    >
                      {project.status === 'active' ? 'VIEW PROJECT' : 'ELIMINATED'}
                    </Button>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>

          {/* Additional Project Cards */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {[
              { number: "528", status: "active" },
              { number: "105", status: "active" },
              { number: "067", status: "eliminated" },
              { number: "194", status: "eliminated" },
            ].map((item, index) => (
              <motion.div
                key={item.number}
                className={`bg-black border-2 ${
                  item.status === 'active' ? 'border-squid-green' : 'border-squid-red'
                } rounded-lg p-4 text-center hover:animate-float ${
                  item.status === 'eliminated' ? 'opacity-60' : ''
                }`}
                initial={{ opacity: 0, scale: 0 }}
                whileInView={{ opacity: item.status === 'eliminated' ? 0.6 : 1, scale: 1 }}
                transition={{ delay: index * 0.1, duration: 0.5 }}
                onMouseEnter={handleHover}
              >
                <div className={`${
                  item.status === 'active' ? 'text-squid-green' : 'text-squid-red'
                } font-bold text-lg`}>
                  PLAYER<br />{item.number}
                </div>
                {item.status === 'eliminated' && (
                  <div className="text-squid-red font-medium text-xs mt-1">
                    ELIMINATED
                  </div>
                )}
                <div className={`mt-2 w-full h-1 ${
                  item.status === 'active' ? 'bg-squid-green' : 'bg-squid-red'
                } rounded-full`} />
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Player Skills Section */}
      <section className="min-h-screen py-20 px-4 relative z-10">
        <div className="max-w-7xl mx-auto">
          <motion.h2
            className="font-bold text-4xl md:text-6xl text-squid-green text-center mb-8 hover:animate-glitch"
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 1 }}
          >
            PLAYER SKILLS
          </motion.h2>

          <motion.p
            className="text-white text-center text-xl mb-16"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, delay: 0.2 }}
          >
            Master of Full Stack Web Development
          </motion.p>

          {/* Skills Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
            {playerSkills.map((skill, index) => (
              <motion.div
                key={skill.id}
                className="card-3d h-80 relative cursor-pointer"
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.2, duration: 0.8 }}
                onMouseEnter={handleHover}
              >
                {/* Front Face */}
                <Card className={`card-face bg-gradient-to-br ${skill.color} rounded-xl border-0`}>
                  <CardContent className="p-6 flex flex-col justify-center items-center text-center h-full relative">
                    <div className="absolute top-4 left-4 text-white font-bold text-xs">
                      PLAYER<br />{skill.playerNumber}
                    </div>
                    <div className="absolute top-4 right-4 text-white font-bold text-xs">
                      {skill.level}
                    </div>
                    <h3 className="font-bold text-2xl text-white mb-2">
                      {skill.name}
                    </h3>
                    <p className="text-white/80 font-medium mb-4">{skill.description}</p>
                    <div className="flex flex-wrap gap-1 justify-center">
                      {skill.specialties.map((specialty, i) => (
                        <span key={i} className="bg-white/20 text-white text-xs px-2 py-1 rounded">
                          {specialty}
                        </span>
                      ))}
                    </div>
                    <div className={`mt-4 w-full h-2 rounded-full ${
                      skill.status === 'active' ? 'bg-white/30' : 'bg-squid-red opacity-50'
                    }`} />
                  </CardContent>
                </Card>

                {/* Back Face */}
                <Card className={`card-face card-back bg-gradient-to-br from-squid-green to-blue-400 text-black rounded-xl`}>
                  <CardContent className="p-6 flex flex-col justify-center items-center text-center h-full">
                    <h3 className="font-bold text-2xl mb-4">
                      SKILL MASTERY
                    </h3>
                    <div className="w-full space-y-3">
                      {skill.specialties.map((specialty, i) => (
                        <div key={i} className="flex justify-between items-center">
                          <span className="text-sm font-medium">{specialty}</span>
                          <div className="w-16 h-2 bg-black/20 rounded-full">
                            <div
                              className="h-full bg-black rounded-full transition-all duration-1000"
                              style={{ width: `${85 + (i * 3)}%` }}
                            />
                          </div>
                        </div>
                      ))}
                    </div>
                    <div className="mt-4 text-center">
                      <span className="bg-black text-white px-3 py-1 rounded-full text-sm font-bold">
                        {skill.level}
                      </span>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>

          {/* Call to Action */}
          <motion.div
            className="bg-gradient-to-r from-squid-pink to-squid-green rounded-2xl p-8 text-center"
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8 }}
          >
            <h3 className="text-white font-bold text-3xl mb-4">
              READY FOR YOUR PROJECT?
            </h3>
            <p className="text-white/90 text-lg mb-6">
              Full Stack Web Developer | HTML, CSS, JS, WordPress Master | English & Bangla
            </p>
            <div className="flex justify-center space-x-4">
              <motion.div
                className="bg-white text-squid-pink px-6 py-3 rounded-lg font-bold cursor-pointer"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onMouseEnter={handleHover}
              >
                HIRE ME
              </motion.div>
              <motion.div
                className="bg-black text-white px-6 py-3 rounded-lg font-bold cursor-pointer"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onMouseEnter={handleHover}
              >
                VIEW WORK
              </motion.div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="min-h-screen py-20 px-4 relative z-10">
        <div className="max-w-7xl mx-auto">
          <motion.h2
            className="font-bold text-4xl md:text-6xl text-squid-green text-center mb-16 hover:animate-glitch"
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 1 }}
          >
            CONTACT
          </motion.h2>

          {/* Mobile Guards - Side by side at the top */}
          <div className="lg:hidden flex justify-center items-center gap-8 mb-8">
            <motion.div
              className="guard-figure-mobile animate-guard-sway"
              initial={{ opacity: 0, x: -30, y: -20 }}
              whileInView={{ opacity: 1, x: 0, y: 0 }}
              transition={{ duration: 1 }}
            >
              <div className="guard-body-mobile">
                <div className="guard-mask-triangular-mobile">
                  <div className="guard-eyes-mobile">
                    <div className="guard-eye-left-mobile" />
                    <div className="guard-eye-right-mobile" />
                  </div>
                </div>
              </div>
            </motion.div>

            <motion.div
              className="guard-figure-mobile animate-guard-sway"
              style={{ animationDelay: '1s' }}
              initial={{ opacity: 0, x: 30, y: -20 }}
              whileInView={{ opacity: 1, x: 0, y: 0 }}
              transition={{ duration: 1, delay: 0.4 }}
            >
              <div className="guard-body-mobile">
                <div className="guard-mask-round-mobile">
                  <div className="guard-eyes-mobile">
                    <div className="guard-eye-left-mobile" />
                    <div className="guard-eye-right-mobile" />
                  </div>
                </div>
              </div>
            </motion.div>
          </div>

          <div className="flex flex-col lg:flex-row items-start justify-center space-y-8 lg:space-y-0 lg:space-x-12 relative">
            {/* Left Guard - Desktop */}
            <motion.div
              className="hidden lg:block guard-figure animate-guard-sway"
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 1 }}
            >
              <div className="guard-body">
                <div className="guard-mask-triangular">
                  <div className="guard-eyes">
                    <div className="guard-eye-left" />
                    <div className="guard-eye-right" />
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Contact Form - Centered */}
            <motion.div
              className="bg-gray-800 border-2 border-squid-pink rounded-lg p-6 md:p-8 w-full max-w-md mx-auto shadow-2xl relative"
              initial={{ opacity: 0, y: 50, scale: 0.9 }}
              whileInView={{ opacity: 1, y: 0, scale: 1 }}
              transition={{ duration: 1, delay: 0.2, type: "spring", bounce: 0.2 }}
            >
              <div className="text-center mb-6">
                <div className="text-squid-red text-3xl font-bold mb-2">
                  FAILED
                </div>
                <div className="text-gray-400 text-sm">
                  Complete the form to continue the game
                </div>
              </div>

              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                  <div>
                    <label className="block text-squid-green text-sm font-medium mb-2">Name</label>
                    <FormField
                      control={form.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <Input
                              placeholder="Enter your player name"
                              className={`w-full bg-gray-900 border ${
                                form.formState.errors.name ? 'border-squid-red animate-shake' : 'border-gray-600'
                              } rounded px-4 py-3 text-white placeholder:text-gray-500 focus:outline-none focus:border-squid-green transition-colors`}
                              {...field}
                            />
                          </FormControl>
                          <FormMessage className="text-squid-red text-sm mt-1" />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div>
                    <label className="block text-squid-green text-sm font-medium mb-2">Email</label>
                    <FormField
                      control={form.control}
                      name="email"
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <Input
                              type="email"
                              placeholder="Enter your email address"
                              className={`w-full bg-gray-900 border ${
                                form.formState.errors.email ? 'border-squid-red animate-shake' : 'border-gray-600'
                              } rounded px-4 py-3 text-white placeholder:text-gray-500 focus:outline-none focus:border-squid-green transition-colors`}
                              {...field}
                            />
                          </FormControl>
                          <FormMessage className="text-squid-red text-sm mt-1" />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div>
                    <label className="block text-squid-green text-sm font-medium mb-2">Message</label>
                    <FormField
                      control={form.control}
                      name="message"
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <Textarea
                              placeholder="Tell me about your project..."
                              rows={4}
                              className={`w-full bg-gray-900 border ${
                                form.formState.errors.message ? 'border-squid-red animate-shake' : 'border-gray-600'
                              } rounded px-4 py-3 text-white placeholder:text-gray-500 focus:outline-none focus:border-squid-green transition-colors resize-none`}
                              {...field}
                            />
                          </FormControl>
                          <FormMessage className="text-squid-red text-sm mt-1" />
                        </FormItem>
                      )}
                    />
                  </div>

                  <Button
                    type="submit"
                    className="w-full bg-squid-pink hover:bg-squid-green text-white font-bold text-sm py-3 rounded transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    disabled={form.formState.isSubmitting}
                  >
                    {form.formState.isSubmitting ? 'SENDING...' : 'SUBMIT GAME REQUEST'}
                  </Button>
                </form>
              </Form>
            </motion.div>

            {/* Right Guard - Desktop */}
            <motion.div
              className="hidden lg:block guard-figure animate-guard-sway"
              style={{ animationDelay: '1s' }}
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 1, delay: 0.4 }}
            >
              <div className="guard-body">
                <div className="guard-mask-round">
                  <div className="guard-eyes">
                    <div className="guard-eye-left" />
                    <div className="guard-eye-right" />
                  </div>
                </div>
              </div>
            </motion.div>
          </div>


        </div>
      </section>

      {/* Footer */}
      <footer className="py-12 px-4 bg-black border-t-2 border-squid-pink relative z-10">
        <div className="max-w-7xl mx-auto text-center">
          <motion.div 
            className="flex justify-center items-center space-x-4 mb-8"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 1 }}
          >
            {/* Geometric shapes */}
            <div className="w-8 h-8 bg-squid-pink rounded-full" />
            <div className="w-8 h-8 bg-white" />
            <div className="w-8 h-8 bg-squid-green" style={{ clipPath: 'polygon(50% 0%, 0% 100%, 100% 100%)' }} />
          </motion.div>
          <motion.p 
            className="text-squid-green font-medium text-lg mb-2"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ delay: 0.2, duration: 1 }}
          >
            © 2025 Fahim ~ Player 456. All games completed successfully.
          </motion.p>
          <motion.p 
            className="text-gray-400 text-sm"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ delay: 0.4, duration: 1 }}
          >
            Designed with Squid Game aesthetics for maximum impact.
          </motion.p>
        </div>
      </footer>

      {/* Premium Squid Game Loading Animation */}
      {isRedirecting && (
        <motion.div
          className="fixed inset-0 z-50 bg-black flex items-center justify-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
        >
          {/* Background Pattern */}
          <div className="absolute inset-0 opacity-10">
            <div className="grid grid-cols-8 grid-rows-8 h-full w-full">
              {Array.from({ length: 64 }).map((_, i) => (
                <motion.div
                  key={i}
                  className="border border-squid-pink"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: [0, 1, 0] }}
                  transition={{
                    duration: 2,
                    delay: i * 0.02,
                    repeat: Infinity,
                    repeatType: "loop"
                  }}
                />
              ))}
            </div>
          </div>

          {/* Central Loading Content */}
          <div className="text-center z-10">
            {/* Squid Game Logo Animation */}
            <motion.div
              className="mb-8"
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.5, type: "spring" }}
            >
              <div className="relative">
                {/* Main Circle */}
                <motion.div
                  className="w-32 h-32 border-4 border-squid-pink rounded-full mx-auto mb-4"
                  animate={{ rotate: 360 }}
                  transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                />

                {/* Inner Shapes */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <motion.div
                    className="flex space-x-2"
                    animate={{ scale: [1, 1.2, 1] }}
                    transition={{ duration: 1, repeat: Infinity }}
                  >
                    <div className="w-4 h-4 bg-squid-pink rounded-full" />
                    <div className="w-4 h-4 bg-white" />
                    <div className="w-4 h-4 bg-squid-green" style={{ clipPath: 'polygon(50% 0%, 0% 100%, 100% 100%)' }} />
                  </motion.div>
                </div>
              </div>
            </motion.div>

            {/* Loading Text */}
            <motion.h2
              className="text-4xl font-bold text-squid-green mb-4 font-mono"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
            >
              REDIRECTING TO
            </motion.h2>

            <motion.h3
              className="text-2xl font-bold text-squid-pink mb-8 tracking-wider"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
            >
              {redirectingTo}
            </motion.h3>

            {/* Progress Bar */}
            <div className="w-64 h-2 bg-gray-800 rounded-full mx-auto mb-4">
              <motion.div
                className="h-full bg-gradient-to-r from-squid-pink to-squid-green rounded-full"
                initial={{ width: 0 }}
                animate={{ width: "100%" }}
                transition={{ duration: 2, ease: "easeInOut" }}
              />
            </div>

            {/* Loading Dots */}
            <div className="flex justify-center space-x-2">
              {[0, 1, 2].map((i) => (
                <motion.div
                  key={i}
                  className="w-3 h-3 bg-squid-green rounded-full"
                  animate={{
                    scale: [1, 1.5, 1],
                    opacity: [0.5, 1, 0.5]
                  }}
                  transition={{
                    duration: 1,
                    repeat: Infinity,
                    delay: i * 0.2
                  }}
                />
              ))}
            </div>

            {/* Warning Text */}
            <motion.p
              className="text-yellow-400 text-sm mt-6 font-mono"
              initial={{ opacity: 0 }}
              animate={{ opacity: [0, 1, 0] }}
              transition={{ duration: 1, repeat: Infinity, delay: 1 }}
            >
              ⚠️ GAME IN PROGRESS - DO NOT CLOSE WINDOW ⚠️
            </motion.p>
          </div>

          {/* Corner Guards */}
          <div className="absolute top-4 left-4">
            <motion.div
              className="w-12 h-12 bg-squid-red rounded-full flex items-center justify-center"
              animate={{ scale: [1, 1.1, 1] }}
              transition={{ duration: 2, repeat: Infinity }}
            >
              <div className="w-6 h-6 bg-black rounded-full" />
            </motion.div>
          </div>

          <div className="absolute top-4 right-4">
            <motion.div
              className="w-12 h-12 bg-squid-red rounded-full flex items-center justify-center"
              animate={{ scale: [1, 1.1, 1] }}
              transition={{ duration: 2, repeat: Infinity, delay: 0.5 }}
            >
              <div className="w-6 h-6 bg-black rounded-full" />
            </motion.div>
          </div>
        </motion.div>
      )}
    </div>
  );
}
