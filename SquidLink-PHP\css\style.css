/* EXACT COPY FROM REACT VERSION - SquidLink CSS */
:root {
  --background: hsl(0, 0%, 0%);
  --foreground: hsl(0, 0%, 100%);
  --muted: hsl(0, 0%, 10%);
  --muted-foreground: hsl(0, 0%, 70%);
  --popover: hsl(0, 0%, 5%);
  --popover-foreground: hsl(0, 0%, 95%);
  --card: hsl(0, 0%, 5%);
  --card-foreground: hsl(0, 0%, 95%);
  --border: hsl(340, 100%, 35%);
  --input: hsl(0, 0%, 10%);
  --primary: hsl(340, 100%, 35%);
  --primary-foreground: hsl(0, 0%, 100%);
  --secondary: hsl(150, 100%, 35%);
  --secondary-foreground: hsl(0, 0%, 0%);
  --accent: hsl(150, 100%, 35%);
  --accent-foreground: hsl(0, 0%, 0%);
  --destructive: hsl(0, 84%, 60%);
  --destructive-foreground: hsl(0, 100%, 100%);
  --ring: hsl(340, 100%, 35%);
  --radius: 0.5rem;
  
  /* Squid Game Colors */
  --squid-pink: rgb(255, 0, 85);
  --squid-green: hsl(150, 100%, 35%);
  --squid-red: hsl(348, 83%, 47%);
  --electric-blue: hsl(195, 100%, 50%);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  border-color: var(--border);
}

body {
  font-family: 'Poppins', sans-serif;
  background: var(--background);
  color: var(--foreground);
  cursor: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

h1, h2, h3, h4, h5, h6 {
  font-family: 'Bebas Neue', cursive;
}

/* EXACT COMPONENT STYLES FROM REACT */
.squid-cursor {
  position: fixed;
  width: 20px;
  height: 20px;
  background: var(--squid-pink);
  border-radius: 50%;
  pointer-events: none;
  z-index: 9999;
  mix-blend-mode: difference;
  transition: transform 0.1s ease;
}

.scroll-progress {
  position: fixed;
  top: 0;
  left: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--squid-pink), var(--squid-green));
  z-index: 1000;
  transition: width 0.1s ease;
}

.card-3d {
  transform-style: preserve-3d;
  transition: transform 0.6s;
}

.card-3d:hover {
  transform: rotateY(180deg);
}

.card-face {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
}

.card-back {
  transform: rotateY(180deg);
}

.nfc-pulse {
  animation: pulse-glow 1s ease-in-out infinite;
}

.guard-figure {
  width: 8rem;
  height: 12rem;
  position: relative;
}

.guard-body {
  width: 100%;
  height: 100%;
  background: rgb(255, 0, 85);
  border-radius: 4rem 4rem 0rem 0rem !important;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.guard-mask-triangular {
  position: absolute;
  top: 4rem;
  left: 50%;
  transform: translateX(-50%);
  width: 4rem;
  height: 4rem;
  background: black;
  border-radius: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.guard-mask-triangular::before {
  content: '';
  width: 1.5rem;
  height: 1.5rem;
  background: white;
  clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
  border: 2px solid white;
}

.guard-mask-round {
  position: absolute;
  top: 4rem;
  left: 50%;
  transform: translateX(-50%);
  width: 4rem;
  height: 4rem;
  background: black;
  border-radius: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.guard-mask-round::before {
  content: '';
  width: 1.5rem;
  height: 1.5rem;
  background: white;
  border-radius: 50%;
}

.guard-eyes {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  gap: 0.3rem;
  z-index: 10;
}

.guard-eye-left, .guard-eye-right {
  width: 0.4rem;
  height: 0.4rem;
  background: radial-gradient(circle, rgb(255, 0, 85), #cc0044);
  border-radius: 50%;
  animation: red-light-bloom 2s ease-in-out infinite;
  box-shadow:
    0 0 8px rgb(255, 0, 85),
    0 0 16px rgb(255, 0, 85),
    0 0 24px rgb(255, 0, 85);
}

.squid-shape {
  width: 4rem;
  height: 4rem;
  background: var(--squid-pink);
  border-radius: 50% 50% 0 50%;
  position: relative;
  transform: rotate(45deg);
}

.squid-shape::before {
  content: '';
  position: absolute;
  width: 1rem;
  height: 2rem;
  background: var(--squid-pink);
  border-radius: 50%;
  top: -0.5rem;
  left: 50%;
  transform: translateX(-50%) rotate(-45deg);
}

.squid-shape::after {
  content: '';
  position: absolute;
  width: 0.5rem;
  height: 0.5rem;
  background: white;
  border-radius: 50%;
  top: 1rem;
  left: 1rem;
  transform: rotate(-45deg);
}

/* EXACT KEYFRAME ANIMATIONS FROM REACT */
@keyframes glitch {
  0% { transform: translate(0) }
  20% { transform: translate(-2px, 2px) }
  40% { transform: translate(-2px, -2px) }
  60% { transform: translate(2px, 2px) }
  80% { transform: translate(2px, -2px) }
  100% { transform: translate(0) }
}

@keyframes pulse-glow {
  0% { box-shadow: 0 0 5px var(--squid-pink), 0 0 10px var(--squid-pink), 0 0 15px var(--squid-pink); }
  100% { box-shadow: 0 0 10px var(--squid-pink), 0 0 20px var(--squid-pink), 0 0 30px var(--squid-pink); }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes liquid {
  0% { border-radius: 50%; transform: scale(1); }
  50% { border-radius: 60% 40% 30% 70%; transform: scale(1.1); }
  100% { border-radius: 50%; transform: scale(1); }
}

@keyframes guard-sway {
  0%, 100% { transform: rotate(-1deg); }
  50% { transform: rotate(1deg); }
}

@keyframes number-flip {
  0% { transform: rotateY(0deg); }
  50% { transform: rotateY(90deg); }
  100% { transform: rotateY(0deg); }
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

.animate-glitch {
  animation: glitch 0.3s ease-in-out infinite alternate;
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite alternate;
}

/* EXACT GLITCH TEXT FROM REACT */
.glitch-text {
  position: relative;
  animation: glitch-main 3s infinite;
  text-shadow:
    0 0 10px rgba(255, 255, 255, 0.8),
    0 0 20px rgba(255, 0, 85, 0.6),
    0 0 30px rgba(0, 255, 136, 0.4);
}

.glitch-text::before,
.glitch-text::after {
  content: attr(data-text);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.glitch-text::before {
  animation: glitch-1 0.3s infinite;
  color: rgb(255, 0, 85);
  z-index: -1;
  text-shadow: 2px 0 0 rgb(255, 0, 85);
}

.glitch-text::after {
  animation: glitch-2 0.3s infinite;
  color: #00ff88;
  z-index: -2;
  text-shadow: -2px 0 0 #00ff88;
}

/* EXACT GLITCH KEYFRAMES FROM REACT */
@keyframes glitch-main {
  0%, 90%, 100% {
    transform: translate(0);
    filter: hue-rotate(0deg);
  }
  10% {
    transform: translate(-5px, 2px);
    filter: hue-rotate(90deg);
  }
  20% {
    transform: translate(-2px, -5px);
    filter: hue-rotate(180deg);
  }
  30% {
    transform: translate(5px, 2px);
    filter: hue-rotate(270deg);
  }
  40% {
    transform: translate(2px, -2px);
    filter: hue-rotate(360deg);
  }
  50% {
    transform: translate(-3px, 3px) scale(1.01);
    filter: hue-rotate(45deg);
  }
  60% {
    transform: translate(3px, -3px) scale(0.99);
    filter: hue-rotate(135deg);
  }
  70% {
    transform: translate(-2px, -2px);
    filter: hue-rotate(225deg);
  }
  80% {
    transform: translate(4px, 1px);
    filter: hue-rotate(315deg);
  }
}

@keyframes glitch-1 {
  0%, 100% {
    transform: translate(0);
    clip-path: polygon(0 0, 100% 0, 100% 8%, 0 8%);
    opacity: 0.8;
  }
  10% {
    transform: translate(-4px, 3px);
    clip-path: polygon(0 12%, 100% 12%, 100% 22%, 0 22%);
    opacity: 0.9;
  }
  20% {
    transform: translate(-3px, 2px);
    clip-path: polygon(0 25%, 100% 25%, 100% 35%, 0 35%);
    opacity: 0.7;
  }
  30% {
    transform: translate(4px, -2px);
    clip-path: polygon(0 38%, 100% 38%, 100% 48%, 0 48%);
    opacity: 0.8;
  }
  40% {
    transform: translate(-2px, -3px);
    clip-path: polygon(0 51%, 100% 51%, 100% 61%, 0 61%);
    opacity: 0.9;
  }
  50% {
    transform: translate(3px, 4px);
    clip-path: polygon(0 64%, 100% 64%, 100% 74%, 0 74%);
    opacity: 0.6;
  }
  60% {
    transform: translate(2px, 2px);
    clip-path: polygon(0 77%, 100% 77%, 100% 87%, 0 87%);
    opacity: 0.8;
  }
  70% {
    transform: translate(-4px, -1px);
    clip-path: polygon(0 90%, 100% 90%, 100% 100%, 0 100%);
    opacity: 0.7;
  }
  80% {
    transform: translate(2px, -4px);
    clip-path: polygon(0 5%, 100% 5%, 100% 15%, 0 15%);
    opacity: 0.9;
  }
  90% {
    transform: translate(-1px, 3px);
    clip-path: polygon(0 30%, 100% 30%, 100% 40%, 0 40%);
    opacity: 0.8;
  }
}

@keyframes glitch-2 {
  0%, 100% {
    transform: translate(0);
    clip-path: polygon(0 5%, 100% 5%, 100% 15%, 0 15%);
    opacity: 0.7;
  }
  15% {
    transform: translate(3px, -4px);
    clip-path: polygon(0 18%, 100% 18%, 100% 28%, 0 28%);
    opacity: 0.8;
  }
  25% {
    transform: translate(2px, -2px);
    clip-path: polygon(0 31%, 100% 31%, 100% 41%, 0 41%);
    opacity: 0.6;
  }
  35% {
    transform: translate(-3px, 3px);
    clip-path: polygon(0 44%, 100% 44%, 100% 54%, 0 54%);
    opacity: 0.9;
  }
  45% {
    transform: translate(4px, 2px);
    clip-path: polygon(0 57%, 100% 57%, 100% 67%, 0 67%);
    opacity: 0.7;
  }
  55% {
    transform: translate(-2px, -3px);
    clip-path: polygon(0 70%, 100% 70%, 100% 80%, 0 80%);
    opacity: 0.8;
  }
  65% {
    transform: translate(-4px, -2px);
    clip-path: polygon(0 83%, 100% 83%, 100% 93%, 0 93%);
    opacity: 0.6;
  }
  75% {
    transform: translate(1px, 4px);
    clip-path: polygon(0 2%, 100% 2%, 100% 12%, 0 12%);
    opacity: 0.9;
  }
  85% {
    transform: translate(-2px, 2px);
    clip-path: polygon(0 96%, 100% 96%, 100% 100%, 0 100%);
    opacity: 0.7;
  }
  95% {
    transform: translate(3px, -1px);
    clip-path: polygon(0 25%, 100% 25%, 100% 35%, 0 35%);
    opacity: 0.8;
  }
}

@keyframes red-light-bloom {
  0%, 100% {
    box-shadow:
      0 0 8px rgb(255, 0, 85),
      0 0 16px rgb(255, 0, 85),
      0 0 24px rgb(255, 0, 85);
    opacity: 0.8;
  }
  50% {
    box-shadow:
      0 0 12px rgb(255, 0, 85),
      0 0 24px rgb(255, 0, 85),
      0 0 36px rgb(255, 0, 85);
    opacity: 1;
  }
}

/* ESSENTIAL UTILITY CLASSES */
.container { max-width: 1200px; margin: 0 auto; padding: 0 1rem; }
.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.text-center { text-align: center; }
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }
.inset-0 { top: 0; right: 0; bottom: 0; left: 0; }
.z-0 { z-index: 0; }
.z-10 { z-index: 10; }
.z-50 { z-index: 50; }
.pointer-events-none { pointer-events: none; }
.p-4 { padding: 1rem; }
.p-6 { padding: 1.5rem; }
.p-8 { padding: 2rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
.py-4 { padding-top: 1rem; padding-bottom: 1rem; }
.py-8 { padding-top: 2rem; padding-bottom: 2rem; }
.py-16 { padding-top: 4rem; padding-bottom: 4rem; }
.py-20 { padding-top: 5rem; padding-bottom: 5rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-6 { margin-bottom: 1.5rem; }
.mb-8 { margin-bottom: 2rem; }
.mb-12 { margin-bottom: 3rem; }
.mt-4 { margin-top: 1rem; }
.mt-8 { margin-top: 2rem; }
.space-x-4 > * + * { margin-left: 1rem; }
.space-x-6 > * + * { margin-left: 1.5rem; }
.space-y-4 > * + * { margin-top: 1rem; }
.space-y-6 > * + * { margin-top: 1.5rem; }
.gap-4 { gap: 1rem; }
.gap-6 { gap: 1.5rem; }
.gap-8 { gap: 2rem; }
.text-xs { font-size: 0.75rem; }
.text-sm { font-size: 0.875rem; }
.text-base { font-size: 1rem; }
.text-lg { font-size: 1.125rem; }
.text-xl { font-size: 1.25rem; }
.text-2xl { font-size: 1.5rem; }
.text-3xl { font-size: 1.875rem; }
.text-4xl { font-size: 2.25rem; }
.text-5xl { font-size: 3rem; }
.text-6xl { font-size: 3.75rem; }
.font-bold { font-weight: 700; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.text-white { color: white; }
.text-squid-pink { color: var(--squid-pink); }
.text-squid-green { color: var(--squid-green); }
.text-squid-red { color: var(--squid-red); }
.bg-black { background-color: black; }
.bg-white { background-color: white; }
.bg-squid-pink { background-color: var(--squid-pink); }
.bg-squid-green { background-color: var(--squid-green); }
.bg-card { background-color: var(--card); }
.border { border-width: 1px; }
.border-2 { border-width: 2px; }
.border-squid-pink { border-color: var(--squid-pink); }
.border-squid-green { border-color: var(--squid-green); }
.rounded { border-radius: 0.25rem; }
.rounded-md { border-radius: 0.375rem; }
.rounded-lg { border-radius: 0.5rem; }
.rounded-xl { border-radius: 0.75rem; }
.rounded-full { border-radius: 9999px; }
.w-full { width: 100%; }
.h-full { height: 100%; }
.min-h-screen { min-height: 100vh; }
.grid { display: grid; }
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.animate-float { animation: float 3s ease-in-out infinite; }
.animate-liquid { animation: liquid 4s ease-in-out infinite; }
.animate-guard-sway { animation: guard-sway 3s ease-in-out infinite; }
.animate-number-flip { animation: number-flip 2s ease-in-out infinite; }
.animate-shake { animation: shake 0.5s ease-in-out infinite; }

@media (min-width: 768px) {
  .md\\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .md\\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .md\\:text-4xl { font-size: 2.25rem; }
  .md\\:text-5xl { font-size: 3rem; }
  .md\\:text-6xl { font-size: 3.75rem; }
}

@media (min-width: 1024px) {
  .lg\\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .lg\\:text-5xl { font-size: 3rem; }
  .lg\\:text-6xl { font-size: 3.75rem; }
}
