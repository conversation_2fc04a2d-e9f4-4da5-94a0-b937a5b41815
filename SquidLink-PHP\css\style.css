/* Squid Game Portfolio Styles - Exact Match to React Version */
:root {
  --background: hsl(0, 0%, 0%);
  --foreground: hsl(0, 0%, 100%);
  --muted: hsl(0, 0%, 10%);
  --muted-foreground: hsl(0, 0%, 70%);
  --popover: hsl(0, 0%, 5%);
  --popover-foreground: hsl(0, 0%, 95%);
  --card: hsl(0, 0%, 5%);
  --card-foreground: hsl(0, 0%, 95%);
  --border: hsl(340, 100%, 35%);
  --input: hsl(0, 0%, 10%);
  --primary: hsl(340, 100%, 35%);
  --primary-foreground: hsl(0, 0%, 100%);
  --secondary: hsl(150, 100%, 35%);
  --secondary-foreground: hsl(0, 0%, 0%);
  --accent: hsl(150, 100%, 35%);
  --accent-foreground: hsl(0, 0%, 0%);
  --destructive: hsl(0, 84%, 60%);
  --destructive-foreground: hsl(0, 100%, 100%);
  --ring: hsl(340, 100%, 35%);
  --radius: 0.5rem;

  /* Squid Game Colors */
  --squid-pink: rgb(255, 0, 85);
  --squid-green: hsl(150, 100%, 35%);
  --squid-red: hsl(348, 83%, 47%);
  --electric-blue: hsl(195, 100%, 50%);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  border-color: var(--border);
}

*,
*::before,
*::after {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

body {
  font-family: 'Poppins', sans-serif;
  background: var(--background);
  color: var(--foreground);
  overflow-x: hidden;
  cursor: none;
  scroll-behavior: smooth;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

h1, h2, h3, h4, h5, h6 {
  font-family: 'Bebas Neue', cursive;
}

/* Custom Cursor */
.squid-cursor {
  position: fixed;
  width: 20px;
  height: 20px;
  background: var(--squid-pink);
  border-radius: 50%;
  pointer-events: none;
  z-index: 9999;
  mix-blend-mode: difference;
  transition: transform 0.1s ease;
}

/* Scroll Progress */
.scroll-progress {
  position: fixed;
  top: 0;
  left: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--squid-pink), var(--squid-green));
  z-index: 1000;
  transition: width 0.1s ease;
}

/* 3D Card Effects */
.card-3d {
  transform-style: preserve-3d;
  transition: transform 0.6s;
}

.card-3d:hover {
  transform: rotateY(180deg);
}

.card-face {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
}

.card-back {
  transform: rotateY(180deg);
}

/* NFC Pulse Effect */
.nfc-pulse {
  animation: pulse-glow 1s ease-in-out infinite;
}

/* Guard Figure Styles */
.guard-figure {
  width: 8rem;
  height: 12rem;
  position: relative;
}

.guard-body {
  width: 100%;
  height: 100%;
  background: rgb(255, 0, 85);
  border-radius: 4rem 4rem 0rem 0rem !important;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.guard-mask-triangular {
  position: absolute;
  top: 4rem;
  left: 50%;
  transform: translateX(-50%);
  width: 4rem;
  height: 4rem;
  background: black;
  border-radius: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.guard-mask-triangular::before {
  content: '';
  width: 1.5rem;
  height: 1.5rem;
  background: white;
  clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
  border: 2px solid white;
}

.guard-mask-round {
  position: absolute;
  top: 4rem;
  left: 50%;
  transform: translateX(-50%);
  width: 4rem;
  height: 4rem;
  background: black;
  border-radius: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.guard-mask-round::before {
  content: '';
  width: 1.5rem;
  height: 1.5rem;
  background: white;
  border-radius: 50%;
}

.guard-eyes {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  gap: 0.3rem;
  z-index: 10;
}

.guard-eye-left, .guard-eye-right {
  width: 0.4rem;
  height: 0.4rem;
  background: radial-gradient(circle, rgb(255, 0, 85), #cc0044);
  border-radius: 50%;
  animation: red-light-bloom 2s ease-in-out infinite;
  box-shadow:
    0 0 8px rgb(255, 0, 85),
    0 0 16px rgb(255, 0, 85),
    0 0 24px rgb(255, 0, 85);
}

/* Mobile Guard Styles */
.guard-figure-mobile {
  width: 8rem;
  height: 10rem;
  position: relative;
}

.guard-body-mobile {
  width: 100%;
  height: 100%;
  background: rgb(255, 0, 85);
  border-radius: 4rem 4rem 0rem 0rem;
  box-shadow: 0 4px 15px rgba(0,0,0,0.3);
  position: relative;
}

.guard-mask-triangular-mobile {
  position: absolute;
  top: 3rem;
  left: 50%;
  transform: translateX(-50%);
  width: 4rem;
  height: 4rem;
  background: black;
  border-radius: 3.3rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.guard-mask-triangular-mobile::before {
  content: '';
  width: 2rem;
  height: 2rem;
  background: white;
  clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
}

.guard-mask-round-mobile {
  position: absolute;
  top: 3rem;
  left: 50%;
  transform: translateX(-50%);
  width: 4rem;
  height: 4rem;
  background: black;
  border-radius: 2.3rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.guard-mask-round-mobile::before {
  content: '';
  width: 2rem;
  height: 2rem;
  background: white;
  border-radius: 50%;
}

.guard-eyes-mobile {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  gap: 0.2rem;
  z-index: 10;
}

.guard-eye-left-mobile, .guard-eye-right-mobile {
  width: 0.25rem;
  height: 0.25rem;
  background: radial-gradient(circle, rgb(255, 0, 85), #cc0044);
  border-radius: 50%;
  animation: red-light-bloom 2s ease-in-out infinite;
  box-shadow:
    0 0 4px rgb(255, 0, 85),
    0 0 8px rgb(255, 0, 85),
    0 0 12px rgb(255, 0, 85);
}

/* Digital Display */
.digital-display {
  font-family: 'Courier New', 'Lucida Console', monospace;
  font-weight: 800;
  color: #ff0000 !important;
  text-align: center;
  display: block;
  letter-spacing: 0.15em;
  text-shadow:
    0 0 2px #ff0000,
    0 0 4px #ff0000,
    0 0 6px #ff0000,
    0 0 8px #ff0000;
  filter: brightness(1.2) contrast(1.1);
  animation: digital-flicker 4s ease-in-out infinite;
  position: relative;
}

.digital-display::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: repeating-linear-gradient(
    0deg,
    transparent,
    transparent 1px,
    rgba(255, 0, 0, 0.03) 1px,
    rgba(255, 0, 0, 0.03) 2px
  );
  pointer-events: none;
}

.digital-display::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: repeating-linear-gradient(
    90deg,
    transparent,
    transparent 1px,
    rgba(255, 0, 0, 0.03) 1px,
    rgba(255, 0, 0, 0.03) 2px
  );
  pointer-events: none;
}

/* Enhanced Glitch Text Effect */
.glitch-text {
  position: relative;
  animation: glitch-main 3s infinite;
  text-shadow:
    0 0 10px rgba(255, 255, 255, 0.8),
    0 0 20px rgba(255, 0, 85, 0.6),
    0 0 30px rgba(0, 255, 136, 0.4);
}

.glitch-text::before,
.glitch-text::after {
  content: attr(data-text);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.glitch-text::before {
  animation: glitch-1 0.3s infinite;
  color: rgb(255, 0, 85);
  z-index: -1;
  text-shadow: 2px 0 0 rgb(255, 0, 85);
}

.glitch-text::after {
  animation: glitch-2 0.3s infinite;
  color: #00ff88;
  z-index: -2;
  text-shadow: -2px 0 0 #00ff88;
}

/* Squid Shape */
.squid-shape {
  width: 4rem;
  height: 4rem;
  background: var(--squid-pink);
  border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
  position: relative;
  animation: squid-rotate 3s infinite ease-in-out;
}

.squid-shape::before {
  content: '';
  position: absolute;
  width: 1rem;
  height: 2rem;
  background: var(--squid-pink);
  border-radius: 50%;
  top: -0.5rem;
  left: 50%;
  transform: translateX(-50%) rotate(-45deg);
}

.squid-shape::after {
  content: '';
  position: absolute;
  width: 0.5rem;
  height: 0.5rem;
  background: white;
  border-radius: 50%;
  top: 1rem;
  left: 1rem;
  transform: rotate(-45deg);
}

@keyframes squid-rotate {
  0%, 100% { transform: rotate(45deg); }
  50% { transform: rotate(50deg); }
}

/* Utility Classes */
.text-squid-pink {
  color: var(--squid-pink);
}

.text-squid-green {
  color: var(--squid-green);
}

.text-squid-red {
  color: var(--squid-red);
}

.bg-squid-pink {
  background-color: var(--squid-pink);
}

.bg-squid-green {
  background-color: var(--squid-green);
}

.bg-squid-red {
  background-color: var(--squid-red);
}

.border-squid-pink {
  border-color: var(--squid-pink);
}

.border-squid-green {
  border-color: var(--squid-green);
}

.border-squid-red {
  border-color: var(--squid-red);
}

/* Animation Classes */
.animate-glitch {
  animation: glitch 0.3s ease-in-out infinite alternate;
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite alternate;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-liquid {
  animation: liquid 0.6s ease-out;
}

.animate-guard-sway {
  animation: guard-sway 3s ease-in-out infinite;
}

.animate-number-flip {
  animation: number-flip 0.8s ease-out;
}

.animate-shake {
  animation: shake 0.5s ease-in-out;
}

/* Keyframe Animations */
@keyframes glitch {
  0% { transform: translate(0) }
  20% { transform: translate(-2px, 2px) }
  40% { transform: translate(-2px, -2px) }
  60% { transform: translate(2px, 2px) }
  80% { transform: translate(2px, -2px) }
  100% { transform: translate(0) }
}

@keyframes pulse-glow {
  0% { box-shadow: 0 0 5px var(--squid-pink), 0 0 10px var(--squid-pink), 0 0 15px var(--squid-pink); }
  100% { box-shadow: 0 0 10px var(--squid-pink), 0 0 20px var(--squid-pink), 0 0 30px var(--squid-pink); }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.animate-glitch {
  animation: glitch 0.5s ease-in-out;
}

@keyframes liquid {
  0% { border-radius: 50%; transform: scale(1); }
  50% { border-radius: 60% 40% 30% 70%; transform: scale(1.1); }
  100% { border-radius: 50%; transform: scale(1); }
}

@keyframes guard-sway {
  0%, 100% { transform: rotate(-1deg); }
  50% { transform: rotate(1deg); }
}

@keyframes number-flip {
  0% { transform: rotateY(0deg); }
  50% { transform: rotateY(90deg); }
  100% { transform: rotateY(0deg); }
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

@keyframes red-light-bloom {
  0%, 100% {
    box-shadow:
      0 0 5px rgb(255, 0, 85),
      0 0 10px rgb(255, 0, 85),
      0 0 15px rgb(255, 0, 85);
    opacity: 0.8;
  }
  50% {
    box-shadow:
      0 0 10px rgb(255, 0, 85),
      0 0 20px rgb(255, 0, 85),
      0 0 30px rgb(255, 0, 85),
      0 0 40px rgb(255, 0, 85);
    opacity: 1;
  }
}

@keyframes digital-flicker {
  0%, 97% {
    opacity: 1;
    text-shadow:
      0 0 2px #ff0000,
      0 0 4px #ff0000,
      0 0 6px #ff0000,
      0 0 8px #ff0000;
  }
  98% {
    opacity: 0.98;
    text-shadow:
      0 0 1px #ff0000,
      0 0 2px #ff0000,
      0 0 4px #ff0000;
  }
  100% {
    opacity: 1;
    text-shadow:
      0 0 2px #ff0000,
      0 0 4px #ff0000,
      0 0 6px #ff0000,
      0 0 8px #ff0000;
  }
}

@keyframes glitch-main {
  0%, 90%, 100% {
    transform: translate(0);
    filter: hue-rotate(0deg);
  }
  10% {
    transform: translate(-5px, 2px);
    filter: hue-rotate(90deg);
  }
  20% {
    transform: translate(-2px, -5px);
    filter: hue-rotate(180deg);
  }
  30% {
    transform: translate(5px, 2px);
    filter: hue-rotate(270deg);
  }
  40% {
    transform: translate(2px, -2px);
    filter: hue-rotate(360deg);
  }
  50% {
    transform: translate(-3px, 3px) scale(1.01);
    filter: hue-rotate(45deg);
  }
  60% {
    transform: translate(3px, -3px) scale(0.99);
    filter: hue-rotate(135deg);
  }
  70% {
    transform: translate(-2px, -2px);
    filter: hue-rotate(225deg);
  }
  80% {
    transform: translate(4px, 1px);
    filter: hue-rotate(315deg);
  }
}

@keyframes glitch-1 {
  0%, 100% {
    transform: translate(0);
    clip-path: polygon(0 0, 100% 0, 100% 8%, 0 8%);
    opacity: 0.8;
  }
  10% {
    transform: translate(-4px, 3px);
    clip-path: polygon(0 12%, 100% 12%, 100% 22%, 0 22%);
    opacity: 0.9;
  }
  20% {
    transform: translate(-3px, 2px);
    clip-path: polygon(0 25%, 100% 25%, 100% 35%, 0 35%);
    opacity: 0.7;
  }
  30% {
    transform: translate(4px, -2px);
    clip-path: polygon(0 38%, 100% 38%, 100% 48%, 0 48%);
    opacity: 0.8;
  }
  40% {
    transform: translate(-2px, -3px);
    clip-path: polygon(0 51%, 100% 51%, 100% 61%, 0 61%);
    opacity: 0.9;
  }
  50% {
    transform: translate(3px, 4px);
    clip-path: polygon(0 64%, 100% 64%, 100% 74%, 0 74%);
    opacity: 0.6;
  }
  60% {
    transform: translate(2px, 2px);
    clip-path: polygon(0 77%, 100% 77%, 100% 87%, 0 87%);
    opacity: 0.8;
  }
  70% {
    transform: translate(-4px, -1px);
    clip-path: polygon(0 90%, 100% 90%, 100% 100%, 0 100%);
    opacity: 0.7;
  }
  80% {
    transform: translate(2px, -4px);
    clip-path: polygon(0 5%, 100% 5%, 100% 15%, 0 15%);
    opacity: 0.9;
  }
  90% {
    transform: translate(-1px, 3px);
    clip-path: polygon(0 30%, 100% 30%, 100% 40%, 0 40%);
    opacity: 0.8;
  }
}

@keyframes glitch-2 {
  0%, 100% {
    transform: translate(0);
    clip-path: polygon(0 5%, 100% 5%, 100% 15%, 0 15%);
    opacity: 0.7;
  }
  15% {
    transform: translate(3px, -4px);
    clip-path: polygon(0 18%, 100% 18%, 100% 28%, 0 28%);
    opacity: 0.8;
  }
  25% {
    transform: translate(2px, -2px);
    clip-path: polygon(0 31%, 100% 31%, 100% 41%, 0 41%);
    opacity: 0.6;
  }
  35% {
    transform: translate(-3px, 3px);
    clip-path: polygon(0 44%, 100% 44%, 100% 54%, 0 54%);
    opacity: 0.9;
  }
  45% {
    transform: translate(4px, 2px);
    clip-path: polygon(0 57%, 100% 57%, 100% 67%, 0 67%);
    opacity: 0.7;
  }
  55% {
    transform: translate(-2px, -3px);
    clip-path: polygon(0 70%, 100% 70%, 100% 80%, 0 80%);
    opacity: 0.8;
  }
  65% {
    transform: translate(-4px, -2px);
    clip-path: polygon(0 83%, 100% 83%, 100% 93%, 0 93%);
    opacity: 0.6;
  }
  75% {
    transform: translate(1px, 4px);
    clip-path: polygon(0 2%, 100% 2%, 100% 12%, 0 12%);
    opacity: 0.9;
  }
  85% {
    transform: translate(-2px, 2px);
    clip-path: polygon(0 96%, 100% 96%, 100% 100%, 0 100%);
    opacity: 0.7;
  }
  95% {
    transform: translate(3px, -1px);
    clip-path: polygon(0 25%, 100% 25%, 100% 35%, 0 35%);
    opacity: 0.8;
  }
}

/* Particle Background */
#particles-js {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: -1;
  background: linear-gradient(135deg, #000000 0%, #1a0a0a 50%, #000000 100%);
}

/* Premium Loading Animation */
.premium-loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.95);
  z-index: 10000;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
}

.loading-content {
  text-align: center;
  color: white;
}

.loading-logo {
  width: 120px;
  height: 120px;
  margin: 0 auto 2rem;
  animation: rotate-logo 2s linear infinite;
}

.loading-progress {
  width: 300px;
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  overflow: hidden;
  margin: 2rem 0;
}

.loading-progress-bar {
  height: 100%;
  background: linear-gradient(90deg, var(--squid-pink), var(--squid-green));
  border-radius: 2px;
  animation: loading-fill 2s ease-out forwards;
}

.loading-text {
  font-family: 'Bebas Neue', cursive;
  font-size: 2rem;
  color: var(--squid-pink);
  margin-bottom: 1rem;
  animation: pulse-text 1s ease-in-out infinite;
}

.loading-subtitle {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 2rem;
}

@keyframes rotate-logo {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes loading-fill {
  from { width: 0%; }
  to { width: 100%; }
}

@keyframes pulse-text {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* Responsive Design */
@media (max-width: 768px) {
  .guard-figure {
    width: 6rem;
    height: 9rem;
  }

  .guard-figure-mobile {
    width: 6rem;
    height: 8rem;
  }

  .loading-logo {
    width: 80px;
    height: 80px;
  }

  .loading-progress {
    width: 250px;
  }

  .loading-text {
    font-size: 1.5rem;
  }
}

/* NFC Animation Overlay */
.nfc-animation-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.9);
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: nfc-flash 4s ease-out forwards;
}

.nfc-content {
  text-align: center;
  color: white;
  font-family: 'Bebas Neue', cursive;
}

.nfc-card {
  font-size: 3rem;
  color: var(--squid-pink);
  margin-bottom: 2rem;
  animation: nfc-pulse 1s ease-in-out infinite;
}

.nfc-player {
  font-size: 2rem;
  color: var(--squid-green);
  margin-bottom: 1rem;
  animation: nfc-glow 2s ease-in-out infinite;
}

.nfc-status {
  font-size: 1.5rem;
  color: white;
  animation: nfc-blink 0.5s ease-in-out infinite;
}

@keyframes nfc-flash {
  0% { background: rgba(255, 0, 85, 0.8); }
  10% { background: rgba(0, 0, 0, 0.9); }
  20% { background: rgba(255, 0, 85, 0.6); }
  30% { background: rgba(0, 0, 0, 0.9); }
  100% { background: rgba(0, 0, 0, 0.9); }
}

@keyframes nfc-pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

@keyframes nfc-glow {
  0%, 100% { text-shadow: 0 0 10px var(--squid-green); }
  50% { text-shadow: 0 0 20px var(--squid-green), 0 0 30px var(--squid-green); }
}

@keyframes nfc-blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Particles Background */
#particles-js {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: -1;
}

/* Digital Display */
.digital-display {
  text-shadow: 
    0 0 5px currentColor,
    0 0 10px currentColor,
    0 0 15px currentColor,
    0 0 20px currentColor;
  animation: digital-flicker 2s infinite alternate;
}

@keyframes digital-flicker {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.8; }
}

/* Glitch Text Animation */
.glitch-text {
  position: relative;
  animation: glitch-main 3s infinite;
  text-shadow: 
    0 0 10px rgba(255, 255, 255, 0.8),
    0 0 20px var(--squid-pink),
    0 0 30px var(--squid-green);
}

.glitch-text::before,
.glitch-text::after {
  content: attr(data-text);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.glitch-text::before {
  animation: glitch-1 0.3s infinite;
  color: var(--squid-pink);
  z-index: -1;
  text-shadow: 2px 0 0 var(--squid-pink);
}

.glitch-text::after {
  animation: glitch-2 0.3s infinite;
  color: var(--squid-green);
  z-index: -2;
  text-shadow: -2px 0 0 var(--squid-green);
}

@keyframes glitch-main {
  0%, 90%, 100% { 
    transform: translate(0); 
    filter: hue-rotate(0deg);
  }
  10% { 
    transform: translate(-5px, 2px); 
    filter: hue-rotate(90deg);
  }
  20% { 
    transform: translate(-2px, -5px); 
    filter: hue-rotate(180deg);
  }
  30% { 
    transform: translate(5px, 2px); 
    filter: hue-rotate(270deg);
  }
  40% { 
    transform: translate(2px, -2px); 
    filter: hue-rotate(360deg);
  }
  50% { 
    transform: translate(-3px, 3px) scale(1.01); 
    filter: hue-rotate(45deg);
  }
  60% { 
    transform: translate(3px, -3px) scale(0.99); 
    filter: hue-rotate(135deg);
  }
  70% { 
    transform: translate(-2px, -2px); 
    filter: hue-rotate(225deg);
  }
  80% { 
    transform: translate(4px, 1px); 
    filter: hue-rotate(315deg);
  }
}

@keyframes glitch-1 {
  0%, 100% {
    transform: translate(0);
    clip-path: polygon(0 0, 100% 0, 100% 8%, 0 8%);
    opacity: 0.8;
  }
  10% {
    transform: translate(-4px, 3px);
    clip-path: polygon(0 12%, 100% 12%, 100% 22%, 0 22%);
    opacity: 0.9;
  }
  20% {
    transform: translate(-3px, 2px);
    clip-path: polygon(0 25%, 100% 25%, 100% 35%, 0 35%);
    opacity: 0.7;
  }
  30% {
    transform: translate(4px, -2px);
    clip-path: polygon(0 38%, 100% 38%, 100% 48%, 0 48%);
    opacity: 0.8;
  }
  40% {
    transform: translate(-2px, -3px);
    clip-path: polygon(0 51%, 100% 51%, 100% 61%, 0 61%);
    opacity: 0.9;
  }
  50% {
    transform: translate(3px, 4px);
    clip-path: polygon(0 64%, 100% 64%, 100% 74%, 0 74%);
    opacity: 0.6;
  }
  60% {
    transform: translate(2px, 2px);
    clip-path: polygon(0 77%, 100% 77%, 100% 87%, 0 87%);
    opacity: 0.8;
  }
  70% {
    transform: translate(-4px, -1px);
    clip-path: polygon(0 90%, 100% 90%, 100% 100%, 0 100%);
    opacity: 0.7;
  }
  80% {
    transform: translate(2px, -4px);
    clip-path: polygon(0 5%, 100% 5%, 100% 15%, 0 15%);
    opacity: 0.9;
  }
  90% {
    transform: translate(-1px, 3px);
    clip-path: polygon(0 30%, 100% 30%, 100% 40%, 0 40%);
    opacity: 0.8;
  }
}

@keyframes glitch-2 {
  0%, 100% {
    transform: translate(0);
    clip-path: polygon(0 5%, 100% 5%, 100% 15%, 0 15%);
    opacity: 0.7;
  }
  15% {
    transform: translate(3px, -4px);
    clip-path: polygon(0 18%, 100% 18%, 100% 28%, 0 28%);
    opacity: 0.8;
  }
  25% {
    transform: translate(2px, -2px);
    clip-path: polygon(0 31%, 100% 31%, 100% 41%, 0 41%);
    opacity: 0.6;
  }
  35% {
    transform: translate(-3px, 3px);
    clip-path: polygon(0 44%, 100% 44%, 100% 54%, 0 54%);
    opacity: 0.9;
  }
  45% {
    transform: translate(4px, 2px);
    clip-path: polygon(0 57%, 100% 57%, 100% 67%, 0 67%);
    opacity: 0.7;
  }
  55% {
    transform: translate(-2px, -3px);
    clip-path: polygon(0 70%, 100% 70%, 100% 80%, 0 80%);
    opacity: 0.8;
  }
  65% {
    transform: translate(-4px, -2px);
    clip-path: polygon(0 83%, 100% 83%, 100% 93%, 0 93%);
    opacity: 0.6;
  }
  75% {
    transform: translate(1px, 4px);
    clip-path: polygon(0 2%, 100% 2%, 100% 12%, 0 12%);
    opacity: 0.9;
  }
  85% {
    transform: translate(-2px, 2px);
    clip-path: polygon(0 96%, 100% 96%, 100% 100%, 0 100%);
    opacity: 0.7;
  }
  95% {
    transform: translate(3px, -1px);
    clip-path: polygon(0 25%, 100% 25%, 100% 35%, 0 35%);
    opacity: 0.8;
  }
}

/* Float Animation */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

/* NFC Pulse */
.nfc-pulse {
  animation: nfc-pulse 2s infinite;
}

@keyframes nfc-pulse {
  0%, 100% { 
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(255, 0, 85, 0.7);
  }
  50% { 
    transform: scale(1.1);
    box-shadow: 0 0 0 20px rgba(255, 0, 85, 0);
  }
}

/* 3D Card Effects */
.card-3d {
  perspective: 1000px;
  transform-style: preserve-3d;
}

.card-face {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  transition: transform 0.6s;
  border-radius: 12px;
  padding: 24px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
}

.card-back {
  transform: rotateY(180deg);
}

.card-3d:hover .card-face:not(.card-back) {
  transform: rotateY(180deg);
}

.card-3d:hover .card-back {
  transform: rotateY(0deg);
}

/* Enhanced Skill Card Animations */
.skill-card {
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  transform-origin: center;
}

.skill-card:hover {
  transform: translateY(-10px) scale(1.05);
  box-shadow:
    0 20px 40px rgba(255, 0, 85, 0.3),
    0 0 20px rgba(255, 0, 85, 0.2),
    inset 0 0 20px rgba(255, 255, 255, 0.1);
  border: 2px solid var(--squid-pink);
}

.skill-card:hover .skill-bar {
  animation: skill-fill 1.5s ease-out forwards;
}

.skill-card:hover .skill-level {
  animation: level-glow 1s ease-in-out infinite alternate;
}

@keyframes skill-fill {
  0% { width: 0%; }
  100% { width: var(--skill-percentage, 90%); }
}

@keyframes level-glow {
  0% {
    text-shadow: 0 0 5px var(--squid-green);
    transform: scale(1);
  }
  100% {
    text-shadow: 0 0 15px var(--squid-green), 0 0 25px var(--squid-green);
    transform: scale(1.1);
  }
}

/* Skill Bar Animation */
.skill-bar {
  transition: width 2s ease-in-out;
}

/* Guard Figures */
.guard-figure, .guard-figure-mobile {
  width: 60px;
  height: 80px;
  position: relative;
}

.guard-figure-mobile {
  width: 40px;
  height: 60px;
}

.guard-body, .guard-body-mobile {
  width: 100%;
  height: 100%;
  background: #ff0055;
  border-radius: 8px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.guard-body-mobile {
  border-radius: 6px;
}

.guard-mask-triangular, .guard-mask-round,
.guard-mask-triangular-mobile, .guard-mask-round-mobile {
  width: 30px;
  height: 30px;
  background: #000;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.guard-mask-triangular-mobile, .guard-mask-round-mobile {
  width: 20px;
  height: 20px;
}

.guard-mask-triangular::before, .guard-mask-triangular-mobile::before {
  content: '';
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-bottom: 12px solid #fff;
  position: absolute;
}

.guard-mask-triangular-mobile::before {
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-bottom: 8px solid #fff;
}

.guard-mask-round::before, .guard-mask-round-mobile::before {
  content: '';
  width: 12px;
  height: 12px;
  background: #fff;
  border-radius: 50%;
  position: absolute;
}

.guard-mask-round-mobile::before {
  width: 8px;
  height: 8px;
}

.guard-eye, .guard-eye-mobile {
  width: 6px;
  height: 6px;
  background: #ff0000;
  border-radius: 50%;
  position: absolute;
  box-shadow: 0 0 10px #ff0000, 0 0 20px #ff0000, 0 0 30px #ff0000;
  animation: eye-glow 2s infinite alternate;
}

.guard-eye-mobile {
  width: 4px;
  height: 4px;
}

@keyframes eye-glow {
  0% { box-shadow: 0 0 5px #ff0000, 0 0 10px #ff0000, 0 0 15px #ff0000; }
  100% { box-shadow: 0 0 10px #ff0000, 0 0 20px #ff0000, 0 0 30px #ff0000; }
}

/* Small Guards for NFC Overlay */
.guard-figure-small {
  width: 30px;
  height: 40px;
}

.guard-body-small {
  width: 100%;
  height: 100%;
  background: #ff0055;
  border-radius: 4px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.guard-mask-triangular-small, .guard-mask-round-small {
  width: 15px;
  height: 15px;
  background: #000;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.guard-mask-triangular-small::before {
  content: '';
  width: 0;
  height: 0;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-bottom: 6px solid #fff;
  position: absolute;
}

.guard-mask-round-small::before {
  content: '';
  width: 6px;
  height: 6px;
  background: #fff;
  border-radius: 50%;
  position: absolute;
}

.guard-eye-small {
  width: 3px;
  height: 3px;
  background: #ff0000;
  border-radius: 50%;
  position: absolute;
  box-shadow: 0 0 5px #ff0000, 0 0 10px #ff0000;
  animation: eye-glow 2s infinite alternate;
}

/* Responsive Design */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

@media (max-width: 768px) {
  .guard-figure {
    width: 40px;
    height: 60px;
  }
  
  .guard-body {
    border-radius: 6px;
  }
  
  .guard-mask-triangular, .guard-mask-round {
    width: 20px;
    height: 20px;
  }
}
