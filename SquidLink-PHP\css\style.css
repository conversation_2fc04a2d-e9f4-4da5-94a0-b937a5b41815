/* SquidLink PHP - Custom Styles */
/* Converted from React/Tailwind to pure CSS while maintaining exact design */

:root {
  --squid-pink: rgb(255, 0, 85);
  --squid-green: #00ffaa;
  --squid-red: #ff0055;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Poppins', sans-serif;
  background: #000;
  color: #fff;
  overflow-x: hidden;
}

/* Custom Cursor */
.squid-cursor {
  position: fixed;
  width: 20px;
  height: 20px;
  background: var(--squid-pink);
  border-radius: 50%;
  pointer-events: none;
  z-index: 9999;
  transition: transform 0.1s ease;
  mix-blend-mode: difference;
}

/* Scroll Progress */
.scroll-progress {
  position: fixed;
  top: 0;
  left: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--squid-pink), var(--squid-green));
  z-index: 1000;
  transition: width 0.1s ease;
}

/* Particles Background */
#particles-js {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: -1;
}

/* Digital Display */
.digital-display {
  text-shadow: 
    0 0 5px currentColor,
    0 0 10px currentColor,
    0 0 15px currentColor,
    0 0 20px currentColor;
  animation: digital-flicker 2s infinite alternate;
}

@keyframes digital-flicker {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.8; }
}

/* Glitch Text Animation */
.glitch-text {
  position: relative;
  animation: glitch-main 3s infinite;
  text-shadow: 
    0 0 10px rgba(255, 255, 255, 0.8),
    0 0 20px var(--squid-pink),
    0 0 30px var(--squid-green);
}

.glitch-text::before,
.glitch-text::after {
  content: attr(data-text);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.glitch-text::before {
  animation: glitch-1 0.3s infinite;
  color: var(--squid-pink);
  z-index: -1;
  text-shadow: 2px 0 0 var(--squid-pink);
}

.glitch-text::after {
  animation: glitch-2 0.3s infinite;
  color: var(--squid-green);
  z-index: -2;
  text-shadow: -2px 0 0 var(--squid-green);
}

@keyframes glitch-main {
  0%, 90%, 100% { 
    transform: translate(0); 
    filter: hue-rotate(0deg);
  }
  10% { 
    transform: translate(-5px, 2px); 
    filter: hue-rotate(90deg);
  }
  20% { 
    transform: translate(-2px, -5px); 
    filter: hue-rotate(180deg);
  }
  30% { 
    transform: translate(5px, 2px); 
    filter: hue-rotate(270deg);
  }
  40% { 
    transform: translate(2px, -2px); 
    filter: hue-rotate(360deg);
  }
  50% { 
    transform: translate(-3px, 3px) scale(1.01); 
    filter: hue-rotate(45deg);
  }
  60% { 
    transform: translate(3px, -3px) scale(0.99); 
    filter: hue-rotate(135deg);
  }
  70% { 
    transform: translate(-2px, -2px); 
    filter: hue-rotate(225deg);
  }
  80% { 
    transform: translate(4px, 1px); 
    filter: hue-rotate(315deg);
  }
}

@keyframes glitch-1 {
  0%, 100% {
    transform: translate(0);
    clip-path: polygon(0 0, 100% 0, 100% 8%, 0 8%);
    opacity: 0.8;
  }
  10% {
    transform: translate(-4px, 3px);
    clip-path: polygon(0 12%, 100% 12%, 100% 22%, 0 22%);
    opacity: 0.9;
  }
  20% {
    transform: translate(-3px, 2px);
    clip-path: polygon(0 25%, 100% 25%, 100% 35%, 0 35%);
    opacity: 0.7;
  }
  30% {
    transform: translate(4px, -2px);
    clip-path: polygon(0 38%, 100% 38%, 100% 48%, 0 48%);
    opacity: 0.8;
  }
  40% {
    transform: translate(-2px, -3px);
    clip-path: polygon(0 51%, 100% 51%, 100% 61%, 0 61%);
    opacity: 0.9;
  }
  50% {
    transform: translate(3px, 4px);
    clip-path: polygon(0 64%, 100% 64%, 100% 74%, 0 74%);
    opacity: 0.6;
  }
  60% {
    transform: translate(2px, 2px);
    clip-path: polygon(0 77%, 100% 77%, 100% 87%, 0 87%);
    opacity: 0.8;
  }
  70% {
    transform: translate(-4px, -1px);
    clip-path: polygon(0 90%, 100% 90%, 100% 100%, 0 100%);
    opacity: 0.7;
  }
  80% {
    transform: translate(2px, -4px);
    clip-path: polygon(0 5%, 100% 5%, 100% 15%, 0 15%);
    opacity: 0.9;
  }
  90% {
    transform: translate(-1px, 3px);
    clip-path: polygon(0 30%, 100% 30%, 100% 40%, 0 40%);
    opacity: 0.8;
  }
}

@keyframes glitch-2 {
  0%, 100% {
    transform: translate(0);
    clip-path: polygon(0 5%, 100% 5%, 100% 15%, 0 15%);
    opacity: 0.7;
  }
  15% {
    transform: translate(3px, -4px);
    clip-path: polygon(0 18%, 100% 18%, 100% 28%, 0 28%);
    opacity: 0.8;
  }
  25% {
    transform: translate(2px, -2px);
    clip-path: polygon(0 31%, 100% 31%, 100% 41%, 0 41%);
    opacity: 0.6;
  }
  35% {
    transform: translate(-3px, 3px);
    clip-path: polygon(0 44%, 100% 44%, 100% 54%, 0 54%);
    opacity: 0.9;
  }
  45% {
    transform: translate(4px, 2px);
    clip-path: polygon(0 57%, 100% 57%, 100% 67%, 0 67%);
    opacity: 0.7;
  }
  55% {
    transform: translate(-2px, -3px);
    clip-path: polygon(0 70%, 100% 70%, 100% 80%, 0 80%);
    opacity: 0.8;
  }
  65% {
    transform: translate(-4px, -2px);
    clip-path: polygon(0 83%, 100% 83%, 100% 93%, 0 93%);
    opacity: 0.6;
  }
  75% {
    transform: translate(1px, 4px);
    clip-path: polygon(0 2%, 100% 2%, 100% 12%, 0 12%);
    opacity: 0.9;
  }
  85% {
    transform: translate(-2px, 2px);
    clip-path: polygon(0 96%, 100% 96%, 100% 100%, 0 100%);
    opacity: 0.7;
  }
  95% {
    transform: translate(3px, -1px);
    clip-path: polygon(0 25%, 100% 25%, 100% 35%, 0 35%);
    opacity: 0.8;
  }
}

/* Float Animation */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

/* NFC Pulse */
.nfc-pulse {
  animation: nfc-pulse 2s infinite;
}

@keyframes nfc-pulse {
  0%, 100% { 
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(255, 0, 85, 0.7);
  }
  50% { 
    transform: scale(1.1);
    box-shadow: 0 0 0 20px rgba(255, 0, 85, 0);
  }
}

/* 3D Card Effects */
.card-3d {
  perspective: 1000px;
  transform-style: preserve-3d;
}

.card-face {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  transition: transform 0.6s;
  border-radius: 12px;
  padding: 24px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
}

.card-back {
  transform: rotateY(180deg);
}

.card-3d:hover .card-face:not(.card-back) {
  transform: rotateY(180deg);
}

.card-3d:hover .card-back {
  transform: rotateY(0deg);
}

/* Skill Bar Animation */
.skill-bar {
  transition: width 2s ease-in-out;
}

/* Guard Figures */
.guard-figure, .guard-figure-mobile {
  width: 60px;
  height: 80px;
  position: relative;
}

.guard-figure-mobile {
  width: 40px;
  height: 60px;
}

.guard-body, .guard-body-mobile {
  width: 100%;
  height: 100%;
  background: #ff0055;
  border-radius: 8px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.guard-body-mobile {
  border-radius: 6px;
}

.guard-mask-triangular, .guard-mask-round,
.guard-mask-triangular-mobile, .guard-mask-round-mobile {
  width: 30px;
  height: 30px;
  background: #000;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.guard-mask-triangular-mobile, .guard-mask-round-mobile {
  width: 20px;
  height: 20px;
}

.guard-mask-triangular::before, .guard-mask-triangular-mobile::before {
  content: '';
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-bottom: 12px solid #fff;
  position: absolute;
}

.guard-mask-triangular-mobile::before {
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-bottom: 8px solid #fff;
}

.guard-mask-round::before, .guard-mask-round-mobile::before {
  content: '';
  width: 12px;
  height: 12px;
  background: #fff;
  border-radius: 50%;
  position: absolute;
}

.guard-mask-round-mobile::before {
  width: 8px;
  height: 8px;
}

.guard-eye, .guard-eye-mobile {
  width: 6px;
  height: 6px;
  background: #ff0000;
  border-radius: 50%;
  position: absolute;
  box-shadow: 0 0 10px #ff0000, 0 0 20px #ff0000, 0 0 30px #ff0000;
  animation: eye-glow 2s infinite alternate;
}

.guard-eye-mobile {
  width: 4px;
  height: 4px;
}

@keyframes eye-glow {
  0% { box-shadow: 0 0 5px #ff0000, 0 0 10px #ff0000, 0 0 15px #ff0000; }
  100% { box-shadow: 0 0 10px #ff0000, 0 0 20px #ff0000, 0 0 30px #ff0000; }
}

/* Small Guards for NFC Overlay */
.guard-figure-small {
  width: 30px;
  height: 40px;
}

.guard-body-small {
  width: 100%;
  height: 100%;
  background: #ff0055;
  border-radius: 4px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.guard-mask-triangular-small, .guard-mask-round-small {
  width: 15px;
  height: 15px;
  background: #000;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.guard-mask-triangular-small::before {
  content: '';
  width: 0;
  height: 0;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-bottom: 6px solid #fff;
  position: absolute;
}

.guard-mask-round-small::before {
  content: '';
  width: 6px;
  height: 6px;
  background: #fff;
  border-radius: 50%;
  position: absolute;
}

.guard-eye-small {
  width: 3px;
  height: 3px;
  background: #ff0000;
  border-radius: 50%;
  position: absolute;
  box-shadow: 0 0 5px #ff0000, 0 0 10px #ff0000;
  animation: eye-glow 2s infinite alternate;
}

/* Responsive Design */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

@media (max-width: 768px) {
  .guard-figure {
    width: 40px;
    height: 60px;
  }
  
  .guard-body {
    border-radius: 6px;
  }
  
  .guard-mask-triangular, .guard-mask-round {
    width: 20px;
    height: 20px;
  }
}
