var xp=e=>{throw TypeError(e)};var Qu=(e,t,n)=>t.has(e)||xp("Cannot "+n);var N=(e,t,n)=>(Qu(e,t,"read from private field"),n?n.call(e):t.get(e)),he=(e,t,n)=>t.has(e)?xp("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,n),ae=(e,t,n,r)=>(Qu(e,t,"write to private field"),r?r.call(e,n):t.set(e,n),n),Ye=(e,t,n)=>(Qu(e,t,"access private method"),n);var va=(e,t,n,r)=>({set _(s){ae(e,t,s,n)},get _(){return N(e,t,r)}});function WS(e,t){for(var n=0;n<t.length;n++){const r=t[n];if(typeof r!="string"&&!Array.isArray(r)){for(const s in r)if(s!=="default"&&!(s in e)){const i=Object.getOwnPropertyDescriptor(r,s);i&&Object.defineProperty(e,s,i.get?i:{enumerable:!0,get:()=>r[s]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))r(s);new MutationObserver(s=>{for(const i of s)if(i.type==="childList")for(const o of i.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&r(o)}).observe(document,{childList:!0,subtree:!0});function n(s){const i={};return s.integrity&&(i.integrity=s.integrity),s.referrerPolicy&&(i.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?i.credentials="include":s.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function r(s){if(s.ep)return;s.ep=!0;const i=n(s);fetch(s.href,i)}})();function zy(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var By={exports:{}},uu={},$y={exports:{}},ue={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var na=Symbol.for("react.element"),HS=Symbol.for("react.portal"),ZS=Symbol.for("react.fragment"),KS=Symbol.for("react.strict_mode"),qS=Symbol.for("react.profiler"),GS=Symbol.for("react.provider"),QS=Symbol.for("react.context"),YS=Symbol.for("react.forward_ref"),XS=Symbol.for("react.suspense"),JS=Symbol.for("react.memo"),e_=Symbol.for("react.lazy"),wp=Symbol.iterator;function t_(e){return e===null||typeof e!="object"?null:(e=wp&&e[wp]||e["@@iterator"],typeof e=="function"?e:null)}var Uy={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Wy=Object.assign,Hy={};function Ti(e,t,n){this.props=e,this.context=t,this.refs=Hy,this.updater=n||Uy}Ti.prototype.isReactComponent={};Ti.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};Ti.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function Zy(){}Zy.prototype=Ti.prototype;function xf(e,t,n){this.props=e,this.context=t,this.refs=Hy,this.updater=n||Uy}var wf=xf.prototype=new Zy;wf.constructor=xf;Wy(wf,Ti.prototype);wf.isPureReactComponent=!0;var Sp=Array.isArray,Ky=Object.prototype.hasOwnProperty,Sf={current:null},qy={key:!0,ref:!0,__self:!0,__source:!0};function Gy(e,t,n){var r,s={},i=null,o=null;if(t!=null)for(r in t.ref!==void 0&&(o=t.ref),t.key!==void 0&&(i=""+t.key),t)Ky.call(t,r)&&!qy.hasOwnProperty(r)&&(s[r]=t[r]);var a=arguments.length-2;if(a===1)s.children=n;else if(1<a){for(var l=Array(a),u=0;u<a;u++)l[u]=arguments[u+2];s.children=l}if(e&&e.defaultProps)for(r in a=e.defaultProps,a)s[r]===void 0&&(s[r]=a[r]);return{$$typeof:na,type:e,key:i,ref:o,props:s,_owner:Sf.current}}function n_(e,t){return{$$typeof:na,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function _f(e){return typeof e=="object"&&e!==null&&e.$$typeof===na}function r_(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var _p=/\/+/g;function Yu(e,t){return typeof e=="object"&&e!==null&&e.key!=null?r_(""+e.key):t.toString(36)}function Wa(e,t,n,r,s){var i=typeof e;(i==="undefined"||i==="boolean")&&(e=null);var o=!1;if(e===null)o=!0;else switch(i){case"string":case"number":o=!0;break;case"object":switch(e.$$typeof){case na:case HS:o=!0}}if(o)return o=e,s=s(o),e=r===""?"."+Yu(o,0):r,Sp(s)?(n="",e!=null&&(n=e.replace(_p,"$&/")+"/"),Wa(s,t,n,"",function(u){return u})):s!=null&&(_f(s)&&(s=n_(s,n+(!s.key||o&&o.key===s.key?"":(""+s.key).replace(_p,"$&/")+"/")+e)),t.push(s)),1;if(o=0,r=r===""?".":r+":",Sp(e))for(var a=0;a<e.length;a++){i=e[a];var l=r+Yu(i,a);o+=Wa(i,t,n,l,s)}else if(l=t_(e),typeof l=="function")for(e=l.call(e),a=0;!(i=e.next()).done;)i=i.value,l=r+Yu(i,a++),o+=Wa(i,t,n,l,s);else if(i==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return o}function xa(e,t,n){if(e==null)return e;var r=[],s=0;return Wa(e,r,"","",function(i){return t.call(n,i,s++)}),r}function s_(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var pt={current:null},Ha={transition:null},i_={ReactCurrentDispatcher:pt,ReactCurrentBatchConfig:Ha,ReactCurrentOwner:Sf};function Qy(){throw Error("act(...) is not supported in production builds of React.")}ue.Children={map:xa,forEach:function(e,t,n){xa(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return xa(e,function(){t++}),t},toArray:function(e){return xa(e,function(t){return t})||[]},only:function(e){if(!_f(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};ue.Component=Ti;ue.Fragment=ZS;ue.Profiler=qS;ue.PureComponent=xf;ue.StrictMode=KS;ue.Suspense=XS;ue.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=i_;ue.act=Qy;ue.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=Wy({},e.props),s=e.key,i=e.ref,o=e._owner;if(t!=null){if(t.ref!==void 0&&(i=t.ref,o=Sf.current),t.key!==void 0&&(s=""+t.key),e.type&&e.type.defaultProps)var a=e.type.defaultProps;for(l in t)Ky.call(t,l)&&!qy.hasOwnProperty(l)&&(r[l]=t[l]===void 0&&a!==void 0?a[l]:t[l])}var l=arguments.length-2;if(l===1)r.children=n;else if(1<l){a=Array(l);for(var u=0;u<l;u++)a[u]=arguments[u+2];r.children=a}return{$$typeof:na,type:e.type,key:s,ref:i,props:r,_owner:o}};ue.createContext=function(e){return e={$$typeof:QS,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:GS,_context:e},e.Consumer=e};ue.createElement=Gy;ue.createFactory=function(e){var t=Gy.bind(null,e);return t.type=e,t};ue.createRef=function(){return{current:null}};ue.forwardRef=function(e){return{$$typeof:YS,render:e}};ue.isValidElement=_f;ue.lazy=function(e){return{$$typeof:e_,_payload:{_status:-1,_result:e},_init:s_}};ue.memo=function(e,t){return{$$typeof:JS,type:e,compare:t===void 0?null:t}};ue.startTransition=function(e){var t=Ha.transition;Ha.transition={};try{e()}finally{Ha.transition=t}};ue.unstable_act=Qy;ue.useCallback=function(e,t){return pt.current.useCallback(e,t)};ue.useContext=function(e){return pt.current.useContext(e)};ue.useDebugValue=function(){};ue.useDeferredValue=function(e){return pt.current.useDeferredValue(e)};ue.useEffect=function(e,t){return pt.current.useEffect(e,t)};ue.useId=function(){return pt.current.useId()};ue.useImperativeHandle=function(e,t,n){return pt.current.useImperativeHandle(e,t,n)};ue.useInsertionEffect=function(e,t){return pt.current.useInsertionEffect(e,t)};ue.useLayoutEffect=function(e,t){return pt.current.useLayoutEffect(e,t)};ue.useMemo=function(e,t){return pt.current.useMemo(e,t)};ue.useReducer=function(e,t,n){return pt.current.useReducer(e,t,n)};ue.useRef=function(e){return pt.current.useRef(e)};ue.useState=function(e){return pt.current.useState(e)};ue.useSyncExternalStore=function(e,t,n){return pt.current.useSyncExternalStore(e,t,n)};ue.useTransition=function(){return pt.current.useTransition()};ue.version="18.3.1";$y.exports=ue;var x=$y.exports;const se=zy(x),o_=WS({__proto__:null,default:se},[x]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var a_=x,l_=Symbol.for("react.element"),u_=Symbol.for("react.fragment"),c_=Object.prototype.hasOwnProperty,d_=a_.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,f_={key:!0,ref:!0,__self:!0,__source:!0};function Yy(e,t,n){var r,s={},i=null,o=null;n!==void 0&&(i=""+n),t.key!==void 0&&(i=""+t.key),t.ref!==void 0&&(o=t.ref);for(r in t)c_.call(t,r)&&!f_.hasOwnProperty(r)&&(s[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)s[r]===void 0&&(s[r]=t[r]);return{$$typeof:l_,type:e,key:i,ref:o,props:s,_owner:d_.current}}uu.Fragment=u_;uu.jsx=Yy;uu.jsxs=Yy;By.exports=uu;var S=By.exports,Xy={exports:{}},Ot={},Jy={exports:{}},ev={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(A,I){var K=A.length;A.push(I);e:for(;0<K;){var re=K-1>>>1,ye=A[re];if(0<s(ye,I))A[re]=I,A[K]=ye,K=re;else break e}}function n(A){return A.length===0?null:A[0]}function r(A){if(A.length===0)return null;var I=A[0],K=A.pop();if(K!==I){A[0]=K;e:for(var re=0,ye=A.length,yt=ye>>>1;re<yt;){var vt=2*(re+1)-1,Xn=A[vt],Ht=vt+1,dn=A[Ht];if(0>s(Xn,K))Ht<ye&&0>s(dn,Xn)?(A[re]=dn,A[Ht]=K,re=Ht):(A[re]=Xn,A[vt]=K,re=vt);else if(Ht<ye&&0>s(dn,K))A[re]=dn,A[Ht]=K,re=Ht;else break e}}return I}function s(A,I){var K=A.sortIndex-I.sortIndex;return K!==0?K:A.id-I.id}if(typeof performance=="object"&&typeof performance.now=="function"){var i=performance;e.unstable_now=function(){return i.now()}}else{var o=Date,a=o.now();e.unstable_now=function(){return o.now()-a}}var l=[],u=[],c=1,d=null,f=3,g=!1,v=!1,y=!1,w=typeof setTimeout=="function"?setTimeout:null,p=typeof clearTimeout=="function"?clearTimeout:null,h=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function m(A){for(var I=n(u);I!==null;){if(I.callback===null)r(u);else if(I.startTime<=A)r(u),I.sortIndex=I.expirationTime,t(l,I);else break;I=n(u)}}function _(A){if(y=!1,m(A),!v)if(n(l)!==null)v=!0,X(C);else{var I=n(u);I!==null&&Q(_,I.startTime-A)}}function C(A,I){v=!1,y&&(y=!1,p(b),b=-1),g=!0;var K=f;try{for(m(I),d=n(l);d!==null&&(!(d.expirationTime>I)||A&&!Y());){var re=d.callback;if(typeof re=="function"){d.callback=null,f=d.priorityLevel;var ye=re(d.expirationTime<=I);I=e.unstable_now(),typeof ye=="function"?d.callback=ye:d===n(l)&&r(l),m(I)}else r(l);d=n(l)}if(d!==null)var yt=!0;else{var vt=n(u);vt!==null&&Q(_,vt.startTime-I),yt=!1}return yt}finally{d=null,f=K,g=!1}}var k=!1,P=null,b=-1,F=5,j=-1;function Y(){return!(e.unstable_now()-j<F)}function L(){if(P!==null){var A=e.unstable_now();j=A;var I=!0;try{I=P(!0,A)}finally{I?U():(k=!1,P=null)}}else k=!1}var U;if(typeof h=="function")U=function(){h(L)};else if(typeof MessageChannel<"u"){var O=new MessageChannel,ne=O.port2;O.port1.onmessage=L,U=function(){ne.postMessage(null)}}else U=function(){w(L,0)};function X(A){P=A,k||(k=!0,U())}function Q(A,I){b=w(function(){A(e.unstable_now())},I)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(A){A.callback=null},e.unstable_continueExecution=function(){v||g||(v=!0,X(C))},e.unstable_forceFrameRate=function(A){0>A||125<A?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):F=0<A?Math.floor(1e3/A):5},e.unstable_getCurrentPriorityLevel=function(){return f},e.unstable_getFirstCallbackNode=function(){return n(l)},e.unstable_next=function(A){switch(f){case 1:case 2:case 3:var I=3;break;default:I=f}var K=f;f=I;try{return A()}finally{f=K}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(A,I){switch(A){case 1:case 2:case 3:case 4:case 5:break;default:A=3}var K=f;f=A;try{return I()}finally{f=K}},e.unstable_scheduleCallback=function(A,I,K){var re=e.unstable_now();switch(typeof K=="object"&&K!==null?(K=K.delay,K=typeof K=="number"&&0<K?re+K:re):K=re,A){case 1:var ye=-1;break;case 2:ye=250;break;case 5:ye=**********;break;case 4:ye=1e4;break;default:ye=5e3}return ye=K+ye,A={id:c++,callback:I,priorityLevel:A,startTime:K,expirationTime:ye,sortIndex:-1},K>re?(A.sortIndex=K,t(u,A),n(l)===null&&A===n(u)&&(y?(p(b),b=-1):y=!0,Q(_,K-re))):(A.sortIndex=ye,t(l,A),v||g||(v=!0,X(C))),A},e.unstable_shouldYield=Y,e.unstable_wrapCallback=function(A){var I=f;return function(){var K=f;f=I;try{return A.apply(this,arguments)}finally{f=K}}}})(ev);Jy.exports=ev;var h_=Jy.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var p_=x,Mt=h_;function D(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var tv=new Set,go={};function gs(e,t){ai(e,t),ai(e+"Capture",t)}function ai(e,t){for(go[e]=t,e=0;e<t.length;e++)tv.add(t[e])}var Un=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Kc=Object.prototype.hasOwnProperty,m_=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Tp={},Cp={};function g_(e){return Kc.call(Cp,e)?!0:Kc.call(Tp,e)?!1:m_.test(e)?Cp[e]=!0:(Tp[e]=!0,!1)}function y_(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function v_(e,t,n,r){if(t===null||typeof t>"u"||y_(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function mt(e,t,n,r,s,i,o){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=s,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=o}var Ge={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){Ge[e]=new mt(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];Ge[t]=new mt(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){Ge[e]=new mt(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){Ge[e]=new mt(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){Ge[e]=new mt(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){Ge[e]=new mt(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){Ge[e]=new mt(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){Ge[e]=new mt(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){Ge[e]=new mt(e,5,!1,e.toLowerCase(),null,!1,!1)});var Tf=/[\-:]([a-z])/g;function Cf(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Tf,Cf);Ge[t]=new mt(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Tf,Cf);Ge[t]=new mt(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Tf,Cf);Ge[t]=new mt(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){Ge[e]=new mt(e,1,!1,e.toLowerCase(),null,!1,!1)});Ge.xlinkHref=new mt("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){Ge[e]=new mt(e,1,!1,e.toLowerCase(),null,!0,!0)});function kf(e,t,n,r){var s=Ge.hasOwnProperty(t)?Ge[t]:null;(s!==null?s.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(v_(t,n,s,r)&&(n=null),r||s===null?g_(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):s.mustUseProperty?e[s.propertyName]=n===null?s.type===3?!1:"":n:(t=s.attributeName,r=s.attributeNamespace,n===null?e.removeAttribute(t):(s=s.type,n=s===3||s===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var Yn=p_.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,wa=Symbol.for("react.element"),ks=Symbol.for("react.portal"),bs=Symbol.for("react.fragment"),bf=Symbol.for("react.strict_mode"),qc=Symbol.for("react.profiler"),nv=Symbol.for("react.provider"),rv=Symbol.for("react.context"),Ef=Symbol.for("react.forward_ref"),Gc=Symbol.for("react.suspense"),Qc=Symbol.for("react.suspense_list"),Pf=Symbol.for("react.memo"),sr=Symbol.for("react.lazy"),sv=Symbol.for("react.offscreen"),kp=Symbol.iterator;function Di(e){return e===null||typeof e!="object"?null:(e=kp&&e[kp]||e["@@iterator"],typeof e=="function"?e:null)}var Pe=Object.assign,Xu;function Zi(e){if(Xu===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Xu=t&&t[1]||""}return`
`+Xu+e}var Ju=!1;function ec(e,t){if(!e||Ju)return"";Ju=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var s=u.stack.split(`
`),i=r.stack.split(`
`),o=s.length-1,a=i.length-1;1<=o&&0<=a&&s[o]!==i[a];)a--;for(;1<=o&&0<=a;o--,a--)if(s[o]!==i[a]){if(o!==1||a!==1)do if(o--,a--,0>a||s[o]!==i[a]){var l=`
`+s[o].replace(" at new "," at ");return e.displayName&&l.includes("<anonymous>")&&(l=l.replace("<anonymous>",e.displayName)),l}while(1<=o&&0<=a);break}}}finally{Ju=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?Zi(e):""}function x_(e){switch(e.tag){case 5:return Zi(e.type);case 16:return Zi("Lazy");case 13:return Zi("Suspense");case 19:return Zi("SuspenseList");case 0:case 2:case 15:return e=ec(e.type,!1),e;case 11:return e=ec(e.type.render,!1),e;case 1:return e=ec(e.type,!0),e;default:return""}}function Yc(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case bs:return"Fragment";case ks:return"Portal";case qc:return"Profiler";case bf:return"StrictMode";case Gc:return"Suspense";case Qc:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case rv:return(e.displayName||"Context")+".Consumer";case nv:return(e._context.displayName||"Context")+".Provider";case Ef:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Pf:return t=e.displayName||null,t!==null?t:Yc(e.type)||"Memo";case sr:t=e._payload,e=e._init;try{return Yc(e(t))}catch{}}return null}function w_(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return Yc(t);case 8:return t===bf?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function br(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function iv(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function S_(e){var t=iv(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var s=n.get,i=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return s.call(this)},set:function(o){r=""+o,i.call(this,o)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(o){r=""+o},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Sa(e){e._valueTracker||(e._valueTracker=S_(e))}function ov(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=iv(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function hl(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Xc(e,t){var n=t.checked;return Pe({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function bp(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=br(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function av(e,t){t=t.checked,t!=null&&kf(e,"checked",t,!1)}function Jc(e,t){av(e,t);var n=br(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?ed(e,t.type,n):t.hasOwnProperty("defaultValue")&&ed(e,t.type,br(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Ep(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function ed(e,t,n){(t!=="number"||hl(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var Ki=Array.isArray;function Us(e,t,n,r){if(e=e.options,t){t={};for(var s=0;s<n.length;s++)t["$"+n[s]]=!0;for(n=0;n<e.length;n++)s=t.hasOwnProperty("$"+e[n].value),e[n].selected!==s&&(e[n].selected=s),s&&r&&(e[n].defaultSelected=!0)}else{for(n=""+br(n),t=null,s=0;s<e.length;s++){if(e[s].value===n){e[s].selected=!0,r&&(e[s].defaultSelected=!0);return}t!==null||e[s].disabled||(t=e[s])}t!==null&&(t.selected=!0)}}function td(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(D(91));return Pe({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Pp(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(D(92));if(Ki(n)){if(1<n.length)throw Error(D(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:br(n)}}function lv(e,t){var n=br(t.value),r=br(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function Ap(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function uv(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function nd(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?uv(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var _a,cv=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,s){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,s)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(_a=_a||document.createElement("div"),_a.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=_a.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function yo(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var eo={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},__=["Webkit","ms","Moz","O"];Object.keys(eo).forEach(function(e){__.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),eo[t]=eo[e]})});function dv(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||eo.hasOwnProperty(e)&&eo[e]?(""+t).trim():t+"px"}function fv(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,s=dv(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,s):e[n]=s}}var T_=Pe({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function rd(e,t){if(t){if(T_[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(D(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(D(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(D(61))}if(t.style!=null&&typeof t.style!="object")throw Error(D(62))}}function sd(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var id=null;function Af(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var od=null,Ws=null,Hs=null;function Rp(e){if(e=ia(e)){if(typeof od!="function")throw Error(D(280));var t=e.stateNode;t&&(t=pu(t),od(e.stateNode,e.type,t))}}function hv(e){Ws?Hs?Hs.push(e):Hs=[e]:Ws=e}function pv(){if(Ws){var e=Ws,t=Hs;if(Hs=Ws=null,Rp(e),t)for(e=0;e<t.length;e++)Rp(t[e])}}function mv(e,t){return e(t)}function gv(){}var tc=!1;function yv(e,t,n){if(tc)return e(t,n);tc=!0;try{return mv(e,t,n)}finally{tc=!1,(Ws!==null||Hs!==null)&&(gv(),pv())}}function vo(e,t){var n=e.stateNode;if(n===null)return null;var r=pu(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(D(231,t,typeof n));return n}var ad=!1;if(Un)try{var Oi={};Object.defineProperty(Oi,"passive",{get:function(){ad=!0}}),window.addEventListener("test",Oi,Oi),window.removeEventListener("test",Oi,Oi)}catch{ad=!1}function C_(e,t,n,r,s,i,o,a,l){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(c){this.onError(c)}}var to=!1,pl=null,ml=!1,ld=null,k_={onError:function(e){to=!0,pl=e}};function b_(e,t,n,r,s,i,o,a,l){to=!1,pl=null,C_.apply(k_,arguments)}function E_(e,t,n,r,s,i,o,a,l){if(b_.apply(this,arguments),to){if(to){var u=pl;to=!1,pl=null}else throw Error(D(198));ml||(ml=!0,ld=u)}}function ys(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function vv(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Np(e){if(ys(e)!==e)throw Error(D(188))}function P_(e){var t=e.alternate;if(!t){if(t=ys(e),t===null)throw Error(D(188));return t!==e?null:e}for(var n=e,r=t;;){var s=n.return;if(s===null)break;var i=s.alternate;if(i===null){if(r=s.return,r!==null){n=r;continue}break}if(s.child===i.child){for(i=s.child;i;){if(i===n)return Np(s),e;if(i===r)return Np(s),t;i=i.sibling}throw Error(D(188))}if(n.return!==r.return)n=s,r=i;else{for(var o=!1,a=s.child;a;){if(a===n){o=!0,n=s,r=i;break}if(a===r){o=!0,r=s,n=i;break}a=a.sibling}if(!o){for(a=i.child;a;){if(a===n){o=!0,n=i,r=s;break}if(a===r){o=!0,r=i,n=s;break}a=a.sibling}if(!o)throw Error(D(189))}}if(n.alternate!==r)throw Error(D(190))}if(n.tag!==3)throw Error(D(188));return n.stateNode.current===n?e:t}function xv(e){return e=P_(e),e!==null?wv(e):null}function wv(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=wv(e);if(t!==null)return t;e=e.sibling}return null}var Sv=Mt.unstable_scheduleCallback,Mp=Mt.unstable_cancelCallback,A_=Mt.unstable_shouldYield,R_=Mt.unstable_requestPaint,Oe=Mt.unstable_now,N_=Mt.unstable_getCurrentPriorityLevel,Rf=Mt.unstable_ImmediatePriority,_v=Mt.unstable_UserBlockingPriority,gl=Mt.unstable_NormalPriority,M_=Mt.unstable_LowPriority,Tv=Mt.unstable_IdlePriority,cu=null,xn=null;function D_(e){if(xn&&typeof xn.onCommitFiberRoot=="function")try{xn.onCommitFiberRoot(cu,e,void 0,(e.current.flags&128)===128)}catch{}}var nn=Math.clz32?Math.clz32:j_,O_=Math.log,L_=Math.LN2;function j_(e){return e>>>=0,e===0?32:31-(O_(e)/L_|0)|0}var Ta=64,Ca=4194304;function qi(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function yl(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,s=e.suspendedLanes,i=e.pingedLanes,o=n&268435455;if(o!==0){var a=o&~s;a!==0?r=qi(a):(i&=o,i!==0&&(r=qi(i)))}else o=n&~s,o!==0?r=qi(o):i!==0&&(r=qi(i));if(r===0)return 0;if(t!==0&&t!==r&&!(t&s)&&(s=r&-r,i=t&-t,s>=i||s===16&&(i&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-nn(t),s=1<<n,r|=e[n],t&=~s;return r}function V_(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function F_(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,s=e.expirationTimes,i=e.pendingLanes;0<i;){var o=31-nn(i),a=1<<o,l=s[o];l===-1?(!(a&n)||a&r)&&(s[o]=V_(a,t)):l<=t&&(e.expiredLanes|=a),i&=~a}}function ud(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Cv(){var e=Ta;return Ta<<=1,!(Ta&4194240)&&(Ta=64),e}function nc(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function ra(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-nn(t),e[t]=n}function I_(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var s=31-nn(n),i=1<<s;t[s]=0,r[s]=-1,e[s]=-1,n&=~i}}function Nf(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-nn(n),s=1<<r;s&t|e[r]&t&&(e[r]|=t),n&=~s}}var me=0;function kv(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var bv,Mf,Ev,Pv,Av,cd=!1,ka=[],gr=null,yr=null,vr=null,xo=new Map,wo=new Map,or=[],z_="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Dp(e,t){switch(e){case"focusin":case"focusout":gr=null;break;case"dragenter":case"dragleave":yr=null;break;case"mouseover":case"mouseout":vr=null;break;case"pointerover":case"pointerout":xo.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":wo.delete(t.pointerId)}}function Li(e,t,n,r,s,i){return e===null||e.nativeEvent!==i?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:i,targetContainers:[s]},t!==null&&(t=ia(t),t!==null&&Mf(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,s!==null&&t.indexOf(s)===-1&&t.push(s),e)}function B_(e,t,n,r,s){switch(t){case"focusin":return gr=Li(gr,e,t,n,r,s),!0;case"dragenter":return yr=Li(yr,e,t,n,r,s),!0;case"mouseover":return vr=Li(vr,e,t,n,r,s),!0;case"pointerover":var i=s.pointerId;return xo.set(i,Li(xo.get(i)||null,e,t,n,r,s)),!0;case"gotpointercapture":return i=s.pointerId,wo.set(i,Li(wo.get(i)||null,e,t,n,r,s)),!0}return!1}function Rv(e){var t=Kr(e.target);if(t!==null){var n=ys(t);if(n!==null){if(t=n.tag,t===13){if(t=vv(n),t!==null){e.blockedOn=t,Av(e.priority,function(){Ev(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Za(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=dd(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);id=r,n.target.dispatchEvent(r),id=null}else return t=ia(n),t!==null&&Mf(t),e.blockedOn=n,!1;t.shift()}return!0}function Op(e,t,n){Za(e)&&n.delete(t)}function $_(){cd=!1,gr!==null&&Za(gr)&&(gr=null),yr!==null&&Za(yr)&&(yr=null),vr!==null&&Za(vr)&&(vr=null),xo.forEach(Op),wo.forEach(Op)}function ji(e,t){e.blockedOn===t&&(e.blockedOn=null,cd||(cd=!0,Mt.unstable_scheduleCallback(Mt.unstable_NormalPriority,$_)))}function So(e){function t(s){return ji(s,e)}if(0<ka.length){ji(ka[0],e);for(var n=1;n<ka.length;n++){var r=ka[n];r.blockedOn===e&&(r.blockedOn=null)}}for(gr!==null&&ji(gr,e),yr!==null&&ji(yr,e),vr!==null&&ji(vr,e),xo.forEach(t),wo.forEach(t),n=0;n<or.length;n++)r=or[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<or.length&&(n=or[0],n.blockedOn===null);)Rv(n),n.blockedOn===null&&or.shift()}var Zs=Yn.ReactCurrentBatchConfig,vl=!0;function U_(e,t,n,r){var s=me,i=Zs.transition;Zs.transition=null;try{me=1,Df(e,t,n,r)}finally{me=s,Zs.transition=i}}function W_(e,t,n,r){var s=me,i=Zs.transition;Zs.transition=null;try{me=4,Df(e,t,n,r)}finally{me=s,Zs.transition=i}}function Df(e,t,n,r){if(vl){var s=dd(e,t,n,r);if(s===null)fc(e,t,r,xl,n),Dp(e,r);else if(B_(s,e,t,n,r))r.stopPropagation();else if(Dp(e,r),t&4&&-1<z_.indexOf(e)){for(;s!==null;){var i=ia(s);if(i!==null&&bv(i),i=dd(e,t,n,r),i===null&&fc(e,t,r,xl,n),i===s)break;s=i}s!==null&&r.stopPropagation()}else fc(e,t,r,null,n)}}var xl=null;function dd(e,t,n,r){if(xl=null,e=Af(r),e=Kr(e),e!==null)if(t=ys(e),t===null)e=null;else if(n=t.tag,n===13){if(e=vv(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return xl=e,null}function Nv(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(N_()){case Rf:return 1;case _v:return 4;case gl:case M_:return 16;case Tv:return 536870912;default:return 16}default:return 16}}var pr=null,Of=null,Ka=null;function Mv(){if(Ka)return Ka;var e,t=Of,n=t.length,r,s="value"in pr?pr.value:pr.textContent,i=s.length;for(e=0;e<n&&t[e]===s[e];e++);var o=n-e;for(r=1;r<=o&&t[n-r]===s[i-r];r++);return Ka=s.slice(e,1<r?1-r:void 0)}function qa(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function ba(){return!0}function Lp(){return!1}function Lt(e){function t(n,r,s,i,o){this._reactName=n,this._targetInst=s,this.type=r,this.nativeEvent=i,this.target=o,this.currentTarget=null;for(var a in e)e.hasOwnProperty(a)&&(n=e[a],this[a]=n?n(i):i[a]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?ba:Lp,this.isPropagationStopped=Lp,this}return Pe(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=ba)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=ba)},persist:function(){},isPersistent:ba}),t}var Ci={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Lf=Lt(Ci),sa=Pe({},Ci,{view:0,detail:0}),H_=Lt(sa),rc,sc,Vi,du=Pe({},sa,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:jf,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Vi&&(Vi&&e.type==="mousemove"?(rc=e.screenX-Vi.screenX,sc=e.screenY-Vi.screenY):sc=rc=0,Vi=e),rc)},movementY:function(e){return"movementY"in e?e.movementY:sc}}),jp=Lt(du),Z_=Pe({},du,{dataTransfer:0}),K_=Lt(Z_),q_=Pe({},sa,{relatedTarget:0}),ic=Lt(q_),G_=Pe({},Ci,{animationName:0,elapsedTime:0,pseudoElement:0}),Q_=Lt(G_),Y_=Pe({},Ci,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),X_=Lt(Y_),J_=Pe({},Ci,{data:0}),Vp=Lt(J_),eT={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},tT={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},nT={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function rT(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=nT[e])?!!t[e]:!1}function jf(){return rT}var sT=Pe({},sa,{key:function(e){if(e.key){var t=eT[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=qa(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?tT[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:jf,charCode:function(e){return e.type==="keypress"?qa(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?qa(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),iT=Lt(sT),oT=Pe({},du,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Fp=Lt(oT),aT=Pe({},sa,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:jf}),lT=Lt(aT),uT=Pe({},Ci,{propertyName:0,elapsedTime:0,pseudoElement:0}),cT=Lt(uT),dT=Pe({},du,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),fT=Lt(dT),hT=[9,13,27,32],Vf=Un&&"CompositionEvent"in window,no=null;Un&&"documentMode"in document&&(no=document.documentMode);var pT=Un&&"TextEvent"in window&&!no,Dv=Un&&(!Vf||no&&8<no&&11>=no),Ip=" ",zp=!1;function Ov(e,t){switch(e){case"keyup":return hT.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Lv(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Es=!1;function mT(e,t){switch(e){case"compositionend":return Lv(t);case"keypress":return t.which!==32?null:(zp=!0,Ip);case"textInput":return e=t.data,e===Ip&&zp?null:e;default:return null}}function gT(e,t){if(Es)return e==="compositionend"||!Vf&&Ov(e,t)?(e=Mv(),Ka=Of=pr=null,Es=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Dv&&t.locale!=="ko"?null:t.data;default:return null}}var yT={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Bp(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!yT[e.type]:t==="textarea"}function jv(e,t,n,r){hv(r),t=wl(t,"onChange"),0<t.length&&(n=new Lf("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var ro=null,_o=null;function vT(e){Kv(e,0)}function fu(e){var t=Rs(e);if(ov(t))return e}function xT(e,t){if(e==="change")return t}var Vv=!1;if(Un){var oc;if(Un){var ac="oninput"in document;if(!ac){var $p=document.createElement("div");$p.setAttribute("oninput","return;"),ac=typeof $p.oninput=="function"}oc=ac}else oc=!1;Vv=oc&&(!document.documentMode||9<document.documentMode)}function Up(){ro&&(ro.detachEvent("onpropertychange",Fv),_o=ro=null)}function Fv(e){if(e.propertyName==="value"&&fu(_o)){var t=[];jv(t,_o,e,Af(e)),yv(vT,t)}}function wT(e,t,n){e==="focusin"?(Up(),ro=t,_o=n,ro.attachEvent("onpropertychange",Fv)):e==="focusout"&&Up()}function ST(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return fu(_o)}function _T(e,t){if(e==="click")return fu(t)}function TT(e,t){if(e==="input"||e==="change")return fu(t)}function CT(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var on=typeof Object.is=="function"?Object.is:CT;function To(e,t){if(on(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var s=n[r];if(!Kc.call(t,s)||!on(e[s],t[s]))return!1}return!0}function Wp(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Hp(e,t){var n=Wp(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Wp(n)}}function Iv(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Iv(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function zv(){for(var e=window,t=hl();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=hl(e.document)}return t}function Ff(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function kT(e){var t=zv(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&Iv(n.ownerDocument.documentElement,n)){if(r!==null&&Ff(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var s=n.textContent.length,i=Math.min(r.start,s);r=r.end===void 0?i:Math.min(r.end,s),!e.extend&&i>r&&(s=r,r=i,i=s),s=Hp(n,i);var o=Hp(n,r);s&&o&&(e.rangeCount!==1||e.anchorNode!==s.node||e.anchorOffset!==s.offset||e.focusNode!==o.node||e.focusOffset!==o.offset)&&(t=t.createRange(),t.setStart(s.node,s.offset),e.removeAllRanges(),i>r?(e.addRange(t),e.extend(o.node,o.offset)):(t.setEnd(o.node,o.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var bT=Un&&"documentMode"in document&&11>=document.documentMode,Ps=null,fd=null,so=null,hd=!1;function Zp(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;hd||Ps==null||Ps!==hl(r)||(r=Ps,"selectionStart"in r&&Ff(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),so&&To(so,r)||(so=r,r=wl(fd,"onSelect"),0<r.length&&(t=new Lf("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Ps)))}function Ea(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var As={animationend:Ea("Animation","AnimationEnd"),animationiteration:Ea("Animation","AnimationIteration"),animationstart:Ea("Animation","AnimationStart"),transitionend:Ea("Transition","TransitionEnd")},lc={},Bv={};Un&&(Bv=document.createElement("div").style,"AnimationEvent"in window||(delete As.animationend.animation,delete As.animationiteration.animation,delete As.animationstart.animation),"TransitionEvent"in window||delete As.transitionend.transition);function hu(e){if(lc[e])return lc[e];if(!As[e])return e;var t=As[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Bv)return lc[e]=t[n];return e}var $v=hu("animationend"),Uv=hu("animationiteration"),Wv=hu("animationstart"),Hv=hu("transitionend"),Zv=new Map,Kp="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Vr(e,t){Zv.set(e,t),gs(t,[e])}for(var uc=0;uc<Kp.length;uc++){var cc=Kp[uc],ET=cc.toLowerCase(),PT=cc[0].toUpperCase()+cc.slice(1);Vr(ET,"on"+PT)}Vr($v,"onAnimationEnd");Vr(Uv,"onAnimationIteration");Vr(Wv,"onAnimationStart");Vr("dblclick","onDoubleClick");Vr("focusin","onFocus");Vr("focusout","onBlur");Vr(Hv,"onTransitionEnd");ai("onMouseEnter",["mouseout","mouseover"]);ai("onMouseLeave",["mouseout","mouseover"]);ai("onPointerEnter",["pointerout","pointerover"]);ai("onPointerLeave",["pointerout","pointerover"]);gs("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));gs("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));gs("onBeforeInput",["compositionend","keypress","textInput","paste"]);gs("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));gs("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));gs("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Gi="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),AT=new Set("cancel close invalid load scroll toggle".split(" ").concat(Gi));function qp(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,E_(r,t,void 0,e),e.currentTarget=null}function Kv(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],s=r.event;r=r.listeners;e:{var i=void 0;if(t)for(var o=r.length-1;0<=o;o--){var a=r[o],l=a.instance,u=a.currentTarget;if(a=a.listener,l!==i&&s.isPropagationStopped())break e;qp(s,a,u),i=l}else for(o=0;o<r.length;o++){if(a=r[o],l=a.instance,u=a.currentTarget,a=a.listener,l!==i&&s.isPropagationStopped())break e;qp(s,a,u),i=l}}}if(ml)throw e=ld,ml=!1,ld=null,e}function Se(e,t){var n=t[vd];n===void 0&&(n=t[vd]=new Set);var r=e+"__bubble";n.has(r)||(qv(t,e,2,!1),n.add(r))}function dc(e,t,n){var r=0;t&&(r|=4),qv(n,e,r,t)}var Pa="_reactListening"+Math.random().toString(36).slice(2);function Co(e){if(!e[Pa]){e[Pa]=!0,tv.forEach(function(n){n!=="selectionchange"&&(AT.has(n)||dc(n,!1,e),dc(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Pa]||(t[Pa]=!0,dc("selectionchange",!1,t))}}function qv(e,t,n,r){switch(Nv(t)){case 1:var s=U_;break;case 4:s=W_;break;default:s=Df}n=s.bind(null,t,n,e),s=void 0,!ad||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(s=!0),r?s!==void 0?e.addEventListener(t,n,{capture:!0,passive:s}):e.addEventListener(t,n,!0):s!==void 0?e.addEventListener(t,n,{passive:s}):e.addEventListener(t,n,!1)}function fc(e,t,n,r,s){var i=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var o=r.tag;if(o===3||o===4){var a=r.stateNode.containerInfo;if(a===s||a.nodeType===8&&a.parentNode===s)break;if(o===4)for(o=r.return;o!==null;){var l=o.tag;if((l===3||l===4)&&(l=o.stateNode.containerInfo,l===s||l.nodeType===8&&l.parentNode===s))return;o=o.return}for(;a!==null;){if(o=Kr(a),o===null)return;if(l=o.tag,l===5||l===6){r=i=o;continue e}a=a.parentNode}}r=r.return}yv(function(){var u=i,c=Af(n),d=[];e:{var f=Zv.get(e);if(f!==void 0){var g=Lf,v=e;switch(e){case"keypress":if(qa(n)===0)break e;case"keydown":case"keyup":g=iT;break;case"focusin":v="focus",g=ic;break;case"focusout":v="blur",g=ic;break;case"beforeblur":case"afterblur":g=ic;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":g=jp;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":g=K_;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":g=lT;break;case $v:case Uv:case Wv:g=Q_;break;case Hv:g=cT;break;case"scroll":g=H_;break;case"wheel":g=fT;break;case"copy":case"cut":case"paste":g=X_;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":g=Fp}var y=(t&4)!==0,w=!y&&e==="scroll",p=y?f!==null?f+"Capture":null:f;y=[];for(var h=u,m;h!==null;){m=h;var _=m.stateNode;if(m.tag===5&&_!==null&&(m=_,p!==null&&(_=vo(h,p),_!=null&&y.push(ko(h,_,m)))),w)break;h=h.return}0<y.length&&(f=new g(f,v,null,n,c),d.push({event:f,listeners:y}))}}if(!(t&7)){e:{if(f=e==="mouseover"||e==="pointerover",g=e==="mouseout"||e==="pointerout",f&&n!==id&&(v=n.relatedTarget||n.fromElement)&&(Kr(v)||v[Wn]))break e;if((g||f)&&(f=c.window===c?c:(f=c.ownerDocument)?f.defaultView||f.parentWindow:window,g?(v=n.relatedTarget||n.toElement,g=u,v=v?Kr(v):null,v!==null&&(w=ys(v),v!==w||v.tag!==5&&v.tag!==6)&&(v=null)):(g=null,v=u),g!==v)){if(y=jp,_="onMouseLeave",p="onMouseEnter",h="mouse",(e==="pointerout"||e==="pointerover")&&(y=Fp,_="onPointerLeave",p="onPointerEnter",h="pointer"),w=g==null?f:Rs(g),m=v==null?f:Rs(v),f=new y(_,h+"leave",g,n,c),f.target=w,f.relatedTarget=m,_=null,Kr(c)===u&&(y=new y(p,h+"enter",v,n,c),y.target=m,y.relatedTarget=w,_=y),w=_,g&&v)t:{for(y=g,p=v,h=0,m=y;m;m=_s(m))h++;for(m=0,_=p;_;_=_s(_))m++;for(;0<h-m;)y=_s(y),h--;for(;0<m-h;)p=_s(p),m--;for(;h--;){if(y===p||p!==null&&y===p.alternate)break t;y=_s(y),p=_s(p)}y=null}else y=null;g!==null&&Gp(d,f,g,y,!1),v!==null&&w!==null&&Gp(d,w,v,y,!0)}}e:{if(f=u?Rs(u):window,g=f.nodeName&&f.nodeName.toLowerCase(),g==="select"||g==="input"&&f.type==="file")var C=xT;else if(Bp(f))if(Vv)C=TT;else{C=ST;var k=wT}else(g=f.nodeName)&&g.toLowerCase()==="input"&&(f.type==="checkbox"||f.type==="radio")&&(C=_T);if(C&&(C=C(e,u))){jv(d,C,n,c);break e}k&&k(e,f,u),e==="focusout"&&(k=f._wrapperState)&&k.controlled&&f.type==="number"&&ed(f,"number",f.value)}switch(k=u?Rs(u):window,e){case"focusin":(Bp(k)||k.contentEditable==="true")&&(Ps=k,fd=u,so=null);break;case"focusout":so=fd=Ps=null;break;case"mousedown":hd=!0;break;case"contextmenu":case"mouseup":case"dragend":hd=!1,Zp(d,n,c);break;case"selectionchange":if(bT)break;case"keydown":case"keyup":Zp(d,n,c)}var P;if(Vf)e:{switch(e){case"compositionstart":var b="onCompositionStart";break e;case"compositionend":b="onCompositionEnd";break e;case"compositionupdate":b="onCompositionUpdate";break e}b=void 0}else Es?Ov(e,n)&&(b="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(b="onCompositionStart");b&&(Dv&&n.locale!=="ko"&&(Es||b!=="onCompositionStart"?b==="onCompositionEnd"&&Es&&(P=Mv()):(pr=c,Of="value"in pr?pr.value:pr.textContent,Es=!0)),k=wl(u,b),0<k.length&&(b=new Vp(b,e,null,n,c),d.push({event:b,listeners:k}),P?b.data=P:(P=Lv(n),P!==null&&(b.data=P)))),(P=pT?mT(e,n):gT(e,n))&&(u=wl(u,"onBeforeInput"),0<u.length&&(c=new Vp("onBeforeInput","beforeinput",null,n,c),d.push({event:c,listeners:u}),c.data=P))}Kv(d,t)})}function ko(e,t,n){return{instance:e,listener:t,currentTarget:n}}function wl(e,t){for(var n=t+"Capture",r=[];e!==null;){var s=e,i=s.stateNode;s.tag===5&&i!==null&&(s=i,i=vo(e,n),i!=null&&r.unshift(ko(e,i,s)),i=vo(e,t),i!=null&&r.push(ko(e,i,s))),e=e.return}return r}function _s(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Gp(e,t,n,r,s){for(var i=t._reactName,o=[];n!==null&&n!==r;){var a=n,l=a.alternate,u=a.stateNode;if(l!==null&&l===r)break;a.tag===5&&u!==null&&(a=u,s?(l=vo(n,i),l!=null&&o.unshift(ko(n,l,a))):s||(l=vo(n,i),l!=null&&o.push(ko(n,l,a)))),n=n.return}o.length!==0&&e.push({event:t,listeners:o})}var RT=/\r\n?/g,NT=/\u0000|\uFFFD/g;function Qp(e){return(typeof e=="string"?e:""+e).replace(RT,`
`).replace(NT,"")}function Aa(e,t,n){if(t=Qp(t),Qp(e)!==t&&n)throw Error(D(425))}function Sl(){}var pd=null,md=null;function gd(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var yd=typeof setTimeout=="function"?setTimeout:void 0,MT=typeof clearTimeout=="function"?clearTimeout:void 0,Yp=typeof Promise=="function"?Promise:void 0,DT=typeof queueMicrotask=="function"?queueMicrotask:typeof Yp<"u"?function(e){return Yp.resolve(null).then(e).catch(OT)}:yd;function OT(e){setTimeout(function(){throw e})}function hc(e,t){var n=t,r=0;do{var s=n.nextSibling;if(e.removeChild(n),s&&s.nodeType===8)if(n=s.data,n==="/$"){if(r===0){e.removeChild(s),So(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=s}while(n);So(t)}function xr(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Xp(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var ki=Math.random().toString(36).slice(2),yn="__reactFiber$"+ki,bo="__reactProps$"+ki,Wn="__reactContainer$"+ki,vd="__reactEvents$"+ki,LT="__reactListeners$"+ki,jT="__reactHandles$"+ki;function Kr(e){var t=e[yn];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Wn]||n[yn]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Xp(e);e!==null;){if(n=e[yn])return n;e=Xp(e)}return t}e=n,n=e.parentNode}return null}function ia(e){return e=e[yn]||e[Wn],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Rs(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(D(33))}function pu(e){return e[bo]||null}var xd=[],Ns=-1;function Fr(e){return{current:e}}function _e(e){0>Ns||(e.current=xd[Ns],xd[Ns]=null,Ns--)}function xe(e,t){Ns++,xd[Ns]=e.current,e.current=t}var Er={},it=Fr(Er),St=Fr(!1),os=Er;function li(e,t){var n=e.type.contextTypes;if(!n)return Er;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var s={},i;for(i in n)s[i]=t[i];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=s),s}function _t(e){return e=e.childContextTypes,e!=null}function _l(){_e(St),_e(it)}function Jp(e,t,n){if(it.current!==Er)throw Error(D(168));xe(it,t),xe(St,n)}function Gv(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var s in r)if(!(s in t))throw Error(D(108,w_(e)||"Unknown",s));return Pe({},n,r)}function Tl(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Er,os=it.current,xe(it,e),xe(St,St.current),!0}function em(e,t,n){var r=e.stateNode;if(!r)throw Error(D(169));n?(e=Gv(e,t,os),r.__reactInternalMemoizedMergedChildContext=e,_e(St),_e(it),xe(it,e)):_e(St),xe(St,n)}var Ln=null,mu=!1,pc=!1;function Qv(e){Ln===null?Ln=[e]:Ln.push(e)}function VT(e){mu=!0,Qv(e)}function Ir(){if(!pc&&Ln!==null){pc=!0;var e=0,t=me;try{var n=Ln;for(me=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}Ln=null,mu=!1}catch(s){throw Ln!==null&&(Ln=Ln.slice(e+1)),Sv(Rf,Ir),s}finally{me=t,pc=!1}}return null}var Ms=[],Ds=0,Cl=null,kl=0,It=[],zt=0,as=null,Vn=1,Fn="";function Ur(e,t){Ms[Ds++]=kl,Ms[Ds++]=Cl,Cl=e,kl=t}function Yv(e,t,n){It[zt++]=Vn,It[zt++]=Fn,It[zt++]=as,as=e;var r=Vn;e=Fn;var s=32-nn(r)-1;r&=~(1<<s),n+=1;var i=32-nn(t)+s;if(30<i){var o=s-s%5;i=(r&(1<<o)-1).toString(32),r>>=o,s-=o,Vn=1<<32-nn(t)+s|n<<s|r,Fn=i+e}else Vn=1<<i|n<<s|r,Fn=e}function If(e){e.return!==null&&(Ur(e,1),Yv(e,1,0))}function zf(e){for(;e===Cl;)Cl=Ms[--Ds],Ms[Ds]=null,kl=Ms[--Ds],Ms[Ds]=null;for(;e===as;)as=It[--zt],It[zt]=null,Fn=It[--zt],It[zt]=null,Vn=It[--zt],It[zt]=null}var At=null,Pt=null,Te=!1,Xt=null;function Xv(e,t){var n=Bt(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function tm(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,At=e,Pt=xr(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,At=e,Pt=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=as!==null?{id:Vn,overflow:Fn}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=Bt(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,At=e,Pt=null,!0):!1;default:return!1}}function wd(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Sd(e){if(Te){var t=Pt;if(t){var n=t;if(!tm(e,t)){if(wd(e))throw Error(D(418));t=xr(n.nextSibling);var r=At;t&&tm(e,t)?Xv(r,n):(e.flags=e.flags&-4097|2,Te=!1,At=e)}}else{if(wd(e))throw Error(D(418));e.flags=e.flags&-4097|2,Te=!1,At=e}}}function nm(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;At=e}function Ra(e){if(e!==At)return!1;if(!Te)return nm(e),Te=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!gd(e.type,e.memoizedProps)),t&&(t=Pt)){if(wd(e))throw Jv(),Error(D(418));for(;t;)Xv(e,t),t=xr(t.nextSibling)}if(nm(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(D(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){Pt=xr(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}Pt=null}}else Pt=At?xr(e.stateNode.nextSibling):null;return!0}function Jv(){for(var e=Pt;e;)e=xr(e.nextSibling)}function ui(){Pt=At=null,Te=!1}function Bf(e){Xt===null?Xt=[e]:Xt.push(e)}var FT=Yn.ReactCurrentBatchConfig;function Fi(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(D(309));var r=n.stateNode}if(!r)throw Error(D(147,e));var s=r,i=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===i?t.ref:(t=function(o){var a=s.refs;o===null?delete a[i]:a[i]=o},t._stringRef=i,t)}if(typeof e!="string")throw Error(D(284));if(!n._owner)throw Error(D(290,e))}return e}function Na(e,t){throw e=Object.prototype.toString.call(t),Error(D(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function rm(e){var t=e._init;return t(e._payload)}function e0(e){function t(p,h){if(e){var m=p.deletions;m===null?(p.deletions=[h],p.flags|=16):m.push(h)}}function n(p,h){if(!e)return null;for(;h!==null;)t(p,h),h=h.sibling;return null}function r(p,h){for(p=new Map;h!==null;)h.key!==null?p.set(h.key,h):p.set(h.index,h),h=h.sibling;return p}function s(p,h){return p=Tr(p,h),p.index=0,p.sibling=null,p}function i(p,h,m){return p.index=m,e?(m=p.alternate,m!==null?(m=m.index,m<h?(p.flags|=2,h):m):(p.flags|=2,h)):(p.flags|=1048576,h)}function o(p){return e&&p.alternate===null&&(p.flags|=2),p}function a(p,h,m,_){return h===null||h.tag!==6?(h=Sc(m,p.mode,_),h.return=p,h):(h=s(h,m),h.return=p,h)}function l(p,h,m,_){var C=m.type;return C===bs?c(p,h,m.props.children,_,m.key):h!==null&&(h.elementType===C||typeof C=="object"&&C!==null&&C.$$typeof===sr&&rm(C)===h.type)?(_=s(h,m.props),_.ref=Fi(p,h,m),_.return=p,_):(_=tl(m.type,m.key,m.props,null,p.mode,_),_.ref=Fi(p,h,m),_.return=p,_)}function u(p,h,m,_){return h===null||h.tag!==4||h.stateNode.containerInfo!==m.containerInfo||h.stateNode.implementation!==m.implementation?(h=_c(m,p.mode,_),h.return=p,h):(h=s(h,m.children||[]),h.return=p,h)}function c(p,h,m,_,C){return h===null||h.tag!==7?(h=rs(m,p.mode,_,C),h.return=p,h):(h=s(h,m),h.return=p,h)}function d(p,h,m){if(typeof h=="string"&&h!==""||typeof h=="number")return h=Sc(""+h,p.mode,m),h.return=p,h;if(typeof h=="object"&&h!==null){switch(h.$$typeof){case wa:return m=tl(h.type,h.key,h.props,null,p.mode,m),m.ref=Fi(p,null,h),m.return=p,m;case ks:return h=_c(h,p.mode,m),h.return=p,h;case sr:var _=h._init;return d(p,_(h._payload),m)}if(Ki(h)||Di(h))return h=rs(h,p.mode,m,null),h.return=p,h;Na(p,h)}return null}function f(p,h,m,_){var C=h!==null?h.key:null;if(typeof m=="string"&&m!==""||typeof m=="number")return C!==null?null:a(p,h,""+m,_);if(typeof m=="object"&&m!==null){switch(m.$$typeof){case wa:return m.key===C?l(p,h,m,_):null;case ks:return m.key===C?u(p,h,m,_):null;case sr:return C=m._init,f(p,h,C(m._payload),_)}if(Ki(m)||Di(m))return C!==null?null:c(p,h,m,_,null);Na(p,m)}return null}function g(p,h,m,_,C){if(typeof _=="string"&&_!==""||typeof _=="number")return p=p.get(m)||null,a(h,p,""+_,C);if(typeof _=="object"&&_!==null){switch(_.$$typeof){case wa:return p=p.get(_.key===null?m:_.key)||null,l(h,p,_,C);case ks:return p=p.get(_.key===null?m:_.key)||null,u(h,p,_,C);case sr:var k=_._init;return g(p,h,m,k(_._payload),C)}if(Ki(_)||Di(_))return p=p.get(m)||null,c(h,p,_,C,null);Na(h,_)}return null}function v(p,h,m,_){for(var C=null,k=null,P=h,b=h=0,F=null;P!==null&&b<m.length;b++){P.index>b?(F=P,P=null):F=P.sibling;var j=f(p,P,m[b],_);if(j===null){P===null&&(P=F);break}e&&P&&j.alternate===null&&t(p,P),h=i(j,h,b),k===null?C=j:k.sibling=j,k=j,P=F}if(b===m.length)return n(p,P),Te&&Ur(p,b),C;if(P===null){for(;b<m.length;b++)P=d(p,m[b],_),P!==null&&(h=i(P,h,b),k===null?C=P:k.sibling=P,k=P);return Te&&Ur(p,b),C}for(P=r(p,P);b<m.length;b++)F=g(P,p,b,m[b],_),F!==null&&(e&&F.alternate!==null&&P.delete(F.key===null?b:F.key),h=i(F,h,b),k===null?C=F:k.sibling=F,k=F);return e&&P.forEach(function(Y){return t(p,Y)}),Te&&Ur(p,b),C}function y(p,h,m,_){var C=Di(m);if(typeof C!="function")throw Error(D(150));if(m=C.call(m),m==null)throw Error(D(151));for(var k=C=null,P=h,b=h=0,F=null,j=m.next();P!==null&&!j.done;b++,j=m.next()){P.index>b?(F=P,P=null):F=P.sibling;var Y=f(p,P,j.value,_);if(Y===null){P===null&&(P=F);break}e&&P&&Y.alternate===null&&t(p,P),h=i(Y,h,b),k===null?C=Y:k.sibling=Y,k=Y,P=F}if(j.done)return n(p,P),Te&&Ur(p,b),C;if(P===null){for(;!j.done;b++,j=m.next())j=d(p,j.value,_),j!==null&&(h=i(j,h,b),k===null?C=j:k.sibling=j,k=j);return Te&&Ur(p,b),C}for(P=r(p,P);!j.done;b++,j=m.next())j=g(P,p,b,j.value,_),j!==null&&(e&&j.alternate!==null&&P.delete(j.key===null?b:j.key),h=i(j,h,b),k===null?C=j:k.sibling=j,k=j);return e&&P.forEach(function(L){return t(p,L)}),Te&&Ur(p,b),C}function w(p,h,m,_){if(typeof m=="object"&&m!==null&&m.type===bs&&m.key===null&&(m=m.props.children),typeof m=="object"&&m!==null){switch(m.$$typeof){case wa:e:{for(var C=m.key,k=h;k!==null;){if(k.key===C){if(C=m.type,C===bs){if(k.tag===7){n(p,k.sibling),h=s(k,m.props.children),h.return=p,p=h;break e}}else if(k.elementType===C||typeof C=="object"&&C!==null&&C.$$typeof===sr&&rm(C)===k.type){n(p,k.sibling),h=s(k,m.props),h.ref=Fi(p,k,m),h.return=p,p=h;break e}n(p,k);break}else t(p,k);k=k.sibling}m.type===bs?(h=rs(m.props.children,p.mode,_,m.key),h.return=p,p=h):(_=tl(m.type,m.key,m.props,null,p.mode,_),_.ref=Fi(p,h,m),_.return=p,p=_)}return o(p);case ks:e:{for(k=m.key;h!==null;){if(h.key===k)if(h.tag===4&&h.stateNode.containerInfo===m.containerInfo&&h.stateNode.implementation===m.implementation){n(p,h.sibling),h=s(h,m.children||[]),h.return=p,p=h;break e}else{n(p,h);break}else t(p,h);h=h.sibling}h=_c(m,p.mode,_),h.return=p,p=h}return o(p);case sr:return k=m._init,w(p,h,k(m._payload),_)}if(Ki(m))return v(p,h,m,_);if(Di(m))return y(p,h,m,_);Na(p,m)}return typeof m=="string"&&m!==""||typeof m=="number"?(m=""+m,h!==null&&h.tag===6?(n(p,h.sibling),h=s(h,m),h.return=p,p=h):(n(p,h),h=Sc(m,p.mode,_),h.return=p,p=h),o(p)):n(p,h)}return w}var ci=e0(!0),t0=e0(!1),bl=Fr(null),El=null,Os=null,$f=null;function Uf(){$f=Os=El=null}function Wf(e){var t=bl.current;_e(bl),e._currentValue=t}function _d(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Ks(e,t){El=e,$f=Os=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(wt=!0),e.firstContext=null)}function Ut(e){var t=e._currentValue;if($f!==e)if(e={context:e,memoizedValue:t,next:null},Os===null){if(El===null)throw Error(D(308));Os=e,El.dependencies={lanes:0,firstContext:e}}else Os=Os.next=e;return t}var qr=null;function Hf(e){qr===null?qr=[e]:qr.push(e)}function n0(e,t,n,r){var s=t.interleaved;return s===null?(n.next=n,Hf(t)):(n.next=s.next,s.next=n),t.interleaved=n,Hn(e,r)}function Hn(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var ir=!1;function Zf(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function r0(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function zn(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function wr(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,de&2){var s=r.pending;return s===null?t.next=t:(t.next=s.next,s.next=t),r.pending=t,Hn(e,n)}return s=r.interleaved,s===null?(t.next=t,Hf(r)):(t.next=s.next,s.next=t),r.interleaved=t,Hn(e,n)}function Ga(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Nf(e,n)}}function sm(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var s=null,i=null;if(n=n.firstBaseUpdate,n!==null){do{var o={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};i===null?s=i=o:i=i.next=o,n=n.next}while(n!==null);i===null?s=i=t:i=i.next=t}else s=i=t;n={baseState:r.baseState,firstBaseUpdate:s,lastBaseUpdate:i,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Pl(e,t,n,r){var s=e.updateQueue;ir=!1;var i=s.firstBaseUpdate,o=s.lastBaseUpdate,a=s.shared.pending;if(a!==null){s.shared.pending=null;var l=a,u=l.next;l.next=null,o===null?i=u:o.next=u,o=l;var c=e.alternate;c!==null&&(c=c.updateQueue,a=c.lastBaseUpdate,a!==o&&(a===null?c.firstBaseUpdate=u:a.next=u,c.lastBaseUpdate=l))}if(i!==null){var d=s.baseState;o=0,c=u=l=null,a=i;do{var f=a.lane,g=a.eventTime;if((r&f)===f){c!==null&&(c=c.next={eventTime:g,lane:0,tag:a.tag,payload:a.payload,callback:a.callback,next:null});e:{var v=e,y=a;switch(f=t,g=n,y.tag){case 1:if(v=y.payload,typeof v=="function"){d=v.call(g,d,f);break e}d=v;break e;case 3:v.flags=v.flags&-65537|128;case 0:if(v=y.payload,f=typeof v=="function"?v.call(g,d,f):v,f==null)break e;d=Pe({},d,f);break e;case 2:ir=!0}}a.callback!==null&&a.lane!==0&&(e.flags|=64,f=s.effects,f===null?s.effects=[a]:f.push(a))}else g={eventTime:g,lane:f,tag:a.tag,payload:a.payload,callback:a.callback,next:null},c===null?(u=c=g,l=d):c=c.next=g,o|=f;if(a=a.next,a===null){if(a=s.shared.pending,a===null)break;f=a,a=f.next,f.next=null,s.lastBaseUpdate=f,s.shared.pending=null}}while(!0);if(c===null&&(l=d),s.baseState=l,s.firstBaseUpdate=u,s.lastBaseUpdate=c,t=s.shared.interleaved,t!==null){s=t;do o|=s.lane,s=s.next;while(s!==t)}else i===null&&(s.shared.lanes=0);us|=o,e.lanes=o,e.memoizedState=d}}function im(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],s=r.callback;if(s!==null){if(r.callback=null,r=n,typeof s!="function")throw Error(D(191,s));s.call(r)}}}var oa={},wn=Fr(oa),Eo=Fr(oa),Po=Fr(oa);function Gr(e){if(e===oa)throw Error(D(174));return e}function Kf(e,t){switch(xe(Po,t),xe(Eo,e),xe(wn,oa),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:nd(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=nd(t,e)}_e(wn),xe(wn,t)}function di(){_e(wn),_e(Eo),_e(Po)}function s0(e){Gr(Po.current);var t=Gr(wn.current),n=nd(t,e.type);t!==n&&(xe(Eo,e),xe(wn,n))}function qf(e){Eo.current===e&&(_e(wn),_e(Eo))}var ke=Fr(0);function Al(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var mc=[];function Gf(){for(var e=0;e<mc.length;e++)mc[e]._workInProgressVersionPrimary=null;mc.length=0}var Qa=Yn.ReactCurrentDispatcher,gc=Yn.ReactCurrentBatchConfig,ls=0,Ee=null,ze=null,Ue=null,Rl=!1,io=!1,Ao=0,IT=0;function Xe(){throw Error(D(321))}function Qf(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!on(e[n],t[n]))return!1;return!0}function Yf(e,t,n,r,s,i){if(ls=i,Ee=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Qa.current=e===null||e.memoizedState===null?UT:WT,e=n(r,s),io){i=0;do{if(io=!1,Ao=0,25<=i)throw Error(D(301));i+=1,Ue=ze=null,t.updateQueue=null,Qa.current=HT,e=n(r,s)}while(io)}if(Qa.current=Nl,t=ze!==null&&ze.next!==null,ls=0,Ue=ze=Ee=null,Rl=!1,t)throw Error(D(300));return e}function Xf(){var e=Ao!==0;return Ao=0,e}function hn(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Ue===null?Ee.memoizedState=Ue=e:Ue=Ue.next=e,Ue}function Wt(){if(ze===null){var e=Ee.alternate;e=e!==null?e.memoizedState:null}else e=ze.next;var t=Ue===null?Ee.memoizedState:Ue.next;if(t!==null)Ue=t,ze=e;else{if(e===null)throw Error(D(310));ze=e,e={memoizedState:ze.memoizedState,baseState:ze.baseState,baseQueue:ze.baseQueue,queue:ze.queue,next:null},Ue===null?Ee.memoizedState=Ue=e:Ue=Ue.next=e}return Ue}function Ro(e,t){return typeof t=="function"?t(e):t}function yc(e){var t=Wt(),n=t.queue;if(n===null)throw Error(D(311));n.lastRenderedReducer=e;var r=ze,s=r.baseQueue,i=n.pending;if(i!==null){if(s!==null){var o=s.next;s.next=i.next,i.next=o}r.baseQueue=s=i,n.pending=null}if(s!==null){i=s.next,r=r.baseState;var a=o=null,l=null,u=i;do{var c=u.lane;if((ls&c)===c)l!==null&&(l=l.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var d={lane:c,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};l===null?(a=l=d,o=r):l=l.next=d,Ee.lanes|=c,us|=c}u=u.next}while(u!==null&&u!==i);l===null?o=r:l.next=a,on(r,t.memoizedState)||(wt=!0),t.memoizedState=r,t.baseState=o,t.baseQueue=l,n.lastRenderedState=r}if(e=n.interleaved,e!==null){s=e;do i=s.lane,Ee.lanes|=i,us|=i,s=s.next;while(s!==e)}else s===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function vc(e){var t=Wt(),n=t.queue;if(n===null)throw Error(D(311));n.lastRenderedReducer=e;var r=n.dispatch,s=n.pending,i=t.memoizedState;if(s!==null){n.pending=null;var o=s=s.next;do i=e(i,o.action),o=o.next;while(o!==s);on(i,t.memoizedState)||(wt=!0),t.memoizedState=i,t.baseQueue===null&&(t.baseState=i),n.lastRenderedState=i}return[i,r]}function i0(){}function o0(e,t){var n=Ee,r=Wt(),s=t(),i=!on(r.memoizedState,s);if(i&&(r.memoizedState=s,wt=!0),r=r.queue,Jf(u0.bind(null,n,r,e),[e]),r.getSnapshot!==t||i||Ue!==null&&Ue.memoizedState.tag&1){if(n.flags|=2048,No(9,l0.bind(null,n,r,s,t),void 0,null),We===null)throw Error(D(349));ls&30||a0(n,t,s)}return s}function a0(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=Ee.updateQueue,t===null?(t={lastEffect:null,stores:null},Ee.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function l0(e,t,n,r){t.value=n,t.getSnapshot=r,c0(t)&&d0(e)}function u0(e,t,n){return n(function(){c0(t)&&d0(e)})}function c0(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!on(e,n)}catch{return!0}}function d0(e){var t=Hn(e,1);t!==null&&rn(t,e,1,-1)}function om(e){var t=hn();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Ro,lastRenderedState:e},t.queue=e,e=e.dispatch=$T.bind(null,Ee,e),[t.memoizedState,e]}function No(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=Ee.updateQueue,t===null?(t={lastEffect:null,stores:null},Ee.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function f0(){return Wt().memoizedState}function Ya(e,t,n,r){var s=hn();Ee.flags|=e,s.memoizedState=No(1|t,n,void 0,r===void 0?null:r)}function gu(e,t,n,r){var s=Wt();r=r===void 0?null:r;var i=void 0;if(ze!==null){var o=ze.memoizedState;if(i=o.destroy,r!==null&&Qf(r,o.deps)){s.memoizedState=No(t,n,i,r);return}}Ee.flags|=e,s.memoizedState=No(1|t,n,i,r)}function am(e,t){return Ya(8390656,8,e,t)}function Jf(e,t){return gu(2048,8,e,t)}function h0(e,t){return gu(4,2,e,t)}function p0(e,t){return gu(4,4,e,t)}function m0(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function g0(e,t,n){return n=n!=null?n.concat([e]):null,gu(4,4,m0.bind(null,t,e),n)}function eh(){}function y0(e,t){var n=Wt();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Qf(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function v0(e,t){var n=Wt();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Qf(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function x0(e,t,n){return ls&21?(on(n,t)||(n=Cv(),Ee.lanes|=n,us|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,wt=!0),e.memoizedState=n)}function zT(e,t){var n=me;me=n!==0&&4>n?n:4,e(!0);var r=gc.transition;gc.transition={};try{e(!1),t()}finally{me=n,gc.transition=r}}function w0(){return Wt().memoizedState}function BT(e,t,n){var r=_r(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},S0(e))_0(t,n);else if(n=n0(e,t,n,r),n!==null){var s=ft();rn(n,e,r,s),T0(n,t,r)}}function $T(e,t,n){var r=_r(e),s={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(S0(e))_0(t,s);else{var i=e.alternate;if(e.lanes===0&&(i===null||i.lanes===0)&&(i=t.lastRenderedReducer,i!==null))try{var o=t.lastRenderedState,a=i(o,n);if(s.hasEagerState=!0,s.eagerState=a,on(a,o)){var l=t.interleaved;l===null?(s.next=s,Hf(t)):(s.next=l.next,l.next=s),t.interleaved=s;return}}catch{}finally{}n=n0(e,t,s,r),n!==null&&(s=ft(),rn(n,e,r,s),T0(n,t,r))}}function S0(e){var t=e.alternate;return e===Ee||t!==null&&t===Ee}function _0(e,t){io=Rl=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function T0(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Nf(e,n)}}var Nl={readContext:Ut,useCallback:Xe,useContext:Xe,useEffect:Xe,useImperativeHandle:Xe,useInsertionEffect:Xe,useLayoutEffect:Xe,useMemo:Xe,useReducer:Xe,useRef:Xe,useState:Xe,useDebugValue:Xe,useDeferredValue:Xe,useTransition:Xe,useMutableSource:Xe,useSyncExternalStore:Xe,useId:Xe,unstable_isNewReconciler:!1},UT={readContext:Ut,useCallback:function(e,t){return hn().memoizedState=[e,t===void 0?null:t],e},useContext:Ut,useEffect:am,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,Ya(4194308,4,m0.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Ya(4194308,4,e,t)},useInsertionEffect:function(e,t){return Ya(4,2,e,t)},useMemo:function(e,t){var n=hn();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=hn();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=BT.bind(null,Ee,e),[r.memoizedState,e]},useRef:function(e){var t=hn();return e={current:e},t.memoizedState=e},useState:om,useDebugValue:eh,useDeferredValue:function(e){return hn().memoizedState=e},useTransition:function(){var e=om(!1),t=e[0];return e=zT.bind(null,e[1]),hn().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=Ee,s=hn();if(Te){if(n===void 0)throw Error(D(407));n=n()}else{if(n=t(),We===null)throw Error(D(349));ls&30||a0(r,t,n)}s.memoizedState=n;var i={value:n,getSnapshot:t};return s.queue=i,am(u0.bind(null,r,i,e),[e]),r.flags|=2048,No(9,l0.bind(null,r,i,n,t),void 0,null),n},useId:function(){var e=hn(),t=We.identifierPrefix;if(Te){var n=Fn,r=Vn;n=(r&~(1<<32-nn(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=Ao++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=IT++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},WT={readContext:Ut,useCallback:y0,useContext:Ut,useEffect:Jf,useImperativeHandle:g0,useInsertionEffect:h0,useLayoutEffect:p0,useMemo:v0,useReducer:yc,useRef:f0,useState:function(){return yc(Ro)},useDebugValue:eh,useDeferredValue:function(e){var t=Wt();return x0(t,ze.memoizedState,e)},useTransition:function(){var e=yc(Ro)[0],t=Wt().memoizedState;return[e,t]},useMutableSource:i0,useSyncExternalStore:o0,useId:w0,unstable_isNewReconciler:!1},HT={readContext:Ut,useCallback:y0,useContext:Ut,useEffect:Jf,useImperativeHandle:g0,useInsertionEffect:h0,useLayoutEffect:p0,useMemo:v0,useReducer:vc,useRef:f0,useState:function(){return vc(Ro)},useDebugValue:eh,useDeferredValue:function(e){var t=Wt();return ze===null?t.memoizedState=e:x0(t,ze.memoizedState,e)},useTransition:function(){var e=vc(Ro)[0],t=Wt().memoizedState;return[e,t]},useMutableSource:i0,useSyncExternalStore:o0,useId:w0,unstable_isNewReconciler:!1};function qt(e,t){if(e&&e.defaultProps){t=Pe({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function Td(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:Pe({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var yu={isMounted:function(e){return(e=e._reactInternals)?ys(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=ft(),s=_r(e),i=zn(r,s);i.payload=t,n!=null&&(i.callback=n),t=wr(e,i,s),t!==null&&(rn(t,e,s,r),Ga(t,e,s))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=ft(),s=_r(e),i=zn(r,s);i.tag=1,i.payload=t,n!=null&&(i.callback=n),t=wr(e,i,s),t!==null&&(rn(t,e,s,r),Ga(t,e,s))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=ft(),r=_r(e),s=zn(n,r);s.tag=2,t!=null&&(s.callback=t),t=wr(e,s,r),t!==null&&(rn(t,e,r,n),Ga(t,e,r))}};function lm(e,t,n,r,s,i,o){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,i,o):t.prototype&&t.prototype.isPureReactComponent?!To(n,r)||!To(s,i):!0}function C0(e,t,n){var r=!1,s=Er,i=t.contextType;return typeof i=="object"&&i!==null?i=Ut(i):(s=_t(t)?os:it.current,r=t.contextTypes,i=(r=r!=null)?li(e,s):Er),t=new t(n,i),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=yu,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=s,e.__reactInternalMemoizedMaskedChildContext=i),t}function um(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&yu.enqueueReplaceState(t,t.state,null)}function Cd(e,t,n,r){var s=e.stateNode;s.props=n,s.state=e.memoizedState,s.refs={},Zf(e);var i=t.contextType;typeof i=="object"&&i!==null?s.context=Ut(i):(i=_t(t)?os:it.current,s.context=li(e,i)),s.state=e.memoizedState,i=t.getDerivedStateFromProps,typeof i=="function"&&(Td(e,t,i,n),s.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof s.getSnapshotBeforeUpdate=="function"||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(t=s.state,typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount(),t!==s.state&&yu.enqueueReplaceState(s,s.state,null),Pl(e,n,s,r),s.state=e.memoizedState),typeof s.componentDidMount=="function"&&(e.flags|=4194308)}function fi(e,t){try{var n="",r=t;do n+=x_(r),r=r.return;while(r);var s=n}catch(i){s=`
Error generating stack: `+i.message+`
`+i.stack}return{value:e,source:t,stack:s,digest:null}}function xc(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function kd(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var ZT=typeof WeakMap=="function"?WeakMap:Map;function k0(e,t,n){n=zn(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Dl||(Dl=!0,Ld=r),kd(e,t)},n}function b0(e,t,n){n=zn(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var s=t.value;n.payload=function(){return r(s)},n.callback=function(){kd(e,t)}}var i=e.stateNode;return i!==null&&typeof i.componentDidCatch=="function"&&(n.callback=function(){kd(e,t),typeof r!="function"&&(Sr===null?Sr=new Set([this]):Sr.add(this));var o=t.stack;this.componentDidCatch(t.value,{componentStack:o!==null?o:""})}),n}function cm(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new ZT;var s=new Set;r.set(t,s)}else s=r.get(t),s===void 0&&(s=new Set,r.set(t,s));s.has(n)||(s.add(n),e=oC.bind(null,e,t,n),t.then(e,e))}function dm(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function fm(e,t,n,r,s){return e.mode&1?(e.flags|=65536,e.lanes=s,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=zn(-1,1),t.tag=2,wr(n,t,1))),n.lanes|=1),e)}var KT=Yn.ReactCurrentOwner,wt=!1;function ut(e,t,n,r){t.child=e===null?t0(t,null,n,r):ci(t,e.child,n,r)}function hm(e,t,n,r,s){n=n.render;var i=t.ref;return Ks(t,s),r=Yf(e,t,n,r,i,s),n=Xf(),e!==null&&!wt?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~s,Zn(e,t,s)):(Te&&n&&If(t),t.flags|=1,ut(e,t,r,s),t.child)}function pm(e,t,n,r,s){if(e===null){var i=n.type;return typeof i=="function"&&!lh(i)&&i.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=i,E0(e,t,i,r,s)):(e=tl(n.type,null,r,t,t.mode,s),e.ref=t.ref,e.return=t,t.child=e)}if(i=e.child,!(e.lanes&s)){var o=i.memoizedProps;if(n=n.compare,n=n!==null?n:To,n(o,r)&&e.ref===t.ref)return Zn(e,t,s)}return t.flags|=1,e=Tr(i,r),e.ref=t.ref,e.return=t,t.child=e}function E0(e,t,n,r,s){if(e!==null){var i=e.memoizedProps;if(To(i,r)&&e.ref===t.ref)if(wt=!1,t.pendingProps=r=i,(e.lanes&s)!==0)e.flags&131072&&(wt=!0);else return t.lanes=e.lanes,Zn(e,t,s)}return bd(e,t,n,r,s)}function P0(e,t,n){var r=t.pendingProps,s=r.children,i=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},xe(js,kt),kt|=n;else{if(!(n&1073741824))return e=i!==null?i.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,xe(js,kt),kt|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=i!==null?i.baseLanes:n,xe(js,kt),kt|=r}else i!==null?(r=i.baseLanes|n,t.memoizedState=null):r=n,xe(js,kt),kt|=r;return ut(e,t,s,n),t.child}function A0(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function bd(e,t,n,r,s){var i=_t(n)?os:it.current;return i=li(t,i),Ks(t,s),n=Yf(e,t,n,r,i,s),r=Xf(),e!==null&&!wt?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~s,Zn(e,t,s)):(Te&&r&&If(t),t.flags|=1,ut(e,t,n,s),t.child)}function mm(e,t,n,r,s){if(_t(n)){var i=!0;Tl(t)}else i=!1;if(Ks(t,s),t.stateNode===null)Xa(e,t),C0(t,n,r),Cd(t,n,r,s),r=!0;else if(e===null){var o=t.stateNode,a=t.memoizedProps;o.props=a;var l=o.context,u=n.contextType;typeof u=="object"&&u!==null?u=Ut(u):(u=_t(n)?os:it.current,u=li(t,u));var c=n.getDerivedStateFromProps,d=typeof c=="function"||typeof o.getSnapshotBeforeUpdate=="function";d||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(a!==r||l!==u)&&um(t,o,r,u),ir=!1;var f=t.memoizedState;o.state=f,Pl(t,r,o,s),l=t.memoizedState,a!==r||f!==l||St.current||ir?(typeof c=="function"&&(Td(t,n,c,r),l=t.memoizedState),(a=ir||lm(t,n,a,r,f,l,u))?(d||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount()),typeof o.componentDidMount=="function"&&(t.flags|=4194308)):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=l),o.props=r,o.state=l,o.context=u,r=a):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{o=t.stateNode,r0(e,t),a=t.memoizedProps,u=t.type===t.elementType?a:qt(t.type,a),o.props=u,d=t.pendingProps,f=o.context,l=n.contextType,typeof l=="object"&&l!==null?l=Ut(l):(l=_t(n)?os:it.current,l=li(t,l));var g=n.getDerivedStateFromProps;(c=typeof g=="function"||typeof o.getSnapshotBeforeUpdate=="function")||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(a!==d||f!==l)&&um(t,o,r,l),ir=!1,f=t.memoizedState,o.state=f,Pl(t,r,o,s);var v=t.memoizedState;a!==d||f!==v||St.current||ir?(typeof g=="function"&&(Td(t,n,g,r),v=t.memoizedState),(u=ir||lm(t,n,u,r,f,v,l)||!1)?(c||typeof o.UNSAFE_componentWillUpdate!="function"&&typeof o.componentWillUpdate!="function"||(typeof o.componentWillUpdate=="function"&&o.componentWillUpdate(r,v,l),typeof o.UNSAFE_componentWillUpdate=="function"&&o.UNSAFE_componentWillUpdate(r,v,l)),typeof o.componentDidUpdate=="function"&&(t.flags|=4),typeof o.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof o.componentDidUpdate!="function"||a===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=v),o.props=r,o.state=v,o.context=l,r=u):(typeof o.componentDidUpdate!="function"||a===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return Ed(e,t,n,r,i,s)}function Ed(e,t,n,r,s,i){A0(e,t);var o=(t.flags&128)!==0;if(!r&&!o)return s&&em(t,n,!1),Zn(e,t,i);r=t.stateNode,KT.current=t;var a=o&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&o?(t.child=ci(t,e.child,null,i),t.child=ci(t,null,a,i)):ut(e,t,a,i),t.memoizedState=r.state,s&&em(t,n,!0),t.child}function R0(e){var t=e.stateNode;t.pendingContext?Jp(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Jp(e,t.context,!1),Kf(e,t.containerInfo)}function gm(e,t,n,r,s){return ui(),Bf(s),t.flags|=256,ut(e,t,n,r),t.child}var Pd={dehydrated:null,treeContext:null,retryLane:0};function Ad(e){return{baseLanes:e,cachePool:null,transitions:null}}function N0(e,t,n){var r=t.pendingProps,s=ke.current,i=!1,o=(t.flags&128)!==0,a;if((a=o)||(a=e!==null&&e.memoizedState===null?!1:(s&2)!==0),a?(i=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(s|=1),xe(ke,s&1),e===null)return Sd(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(o=r.children,e=r.fallback,i?(r=t.mode,i=t.child,o={mode:"hidden",children:o},!(r&1)&&i!==null?(i.childLanes=0,i.pendingProps=o):i=wu(o,r,0,null),e=rs(e,r,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=Ad(n),t.memoizedState=Pd,e):th(t,o));if(s=e.memoizedState,s!==null&&(a=s.dehydrated,a!==null))return qT(e,t,o,r,a,s,n);if(i){i=r.fallback,o=t.mode,s=e.child,a=s.sibling;var l={mode:"hidden",children:r.children};return!(o&1)&&t.child!==s?(r=t.child,r.childLanes=0,r.pendingProps=l,t.deletions=null):(r=Tr(s,l),r.subtreeFlags=s.subtreeFlags&14680064),a!==null?i=Tr(a,i):(i=rs(i,o,n,null),i.flags|=2),i.return=t,r.return=t,r.sibling=i,t.child=r,r=i,i=t.child,o=e.child.memoizedState,o=o===null?Ad(n):{baseLanes:o.baseLanes|n,cachePool:null,transitions:o.transitions},i.memoizedState=o,i.childLanes=e.childLanes&~n,t.memoizedState=Pd,r}return i=e.child,e=i.sibling,r=Tr(i,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function th(e,t){return t=wu({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Ma(e,t,n,r){return r!==null&&Bf(r),ci(t,e.child,null,n),e=th(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function qT(e,t,n,r,s,i,o){if(n)return t.flags&256?(t.flags&=-257,r=xc(Error(D(422))),Ma(e,t,o,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(i=r.fallback,s=t.mode,r=wu({mode:"visible",children:r.children},s,0,null),i=rs(i,s,o,null),i.flags|=2,r.return=t,i.return=t,r.sibling=i,t.child=r,t.mode&1&&ci(t,e.child,null,o),t.child.memoizedState=Ad(o),t.memoizedState=Pd,i);if(!(t.mode&1))return Ma(e,t,o,null);if(s.data==="$!"){if(r=s.nextSibling&&s.nextSibling.dataset,r)var a=r.dgst;return r=a,i=Error(D(419)),r=xc(i,r,void 0),Ma(e,t,o,r)}if(a=(o&e.childLanes)!==0,wt||a){if(r=We,r!==null){switch(o&-o){case 4:s=2;break;case 16:s=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:s=32;break;case 536870912:s=268435456;break;default:s=0}s=s&(r.suspendedLanes|o)?0:s,s!==0&&s!==i.retryLane&&(i.retryLane=s,Hn(e,s),rn(r,e,s,-1))}return ah(),r=xc(Error(D(421))),Ma(e,t,o,r)}return s.data==="$?"?(t.flags|=128,t.child=e.child,t=aC.bind(null,e),s._reactRetry=t,null):(e=i.treeContext,Pt=xr(s.nextSibling),At=t,Te=!0,Xt=null,e!==null&&(It[zt++]=Vn,It[zt++]=Fn,It[zt++]=as,Vn=e.id,Fn=e.overflow,as=t),t=th(t,r.children),t.flags|=4096,t)}function ym(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),_d(e.return,t,n)}function wc(e,t,n,r,s){var i=e.memoizedState;i===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:s}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=r,i.tail=n,i.tailMode=s)}function M0(e,t,n){var r=t.pendingProps,s=r.revealOrder,i=r.tail;if(ut(e,t,r.children,n),r=ke.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&ym(e,n,t);else if(e.tag===19)ym(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(xe(ke,r),!(t.mode&1))t.memoizedState=null;else switch(s){case"forwards":for(n=t.child,s=null;n!==null;)e=n.alternate,e!==null&&Al(e)===null&&(s=n),n=n.sibling;n=s,n===null?(s=t.child,t.child=null):(s=n.sibling,n.sibling=null),wc(t,!1,s,n,i);break;case"backwards":for(n=null,s=t.child,t.child=null;s!==null;){if(e=s.alternate,e!==null&&Al(e)===null){t.child=s;break}e=s.sibling,s.sibling=n,n=s,s=e}wc(t,!0,n,null,i);break;case"together":wc(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Xa(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Zn(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),us|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(D(153));if(t.child!==null){for(e=t.child,n=Tr(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Tr(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function GT(e,t,n){switch(t.tag){case 3:R0(t),ui();break;case 5:s0(t);break;case 1:_t(t.type)&&Tl(t);break;case 4:Kf(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,s=t.memoizedProps.value;xe(bl,r._currentValue),r._currentValue=s;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(xe(ke,ke.current&1),t.flags|=128,null):n&t.child.childLanes?N0(e,t,n):(xe(ke,ke.current&1),e=Zn(e,t,n),e!==null?e.sibling:null);xe(ke,ke.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return M0(e,t,n);t.flags|=128}if(s=t.memoizedState,s!==null&&(s.rendering=null,s.tail=null,s.lastEffect=null),xe(ke,ke.current),r)break;return null;case 22:case 23:return t.lanes=0,P0(e,t,n)}return Zn(e,t,n)}var D0,Rd,O0,L0;D0=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};Rd=function(){};O0=function(e,t,n,r){var s=e.memoizedProps;if(s!==r){e=t.stateNode,Gr(wn.current);var i=null;switch(n){case"input":s=Xc(e,s),r=Xc(e,r),i=[];break;case"select":s=Pe({},s,{value:void 0}),r=Pe({},r,{value:void 0}),i=[];break;case"textarea":s=td(e,s),r=td(e,r),i=[];break;default:typeof s.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=Sl)}rd(n,r);var o;n=null;for(u in s)if(!r.hasOwnProperty(u)&&s.hasOwnProperty(u)&&s[u]!=null)if(u==="style"){var a=s[u];for(o in a)a.hasOwnProperty(o)&&(n||(n={}),n[o]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(go.hasOwnProperty(u)?i||(i=[]):(i=i||[]).push(u,null));for(u in r){var l=r[u];if(a=s!=null?s[u]:void 0,r.hasOwnProperty(u)&&l!==a&&(l!=null||a!=null))if(u==="style")if(a){for(o in a)!a.hasOwnProperty(o)||l&&l.hasOwnProperty(o)||(n||(n={}),n[o]="");for(o in l)l.hasOwnProperty(o)&&a[o]!==l[o]&&(n||(n={}),n[o]=l[o])}else n||(i||(i=[]),i.push(u,n)),n=l;else u==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,a=a?a.__html:void 0,l!=null&&a!==l&&(i=i||[]).push(u,l)):u==="children"?typeof l!="string"&&typeof l!="number"||(i=i||[]).push(u,""+l):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(go.hasOwnProperty(u)?(l!=null&&u==="onScroll"&&Se("scroll",e),i||a===l||(i=[])):(i=i||[]).push(u,l))}n&&(i=i||[]).push("style",n);var u=i;(t.updateQueue=u)&&(t.flags|=4)}};L0=function(e,t,n,r){n!==r&&(t.flags|=4)};function Ii(e,t){if(!Te)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Je(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var s=e.child;s!==null;)n|=s.lanes|s.childLanes,r|=s.subtreeFlags&14680064,r|=s.flags&14680064,s.return=e,s=s.sibling;else for(s=e.child;s!==null;)n|=s.lanes|s.childLanes,r|=s.subtreeFlags,r|=s.flags,s.return=e,s=s.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function QT(e,t,n){var r=t.pendingProps;switch(zf(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Je(t),null;case 1:return _t(t.type)&&_l(),Je(t),null;case 3:return r=t.stateNode,di(),_e(St),_e(it),Gf(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(Ra(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,Xt!==null&&(Fd(Xt),Xt=null))),Rd(e,t),Je(t),null;case 5:qf(t);var s=Gr(Po.current);if(n=t.type,e!==null&&t.stateNode!=null)O0(e,t,n,r,s),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(D(166));return Je(t),null}if(e=Gr(wn.current),Ra(t)){r=t.stateNode,n=t.type;var i=t.memoizedProps;switch(r[yn]=t,r[bo]=i,e=(t.mode&1)!==0,n){case"dialog":Se("cancel",r),Se("close",r);break;case"iframe":case"object":case"embed":Se("load",r);break;case"video":case"audio":for(s=0;s<Gi.length;s++)Se(Gi[s],r);break;case"source":Se("error",r);break;case"img":case"image":case"link":Se("error",r),Se("load",r);break;case"details":Se("toggle",r);break;case"input":bp(r,i),Se("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!i.multiple},Se("invalid",r);break;case"textarea":Pp(r,i),Se("invalid",r)}rd(n,i),s=null;for(var o in i)if(i.hasOwnProperty(o)){var a=i[o];o==="children"?typeof a=="string"?r.textContent!==a&&(i.suppressHydrationWarning!==!0&&Aa(r.textContent,a,e),s=["children",a]):typeof a=="number"&&r.textContent!==""+a&&(i.suppressHydrationWarning!==!0&&Aa(r.textContent,a,e),s=["children",""+a]):go.hasOwnProperty(o)&&a!=null&&o==="onScroll"&&Se("scroll",r)}switch(n){case"input":Sa(r),Ep(r,i,!0);break;case"textarea":Sa(r),Ap(r);break;case"select":case"option":break;default:typeof i.onClick=="function"&&(r.onclick=Sl)}r=s,t.updateQueue=r,r!==null&&(t.flags|=4)}else{o=s.nodeType===9?s:s.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=uv(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=o.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=o.createElement(n,{is:r.is}):(e=o.createElement(n),n==="select"&&(o=e,r.multiple?o.multiple=!0:r.size&&(o.size=r.size))):e=o.createElementNS(e,n),e[yn]=t,e[bo]=r,D0(e,t,!1,!1),t.stateNode=e;e:{switch(o=sd(n,r),n){case"dialog":Se("cancel",e),Se("close",e),s=r;break;case"iframe":case"object":case"embed":Se("load",e),s=r;break;case"video":case"audio":for(s=0;s<Gi.length;s++)Se(Gi[s],e);s=r;break;case"source":Se("error",e),s=r;break;case"img":case"image":case"link":Se("error",e),Se("load",e),s=r;break;case"details":Se("toggle",e),s=r;break;case"input":bp(e,r),s=Xc(e,r),Se("invalid",e);break;case"option":s=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},s=Pe({},r,{value:void 0}),Se("invalid",e);break;case"textarea":Pp(e,r),s=td(e,r),Se("invalid",e);break;default:s=r}rd(n,s),a=s;for(i in a)if(a.hasOwnProperty(i)){var l=a[i];i==="style"?fv(e,l):i==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,l!=null&&cv(e,l)):i==="children"?typeof l=="string"?(n!=="textarea"||l!=="")&&yo(e,l):typeof l=="number"&&yo(e,""+l):i!=="suppressContentEditableWarning"&&i!=="suppressHydrationWarning"&&i!=="autoFocus"&&(go.hasOwnProperty(i)?l!=null&&i==="onScroll"&&Se("scroll",e):l!=null&&kf(e,i,l,o))}switch(n){case"input":Sa(e),Ep(e,r,!1);break;case"textarea":Sa(e),Ap(e);break;case"option":r.value!=null&&e.setAttribute("value",""+br(r.value));break;case"select":e.multiple=!!r.multiple,i=r.value,i!=null?Us(e,!!r.multiple,i,!1):r.defaultValue!=null&&Us(e,!!r.multiple,r.defaultValue,!0);break;default:typeof s.onClick=="function"&&(e.onclick=Sl)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return Je(t),null;case 6:if(e&&t.stateNode!=null)L0(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(D(166));if(n=Gr(Po.current),Gr(wn.current),Ra(t)){if(r=t.stateNode,n=t.memoizedProps,r[yn]=t,(i=r.nodeValue!==n)&&(e=At,e!==null))switch(e.tag){case 3:Aa(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Aa(r.nodeValue,n,(e.mode&1)!==0)}i&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[yn]=t,t.stateNode=r}return Je(t),null;case 13:if(_e(ke),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(Te&&Pt!==null&&t.mode&1&&!(t.flags&128))Jv(),ui(),t.flags|=98560,i=!1;else if(i=Ra(t),r!==null&&r.dehydrated!==null){if(e===null){if(!i)throw Error(D(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(D(317));i[yn]=t}else ui(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;Je(t),i=!1}else Xt!==null&&(Fd(Xt),Xt=null),i=!0;if(!i)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||ke.current&1?$e===0&&($e=3):ah())),t.updateQueue!==null&&(t.flags|=4),Je(t),null);case 4:return di(),Rd(e,t),e===null&&Co(t.stateNode.containerInfo),Je(t),null;case 10:return Wf(t.type._context),Je(t),null;case 17:return _t(t.type)&&_l(),Je(t),null;case 19:if(_e(ke),i=t.memoizedState,i===null)return Je(t),null;if(r=(t.flags&128)!==0,o=i.rendering,o===null)if(r)Ii(i,!1);else{if($e!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(o=Al(e),o!==null){for(t.flags|=128,Ii(i,!1),r=o.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)i=n,e=r,i.flags&=14680066,o=i.alternate,o===null?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=o.childLanes,i.lanes=o.lanes,i.child=o.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=o.memoizedProps,i.memoizedState=o.memoizedState,i.updateQueue=o.updateQueue,i.type=o.type,e=o.dependencies,i.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return xe(ke,ke.current&1|2),t.child}e=e.sibling}i.tail!==null&&Oe()>hi&&(t.flags|=128,r=!0,Ii(i,!1),t.lanes=4194304)}else{if(!r)if(e=Al(o),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),Ii(i,!0),i.tail===null&&i.tailMode==="hidden"&&!o.alternate&&!Te)return Je(t),null}else 2*Oe()-i.renderingStartTime>hi&&n!==1073741824&&(t.flags|=128,r=!0,Ii(i,!1),t.lanes=4194304);i.isBackwards?(o.sibling=t.child,t.child=o):(n=i.last,n!==null?n.sibling=o:t.child=o,i.last=o)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=Oe(),t.sibling=null,n=ke.current,xe(ke,r?n&1|2:n&1),t):(Je(t),null);case 22:case 23:return oh(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?kt&1073741824&&(Je(t),t.subtreeFlags&6&&(t.flags|=8192)):Je(t),null;case 24:return null;case 25:return null}throw Error(D(156,t.tag))}function YT(e,t){switch(zf(t),t.tag){case 1:return _t(t.type)&&_l(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return di(),_e(St),_e(it),Gf(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return qf(t),null;case 13:if(_e(ke),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(D(340));ui()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return _e(ke),null;case 4:return di(),null;case 10:return Wf(t.type._context),null;case 22:case 23:return oh(),null;case 24:return null;default:return null}}var Da=!1,nt=!1,XT=typeof WeakSet=="function"?WeakSet:Set,Z=null;function Ls(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){Ne(e,t,r)}else n.current=null}function Nd(e,t,n){try{n()}catch(r){Ne(e,t,r)}}var vm=!1;function JT(e,t){if(pd=vl,e=zv(),Ff(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var s=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch{n=null;break e}var o=0,a=-1,l=-1,u=0,c=0,d=e,f=null;t:for(;;){for(var g;d!==n||s!==0&&d.nodeType!==3||(a=o+s),d!==i||r!==0&&d.nodeType!==3||(l=o+r),d.nodeType===3&&(o+=d.nodeValue.length),(g=d.firstChild)!==null;)f=d,d=g;for(;;){if(d===e)break t;if(f===n&&++u===s&&(a=o),f===i&&++c===r&&(l=o),(g=d.nextSibling)!==null)break;d=f,f=d.parentNode}d=g}n=a===-1||l===-1?null:{start:a,end:l}}else n=null}n=n||{start:0,end:0}}else n=null;for(md={focusedElem:e,selectionRange:n},vl=!1,Z=t;Z!==null;)if(t=Z,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,Z=e;else for(;Z!==null;){t=Z;try{var v=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(v!==null){var y=v.memoizedProps,w=v.memoizedState,p=t.stateNode,h=p.getSnapshotBeforeUpdate(t.elementType===t.type?y:qt(t.type,y),w);p.__reactInternalSnapshotBeforeUpdate=h}break;case 3:var m=t.stateNode.containerInfo;m.nodeType===1?m.textContent="":m.nodeType===9&&m.documentElement&&m.removeChild(m.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(D(163))}}catch(_){Ne(t,t.return,_)}if(e=t.sibling,e!==null){e.return=t.return,Z=e;break}Z=t.return}return v=vm,vm=!1,v}function oo(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var s=r=r.next;do{if((s.tag&e)===e){var i=s.destroy;s.destroy=void 0,i!==void 0&&Nd(t,n,i)}s=s.next}while(s!==r)}}function vu(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function Md(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function j0(e){var t=e.alternate;t!==null&&(e.alternate=null,j0(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[yn],delete t[bo],delete t[vd],delete t[LT],delete t[jT])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function V0(e){return e.tag===5||e.tag===3||e.tag===4}function xm(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||V0(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Dd(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=Sl));else if(r!==4&&(e=e.child,e!==null))for(Dd(e,t,n),e=e.sibling;e!==null;)Dd(e,t,n),e=e.sibling}function Od(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(Od(e,t,n),e=e.sibling;e!==null;)Od(e,t,n),e=e.sibling}var Ze=null,Yt=!1;function Jn(e,t,n){for(n=n.child;n!==null;)F0(e,t,n),n=n.sibling}function F0(e,t,n){if(xn&&typeof xn.onCommitFiberUnmount=="function")try{xn.onCommitFiberUnmount(cu,n)}catch{}switch(n.tag){case 5:nt||Ls(n,t);case 6:var r=Ze,s=Yt;Ze=null,Jn(e,t,n),Ze=r,Yt=s,Ze!==null&&(Yt?(e=Ze,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):Ze.removeChild(n.stateNode));break;case 18:Ze!==null&&(Yt?(e=Ze,n=n.stateNode,e.nodeType===8?hc(e.parentNode,n):e.nodeType===1&&hc(e,n),So(e)):hc(Ze,n.stateNode));break;case 4:r=Ze,s=Yt,Ze=n.stateNode.containerInfo,Yt=!0,Jn(e,t,n),Ze=r,Yt=s;break;case 0:case 11:case 14:case 15:if(!nt&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){s=r=r.next;do{var i=s,o=i.destroy;i=i.tag,o!==void 0&&(i&2||i&4)&&Nd(n,t,o),s=s.next}while(s!==r)}Jn(e,t,n);break;case 1:if(!nt&&(Ls(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(a){Ne(n,t,a)}Jn(e,t,n);break;case 21:Jn(e,t,n);break;case 22:n.mode&1?(nt=(r=nt)||n.memoizedState!==null,Jn(e,t,n),nt=r):Jn(e,t,n);break;default:Jn(e,t,n)}}function wm(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new XT),t.forEach(function(r){var s=lC.bind(null,e,r);n.has(r)||(n.add(r),r.then(s,s))})}}function Zt(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var s=n[r];try{var i=e,o=t,a=o;e:for(;a!==null;){switch(a.tag){case 5:Ze=a.stateNode,Yt=!1;break e;case 3:Ze=a.stateNode.containerInfo,Yt=!0;break e;case 4:Ze=a.stateNode.containerInfo,Yt=!0;break e}a=a.return}if(Ze===null)throw Error(D(160));F0(i,o,s),Ze=null,Yt=!1;var l=s.alternate;l!==null&&(l.return=null),s.return=null}catch(u){Ne(s,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)I0(t,e),t=t.sibling}function I0(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Zt(t,e),fn(e),r&4){try{oo(3,e,e.return),vu(3,e)}catch(y){Ne(e,e.return,y)}try{oo(5,e,e.return)}catch(y){Ne(e,e.return,y)}}break;case 1:Zt(t,e),fn(e),r&512&&n!==null&&Ls(n,n.return);break;case 5:if(Zt(t,e),fn(e),r&512&&n!==null&&Ls(n,n.return),e.flags&32){var s=e.stateNode;try{yo(s,"")}catch(y){Ne(e,e.return,y)}}if(r&4&&(s=e.stateNode,s!=null)){var i=e.memoizedProps,o=n!==null?n.memoizedProps:i,a=e.type,l=e.updateQueue;if(e.updateQueue=null,l!==null)try{a==="input"&&i.type==="radio"&&i.name!=null&&av(s,i),sd(a,o);var u=sd(a,i);for(o=0;o<l.length;o+=2){var c=l[o],d=l[o+1];c==="style"?fv(s,d):c==="dangerouslySetInnerHTML"?cv(s,d):c==="children"?yo(s,d):kf(s,c,d,u)}switch(a){case"input":Jc(s,i);break;case"textarea":lv(s,i);break;case"select":var f=s._wrapperState.wasMultiple;s._wrapperState.wasMultiple=!!i.multiple;var g=i.value;g!=null?Us(s,!!i.multiple,g,!1):f!==!!i.multiple&&(i.defaultValue!=null?Us(s,!!i.multiple,i.defaultValue,!0):Us(s,!!i.multiple,i.multiple?[]:"",!1))}s[bo]=i}catch(y){Ne(e,e.return,y)}}break;case 6:if(Zt(t,e),fn(e),r&4){if(e.stateNode===null)throw Error(D(162));s=e.stateNode,i=e.memoizedProps;try{s.nodeValue=i}catch(y){Ne(e,e.return,y)}}break;case 3:if(Zt(t,e),fn(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{So(t.containerInfo)}catch(y){Ne(e,e.return,y)}break;case 4:Zt(t,e),fn(e);break;case 13:Zt(t,e),fn(e),s=e.child,s.flags&8192&&(i=s.memoizedState!==null,s.stateNode.isHidden=i,!i||s.alternate!==null&&s.alternate.memoizedState!==null||(sh=Oe())),r&4&&wm(e);break;case 22:if(c=n!==null&&n.memoizedState!==null,e.mode&1?(nt=(u=nt)||c,Zt(t,e),nt=u):Zt(t,e),fn(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!c&&e.mode&1)for(Z=e,c=e.child;c!==null;){for(d=Z=c;Z!==null;){switch(f=Z,g=f.child,f.tag){case 0:case 11:case 14:case 15:oo(4,f,f.return);break;case 1:Ls(f,f.return);var v=f.stateNode;if(typeof v.componentWillUnmount=="function"){r=f,n=f.return;try{t=r,v.props=t.memoizedProps,v.state=t.memoizedState,v.componentWillUnmount()}catch(y){Ne(r,n,y)}}break;case 5:Ls(f,f.return);break;case 22:if(f.memoizedState!==null){_m(d);continue}}g!==null?(g.return=f,Z=g):_m(d)}c=c.sibling}e:for(c=null,d=e;;){if(d.tag===5){if(c===null){c=d;try{s=d.stateNode,u?(i=s.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none"):(a=d.stateNode,l=d.memoizedProps.style,o=l!=null&&l.hasOwnProperty("display")?l.display:null,a.style.display=dv("display",o))}catch(y){Ne(e,e.return,y)}}}else if(d.tag===6){if(c===null)try{d.stateNode.nodeValue=u?"":d.memoizedProps}catch(y){Ne(e,e.return,y)}}else if((d.tag!==22&&d.tag!==23||d.memoizedState===null||d===e)&&d.child!==null){d.child.return=d,d=d.child;continue}if(d===e)break e;for(;d.sibling===null;){if(d.return===null||d.return===e)break e;c===d&&(c=null),d=d.return}c===d&&(c=null),d.sibling.return=d.return,d=d.sibling}}break;case 19:Zt(t,e),fn(e),r&4&&wm(e);break;case 21:break;default:Zt(t,e),fn(e)}}function fn(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(V0(n)){var r=n;break e}n=n.return}throw Error(D(160))}switch(r.tag){case 5:var s=r.stateNode;r.flags&32&&(yo(s,""),r.flags&=-33);var i=xm(e);Od(e,i,s);break;case 3:case 4:var o=r.stateNode.containerInfo,a=xm(e);Dd(e,a,o);break;default:throw Error(D(161))}}catch(l){Ne(e,e.return,l)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function eC(e,t,n){Z=e,z0(e)}function z0(e,t,n){for(var r=(e.mode&1)!==0;Z!==null;){var s=Z,i=s.child;if(s.tag===22&&r){var o=s.memoizedState!==null||Da;if(!o){var a=s.alternate,l=a!==null&&a.memoizedState!==null||nt;a=Da;var u=nt;if(Da=o,(nt=l)&&!u)for(Z=s;Z!==null;)o=Z,l=o.child,o.tag===22&&o.memoizedState!==null?Tm(s):l!==null?(l.return=o,Z=l):Tm(s);for(;i!==null;)Z=i,z0(i),i=i.sibling;Z=s,Da=a,nt=u}Sm(e)}else s.subtreeFlags&8772&&i!==null?(i.return=s,Z=i):Sm(e)}}function Sm(e){for(;Z!==null;){var t=Z;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:nt||vu(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!nt)if(n===null)r.componentDidMount();else{var s=t.elementType===t.type?n.memoizedProps:qt(t.type,n.memoizedProps);r.componentDidUpdate(s,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;i!==null&&im(t,i,r);break;case 3:var o=t.updateQueue;if(o!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}im(t,o,n)}break;case 5:var a=t.stateNode;if(n===null&&t.flags&4){n=a;var l=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":l.autoFocus&&n.focus();break;case"img":l.src&&(n.src=l.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var c=u.memoizedState;if(c!==null){var d=c.dehydrated;d!==null&&So(d)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(D(163))}nt||t.flags&512&&Md(t)}catch(f){Ne(t,t.return,f)}}if(t===e){Z=null;break}if(n=t.sibling,n!==null){n.return=t.return,Z=n;break}Z=t.return}}function _m(e){for(;Z!==null;){var t=Z;if(t===e){Z=null;break}var n=t.sibling;if(n!==null){n.return=t.return,Z=n;break}Z=t.return}}function Tm(e){for(;Z!==null;){var t=Z;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{vu(4,t)}catch(l){Ne(t,n,l)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var s=t.return;try{r.componentDidMount()}catch(l){Ne(t,s,l)}}var i=t.return;try{Md(t)}catch(l){Ne(t,i,l)}break;case 5:var o=t.return;try{Md(t)}catch(l){Ne(t,o,l)}}}catch(l){Ne(t,t.return,l)}if(t===e){Z=null;break}var a=t.sibling;if(a!==null){a.return=t.return,Z=a;break}Z=t.return}}var tC=Math.ceil,Ml=Yn.ReactCurrentDispatcher,nh=Yn.ReactCurrentOwner,$t=Yn.ReactCurrentBatchConfig,de=0,We=null,Fe=null,qe=0,kt=0,js=Fr(0),$e=0,Mo=null,us=0,xu=0,rh=0,ao=null,xt=null,sh=0,hi=1/0,On=null,Dl=!1,Ld=null,Sr=null,Oa=!1,mr=null,Ol=0,lo=0,jd=null,Ja=-1,el=0;function ft(){return de&6?Oe():Ja!==-1?Ja:Ja=Oe()}function _r(e){return e.mode&1?de&2&&qe!==0?qe&-qe:FT.transition!==null?(el===0&&(el=Cv()),el):(e=me,e!==0||(e=window.event,e=e===void 0?16:Nv(e.type)),e):1}function rn(e,t,n,r){if(50<lo)throw lo=0,jd=null,Error(D(185));ra(e,n,r),(!(de&2)||e!==We)&&(e===We&&(!(de&2)&&(xu|=n),$e===4&&ar(e,qe)),Tt(e,r),n===1&&de===0&&!(t.mode&1)&&(hi=Oe()+500,mu&&Ir()))}function Tt(e,t){var n=e.callbackNode;F_(e,t);var r=yl(e,e===We?qe:0);if(r===0)n!==null&&Mp(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&Mp(n),t===1)e.tag===0?VT(Cm.bind(null,e)):Qv(Cm.bind(null,e)),DT(function(){!(de&6)&&Ir()}),n=null;else{switch(kv(r)){case 1:n=Rf;break;case 4:n=_v;break;case 16:n=gl;break;case 536870912:n=Tv;break;default:n=gl}n=q0(n,B0.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function B0(e,t){if(Ja=-1,el=0,de&6)throw Error(D(327));var n=e.callbackNode;if(qs()&&e.callbackNode!==n)return null;var r=yl(e,e===We?qe:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=Ll(e,r);else{t=r;var s=de;de|=2;var i=U0();(We!==e||qe!==t)&&(On=null,hi=Oe()+500,ns(e,t));do try{sC();break}catch(a){$0(e,a)}while(!0);Uf(),Ml.current=i,de=s,Fe!==null?t=0:(We=null,qe=0,t=$e)}if(t!==0){if(t===2&&(s=ud(e),s!==0&&(r=s,t=Vd(e,s))),t===1)throw n=Mo,ns(e,0),ar(e,r),Tt(e,Oe()),n;if(t===6)ar(e,r);else{if(s=e.current.alternate,!(r&30)&&!nC(s)&&(t=Ll(e,r),t===2&&(i=ud(e),i!==0&&(r=i,t=Vd(e,i))),t===1))throw n=Mo,ns(e,0),ar(e,r),Tt(e,Oe()),n;switch(e.finishedWork=s,e.finishedLanes=r,t){case 0:case 1:throw Error(D(345));case 2:Wr(e,xt,On);break;case 3:if(ar(e,r),(r&130023424)===r&&(t=sh+500-Oe(),10<t)){if(yl(e,0)!==0)break;if(s=e.suspendedLanes,(s&r)!==r){ft(),e.pingedLanes|=e.suspendedLanes&s;break}e.timeoutHandle=yd(Wr.bind(null,e,xt,On),t);break}Wr(e,xt,On);break;case 4:if(ar(e,r),(r&4194240)===r)break;for(t=e.eventTimes,s=-1;0<r;){var o=31-nn(r);i=1<<o,o=t[o],o>s&&(s=o),r&=~i}if(r=s,r=Oe()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*tC(r/1960))-r,10<r){e.timeoutHandle=yd(Wr.bind(null,e,xt,On),r);break}Wr(e,xt,On);break;case 5:Wr(e,xt,On);break;default:throw Error(D(329))}}}return Tt(e,Oe()),e.callbackNode===n?B0.bind(null,e):null}function Vd(e,t){var n=ao;return e.current.memoizedState.isDehydrated&&(ns(e,t).flags|=256),e=Ll(e,t),e!==2&&(t=xt,xt=n,t!==null&&Fd(t)),e}function Fd(e){xt===null?xt=e:xt.push.apply(xt,e)}function nC(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var s=n[r],i=s.getSnapshot;s=s.value;try{if(!on(i(),s))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function ar(e,t){for(t&=~rh,t&=~xu,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-nn(t),r=1<<n;e[n]=-1,t&=~r}}function Cm(e){if(de&6)throw Error(D(327));qs();var t=yl(e,0);if(!(t&1))return Tt(e,Oe()),null;var n=Ll(e,t);if(e.tag!==0&&n===2){var r=ud(e);r!==0&&(t=r,n=Vd(e,r))}if(n===1)throw n=Mo,ns(e,0),ar(e,t),Tt(e,Oe()),n;if(n===6)throw Error(D(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Wr(e,xt,On),Tt(e,Oe()),null}function ih(e,t){var n=de;de|=1;try{return e(t)}finally{de=n,de===0&&(hi=Oe()+500,mu&&Ir())}}function cs(e){mr!==null&&mr.tag===0&&!(de&6)&&qs();var t=de;de|=1;var n=$t.transition,r=me;try{if($t.transition=null,me=1,e)return e()}finally{me=r,$t.transition=n,de=t,!(de&6)&&Ir()}}function oh(){kt=js.current,_e(js)}function ns(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,MT(n)),Fe!==null)for(n=Fe.return;n!==null;){var r=n;switch(zf(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&_l();break;case 3:di(),_e(St),_e(it),Gf();break;case 5:qf(r);break;case 4:di();break;case 13:_e(ke);break;case 19:_e(ke);break;case 10:Wf(r.type._context);break;case 22:case 23:oh()}n=n.return}if(We=e,Fe=e=Tr(e.current,null),qe=kt=t,$e=0,Mo=null,rh=xu=us=0,xt=ao=null,qr!==null){for(t=0;t<qr.length;t++)if(n=qr[t],r=n.interleaved,r!==null){n.interleaved=null;var s=r.next,i=n.pending;if(i!==null){var o=i.next;i.next=s,r.next=o}n.pending=r}qr=null}return e}function $0(e,t){do{var n=Fe;try{if(Uf(),Qa.current=Nl,Rl){for(var r=Ee.memoizedState;r!==null;){var s=r.queue;s!==null&&(s.pending=null),r=r.next}Rl=!1}if(ls=0,Ue=ze=Ee=null,io=!1,Ao=0,nh.current=null,n===null||n.return===null){$e=1,Mo=t,Fe=null;break}e:{var i=e,o=n.return,a=n,l=t;if(t=qe,a.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){var u=l,c=a,d=c.tag;if(!(c.mode&1)&&(d===0||d===11||d===15)){var f=c.alternate;f?(c.updateQueue=f.updateQueue,c.memoizedState=f.memoizedState,c.lanes=f.lanes):(c.updateQueue=null,c.memoizedState=null)}var g=dm(o);if(g!==null){g.flags&=-257,fm(g,o,a,i,t),g.mode&1&&cm(i,u,t),t=g,l=u;var v=t.updateQueue;if(v===null){var y=new Set;y.add(l),t.updateQueue=y}else v.add(l);break e}else{if(!(t&1)){cm(i,u,t),ah();break e}l=Error(D(426))}}else if(Te&&a.mode&1){var w=dm(o);if(w!==null){!(w.flags&65536)&&(w.flags|=256),fm(w,o,a,i,t),Bf(fi(l,a));break e}}i=l=fi(l,a),$e!==4&&($e=2),ao===null?ao=[i]:ao.push(i),i=o;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t;var p=k0(i,l,t);sm(i,p);break e;case 1:a=l;var h=i.type,m=i.stateNode;if(!(i.flags&128)&&(typeof h.getDerivedStateFromError=="function"||m!==null&&typeof m.componentDidCatch=="function"&&(Sr===null||!Sr.has(m)))){i.flags|=65536,t&=-t,i.lanes|=t;var _=b0(i,a,t);sm(i,_);break e}}i=i.return}while(i!==null)}H0(n)}catch(C){t=C,Fe===n&&n!==null&&(Fe=n=n.return);continue}break}while(!0)}function U0(){var e=Ml.current;return Ml.current=Nl,e===null?Nl:e}function ah(){($e===0||$e===3||$e===2)&&($e=4),We===null||!(us&268435455)&&!(xu&268435455)||ar(We,qe)}function Ll(e,t){var n=de;de|=2;var r=U0();(We!==e||qe!==t)&&(On=null,ns(e,t));do try{rC();break}catch(s){$0(e,s)}while(!0);if(Uf(),de=n,Ml.current=r,Fe!==null)throw Error(D(261));return We=null,qe=0,$e}function rC(){for(;Fe!==null;)W0(Fe)}function sC(){for(;Fe!==null&&!A_();)W0(Fe)}function W0(e){var t=K0(e.alternate,e,kt);e.memoizedProps=e.pendingProps,t===null?H0(e):Fe=t,nh.current=null}function H0(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=YT(n,t),n!==null){n.flags&=32767,Fe=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{$e=6,Fe=null;return}}else if(n=QT(n,t,kt),n!==null){Fe=n;return}if(t=t.sibling,t!==null){Fe=t;return}Fe=t=e}while(t!==null);$e===0&&($e=5)}function Wr(e,t,n){var r=me,s=$t.transition;try{$t.transition=null,me=1,iC(e,t,n,r)}finally{$t.transition=s,me=r}return null}function iC(e,t,n,r){do qs();while(mr!==null);if(de&6)throw Error(D(327));n=e.finishedWork;var s=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(D(177));e.callbackNode=null,e.callbackPriority=0;var i=n.lanes|n.childLanes;if(I_(e,i),e===We&&(Fe=We=null,qe=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Oa||(Oa=!0,q0(gl,function(){return qs(),null})),i=(n.flags&15990)!==0,n.subtreeFlags&15990||i){i=$t.transition,$t.transition=null;var o=me;me=1;var a=de;de|=4,nh.current=null,JT(e,n),I0(n,e),kT(md),vl=!!pd,md=pd=null,e.current=n,eC(n),R_(),de=a,me=o,$t.transition=i}else e.current=n;if(Oa&&(Oa=!1,mr=e,Ol=s),i=e.pendingLanes,i===0&&(Sr=null),D_(n.stateNode),Tt(e,Oe()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)s=t[n],r(s.value,{componentStack:s.stack,digest:s.digest});if(Dl)throw Dl=!1,e=Ld,Ld=null,e;return Ol&1&&e.tag!==0&&qs(),i=e.pendingLanes,i&1?e===jd?lo++:(lo=0,jd=e):lo=0,Ir(),null}function qs(){if(mr!==null){var e=kv(Ol),t=$t.transition,n=me;try{if($t.transition=null,me=16>e?16:e,mr===null)var r=!1;else{if(e=mr,mr=null,Ol=0,de&6)throw Error(D(331));var s=de;for(de|=4,Z=e.current;Z!==null;){var i=Z,o=i.child;if(Z.flags&16){var a=i.deletions;if(a!==null){for(var l=0;l<a.length;l++){var u=a[l];for(Z=u;Z!==null;){var c=Z;switch(c.tag){case 0:case 11:case 15:oo(8,c,i)}var d=c.child;if(d!==null)d.return=c,Z=d;else for(;Z!==null;){c=Z;var f=c.sibling,g=c.return;if(j0(c),c===u){Z=null;break}if(f!==null){f.return=g,Z=f;break}Z=g}}}var v=i.alternate;if(v!==null){var y=v.child;if(y!==null){v.child=null;do{var w=y.sibling;y.sibling=null,y=w}while(y!==null)}}Z=i}}if(i.subtreeFlags&2064&&o!==null)o.return=i,Z=o;else e:for(;Z!==null;){if(i=Z,i.flags&2048)switch(i.tag){case 0:case 11:case 15:oo(9,i,i.return)}var p=i.sibling;if(p!==null){p.return=i.return,Z=p;break e}Z=i.return}}var h=e.current;for(Z=h;Z!==null;){o=Z;var m=o.child;if(o.subtreeFlags&2064&&m!==null)m.return=o,Z=m;else e:for(o=h;Z!==null;){if(a=Z,a.flags&2048)try{switch(a.tag){case 0:case 11:case 15:vu(9,a)}}catch(C){Ne(a,a.return,C)}if(a===o){Z=null;break e}var _=a.sibling;if(_!==null){_.return=a.return,Z=_;break e}Z=a.return}}if(de=s,Ir(),xn&&typeof xn.onPostCommitFiberRoot=="function")try{xn.onPostCommitFiberRoot(cu,e)}catch{}r=!0}return r}finally{me=n,$t.transition=t}}return!1}function km(e,t,n){t=fi(n,t),t=k0(e,t,1),e=wr(e,t,1),t=ft(),e!==null&&(ra(e,1,t),Tt(e,t))}function Ne(e,t,n){if(e.tag===3)km(e,e,n);else for(;t!==null;){if(t.tag===3){km(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(Sr===null||!Sr.has(r))){e=fi(n,e),e=b0(t,e,1),t=wr(t,e,1),e=ft(),t!==null&&(ra(t,1,e),Tt(t,e));break}}t=t.return}}function oC(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=ft(),e.pingedLanes|=e.suspendedLanes&n,We===e&&(qe&n)===n&&($e===4||$e===3&&(qe&130023424)===qe&&500>Oe()-sh?ns(e,0):rh|=n),Tt(e,t)}function Z0(e,t){t===0&&(e.mode&1?(t=Ca,Ca<<=1,!(Ca&130023424)&&(Ca=4194304)):t=1);var n=ft();e=Hn(e,t),e!==null&&(ra(e,t,n),Tt(e,n))}function aC(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Z0(e,n)}function lC(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,s=e.memoizedState;s!==null&&(n=s.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(D(314))}r!==null&&r.delete(t),Z0(e,n)}var K0;K0=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||St.current)wt=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return wt=!1,GT(e,t,n);wt=!!(e.flags&131072)}else wt=!1,Te&&t.flags&1048576&&Yv(t,kl,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Xa(e,t),e=t.pendingProps;var s=li(t,it.current);Ks(t,n),s=Yf(null,t,r,e,s,n);var i=Xf();return t.flags|=1,typeof s=="object"&&s!==null&&typeof s.render=="function"&&s.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,_t(r)?(i=!0,Tl(t)):i=!1,t.memoizedState=s.state!==null&&s.state!==void 0?s.state:null,Zf(t),s.updater=yu,t.stateNode=s,s._reactInternals=t,Cd(t,r,e,n),t=Ed(null,t,r,!0,i,n)):(t.tag=0,Te&&i&&If(t),ut(null,t,s,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Xa(e,t),e=t.pendingProps,s=r._init,r=s(r._payload),t.type=r,s=t.tag=cC(r),e=qt(r,e),s){case 0:t=bd(null,t,r,e,n);break e;case 1:t=mm(null,t,r,e,n);break e;case 11:t=hm(null,t,r,e,n);break e;case 14:t=pm(null,t,r,qt(r.type,e),n);break e}throw Error(D(306,r,""))}return t;case 0:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:qt(r,s),bd(e,t,r,s,n);case 1:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:qt(r,s),mm(e,t,r,s,n);case 3:e:{if(R0(t),e===null)throw Error(D(387));r=t.pendingProps,i=t.memoizedState,s=i.element,r0(e,t),Pl(t,r,null,n);var o=t.memoizedState;if(r=o.element,i.isDehydrated)if(i={element:r,isDehydrated:!1,cache:o.cache,pendingSuspenseBoundaries:o.pendingSuspenseBoundaries,transitions:o.transitions},t.updateQueue.baseState=i,t.memoizedState=i,t.flags&256){s=fi(Error(D(423)),t),t=gm(e,t,r,n,s);break e}else if(r!==s){s=fi(Error(D(424)),t),t=gm(e,t,r,n,s);break e}else for(Pt=xr(t.stateNode.containerInfo.firstChild),At=t,Te=!0,Xt=null,n=t0(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(ui(),r===s){t=Zn(e,t,n);break e}ut(e,t,r,n)}t=t.child}return t;case 5:return s0(t),e===null&&Sd(t),r=t.type,s=t.pendingProps,i=e!==null?e.memoizedProps:null,o=s.children,gd(r,s)?o=null:i!==null&&gd(r,i)&&(t.flags|=32),A0(e,t),ut(e,t,o,n),t.child;case 6:return e===null&&Sd(t),null;case 13:return N0(e,t,n);case 4:return Kf(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=ci(t,null,r,n):ut(e,t,r,n),t.child;case 11:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:qt(r,s),hm(e,t,r,s,n);case 7:return ut(e,t,t.pendingProps,n),t.child;case 8:return ut(e,t,t.pendingProps.children,n),t.child;case 12:return ut(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,s=t.pendingProps,i=t.memoizedProps,o=s.value,xe(bl,r._currentValue),r._currentValue=o,i!==null)if(on(i.value,o)){if(i.children===s.children&&!St.current){t=Zn(e,t,n);break e}}else for(i=t.child,i!==null&&(i.return=t);i!==null;){var a=i.dependencies;if(a!==null){o=i.child;for(var l=a.firstContext;l!==null;){if(l.context===r){if(i.tag===1){l=zn(-1,n&-n),l.tag=2;var u=i.updateQueue;if(u!==null){u=u.shared;var c=u.pending;c===null?l.next=l:(l.next=c.next,c.next=l),u.pending=l}}i.lanes|=n,l=i.alternate,l!==null&&(l.lanes|=n),_d(i.return,n,t),a.lanes|=n;break}l=l.next}}else if(i.tag===10)o=i.type===t.type?null:i.child;else if(i.tag===18){if(o=i.return,o===null)throw Error(D(341));o.lanes|=n,a=o.alternate,a!==null&&(a.lanes|=n),_d(o,n,t),o=i.sibling}else o=i.child;if(o!==null)o.return=i;else for(o=i;o!==null;){if(o===t){o=null;break}if(i=o.sibling,i!==null){i.return=o.return,o=i;break}o=o.return}i=o}ut(e,t,s.children,n),t=t.child}return t;case 9:return s=t.type,r=t.pendingProps.children,Ks(t,n),s=Ut(s),r=r(s),t.flags|=1,ut(e,t,r,n),t.child;case 14:return r=t.type,s=qt(r,t.pendingProps),s=qt(r.type,s),pm(e,t,r,s,n);case 15:return E0(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:qt(r,s),Xa(e,t),t.tag=1,_t(r)?(e=!0,Tl(t)):e=!1,Ks(t,n),C0(t,r,s),Cd(t,r,s,n),Ed(null,t,r,!0,e,n);case 19:return M0(e,t,n);case 22:return P0(e,t,n)}throw Error(D(156,t.tag))};function q0(e,t){return Sv(e,t)}function uC(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Bt(e,t,n,r){return new uC(e,t,n,r)}function lh(e){return e=e.prototype,!(!e||!e.isReactComponent)}function cC(e){if(typeof e=="function")return lh(e)?1:0;if(e!=null){if(e=e.$$typeof,e===Ef)return 11;if(e===Pf)return 14}return 2}function Tr(e,t){var n=e.alternate;return n===null?(n=Bt(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function tl(e,t,n,r,s,i){var o=2;if(r=e,typeof e=="function")lh(e)&&(o=1);else if(typeof e=="string")o=5;else e:switch(e){case bs:return rs(n.children,s,i,t);case bf:o=8,s|=8;break;case qc:return e=Bt(12,n,t,s|2),e.elementType=qc,e.lanes=i,e;case Gc:return e=Bt(13,n,t,s),e.elementType=Gc,e.lanes=i,e;case Qc:return e=Bt(19,n,t,s),e.elementType=Qc,e.lanes=i,e;case sv:return wu(n,s,i,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case nv:o=10;break e;case rv:o=9;break e;case Ef:o=11;break e;case Pf:o=14;break e;case sr:o=16,r=null;break e}throw Error(D(130,e==null?e:typeof e,""))}return t=Bt(o,n,t,s),t.elementType=e,t.type=r,t.lanes=i,t}function rs(e,t,n,r){return e=Bt(7,e,r,t),e.lanes=n,e}function wu(e,t,n,r){return e=Bt(22,e,r,t),e.elementType=sv,e.lanes=n,e.stateNode={isHidden:!1},e}function Sc(e,t,n){return e=Bt(6,e,null,t),e.lanes=n,e}function _c(e,t,n){return t=Bt(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function dC(e,t,n,r,s){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=nc(0),this.expirationTimes=nc(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=nc(0),this.identifierPrefix=r,this.onRecoverableError=s,this.mutableSourceEagerHydrationData=null}function uh(e,t,n,r,s,i,o,a,l){return e=new dC(e,t,n,a,l),t===1?(t=1,i===!0&&(t|=8)):t=0,i=Bt(3,null,null,t),e.current=i,i.stateNode=e,i.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Zf(i),e}function fC(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:ks,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function G0(e){if(!e)return Er;e=e._reactInternals;e:{if(ys(e)!==e||e.tag!==1)throw Error(D(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(_t(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(D(171))}if(e.tag===1){var n=e.type;if(_t(n))return Gv(e,n,t)}return t}function Q0(e,t,n,r,s,i,o,a,l){return e=uh(n,r,!0,e,s,i,o,a,l),e.context=G0(null),n=e.current,r=ft(),s=_r(n),i=zn(r,s),i.callback=t??null,wr(n,i,s),e.current.lanes=s,ra(e,s,r),Tt(e,r),e}function Su(e,t,n,r){var s=t.current,i=ft(),o=_r(s);return n=G0(n),t.context===null?t.context=n:t.pendingContext=n,t=zn(i,o),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=wr(s,t,o),e!==null&&(rn(e,s,o,i),Ga(e,s,o)),o}function jl(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function bm(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function ch(e,t){bm(e,t),(e=e.alternate)&&bm(e,t)}function hC(){return null}var Y0=typeof reportError=="function"?reportError:function(e){console.error(e)};function dh(e){this._internalRoot=e}_u.prototype.render=dh.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(D(409));Su(e,t,null,null)};_u.prototype.unmount=dh.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;cs(function(){Su(null,e,null,null)}),t[Wn]=null}};function _u(e){this._internalRoot=e}_u.prototype.unstable_scheduleHydration=function(e){if(e){var t=Pv();e={blockedOn:null,target:e,priority:t};for(var n=0;n<or.length&&t!==0&&t<or[n].priority;n++);or.splice(n,0,e),n===0&&Rv(e)}};function fh(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Tu(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Em(){}function pC(e,t,n,r,s){if(s){if(typeof r=="function"){var i=r;r=function(){var u=jl(o);i.call(u)}}var o=Q0(t,r,e,0,null,!1,!1,"",Em);return e._reactRootContainer=o,e[Wn]=o.current,Co(e.nodeType===8?e.parentNode:e),cs(),o}for(;s=e.lastChild;)e.removeChild(s);if(typeof r=="function"){var a=r;r=function(){var u=jl(l);a.call(u)}}var l=uh(e,0,!1,null,null,!1,!1,"",Em);return e._reactRootContainer=l,e[Wn]=l.current,Co(e.nodeType===8?e.parentNode:e),cs(function(){Su(t,l,n,r)}),l}function Cu(e,t,n,r,s){var i=n._reactRootContainer;if(i){var o=i;if(typeof s=="function"){var a=s;s=function(){var l=jl(o);a.call(l)}}Su(t,o,e,s)}else o=pC(n,t,e,s,r);return jl(o)}bv=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=qi(t.pendingLanes);n!==0&&(Nf(t,n|1),Tt(t,Oe()),!(de&6)&&(hi=Oe()+500,Ir()))}break;case 13:cs(function(){var r=Hn(e,1);if(r!==null){var s=ft();rn(r,e,1,s)}}),ch(e,1)}};Mf=function(e){if(e.tag===13){var t=Hn(e,134217728);if(t!==null){var n=ft();rn(t,e,134217728,n)}ch(e,134217728)}};Ev=function(e){if(e.tag===13){var t=_r(e),n=Hn(e,t);if(n!==null){var r=ft();rn(n,e,t,r)}ch(e,t)}};Pv=function(){return me};Av=function(e,t){var n=me;try{return me=e,t()}finally{me=n}};od=function(e,t,n){switch(t){case"input":if(Jc(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var s=pu(r);if(!s)throw Error(D(90));ov(r),Jc(r,s)}}}break;case"textarea":lv(e,n);break;case"select":t=n.value,t!=null&&Us(e,!!n.multiple,t,!1)}};mv=ih;gv=cs;var mC={usingClientEntryPoint:!1,Events:[ia,Rs,pu,hv,pv,ih]},zi={findFiberByHostInstance:Kr,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},gC={bundleType:zi.bundleType,version:zi.version,rendererPackageName:zi.rendererPackageName,rendererConfig:zi.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:Yn.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=xv(e),e===null?null:e.stateNode},findFiberByHostInstance:zi.findFiberByHostInstance||hC,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var La=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!La.isDisabled&&La.supportsFiber)try{cu=La.inject(gC),xn=La}catch{}}Ot.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=mC;Ot.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!fh(t))throw Error(D(200));return fC(e,t,null,n)};Ot.createRoot=function(e,t){if(!fh(e))throw Error(D(299));var n=!1,r="",s=Y0;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(s=t.onRecoverableError)),t=uh(e,1,!1,null,null,n,!1,r,s),e[Wn]=t.current,Co(e.nodeType===8?e.parentNode:e),new dh(t)};Ot.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(D(188)):(e=Object.keys(e).join(","),Error(D(268,e)));return e=xv(t),e=e===null?null:e.stateNode,e};Ot.flushSync=function(e){return cs(e)};Ot.hydrate=function(e,t,n){if(!Tu(t))throw Error(D(200));return Cu(null,e,t,!0,n)};Ot.hydrateRoot=function(e,t,n){if(!fh(e))throw Error(D(405));var r=n!=null&&n.hydratedSources||null,s=!1,i="",o=Y0;if(n!=null&&(n.unstable_strictMode===!0&&(s=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onRecoverableError!==void 0&&(o=n.onRecoverableError)),t=Q0(t,null,e,1,n??null,s,!1,i,o),e[Wn]=t.current,Co(e),r)for(e=0;e<r.length;e++)n=r[e],s=n._getVersion,s=s(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,s]:t.mutableSourceEagerHydrationData.push(n,s);return new _u(t)};Ot.render=function(e,t,n){if(!Tu(t))throw Error(D(200));return Cu(null,e,t,!1,n)};Ot.unmountComponentAtNode=function(e){if(!Tu(e))throw Error(D(40));return e._reactRootContainer?(cs(function(){Cu(null,null,e,!1,function(){e._reactRootContainer=null,e[Wn]=null})}),!0):!1};Ot.unstable_batchedUpdates=ih;Ot.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Tu(n))throw Error(D(200));if(e==null||e._reactInternals===void 0)throw Error(D(38));return Cu(e,t,n,!1,r)};Ot.version="18.3.1-next-f1338f8080-20240426";function X0(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(X0)}catch(e){console.error(e)}}X0(),Xy.exports=Ot;var aa=Xy.exports;const yC=zy(aa);var J0,Pm=aa;J0=Pm.createRoot,Pm.hydrateRoot;function vC(e,t){if(e instanceof RegExp)return{keys:!1,pattern:e};var n,r,s,i,o=[],a="",l=e.split("/");for(l[0]||l.shift();s=l.shift();)n=s[0],n==="*"?(o.push(n),a+=s[1]==="?"?"(?:/(.*))?":"/(.*)"):n===":"?(r=s.indexOf("?",1),i=s.indexOf(".",1),o.push(s.substring(1,~r?r:~i?i:s.length)),a+=~r&&!~i?"(?:/([^/]+?))?":"/([^/]+?)",~i&&(a+=(~r?"?":"")+"\\"+s.substring(i))):a+="/"+s;return{keys:o,pattern:new RegExp("^"+a+(t?"(?=$|/)":"/?$"),"i")}}var ex={exports:{}},tx={};/**
 * @license React
 * use-sync-external-store-shim.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var pi=x;function xC(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var wC=typeof Object.is=="function"?Object.is:xC,SC=pi.useState,_C=pi.useEffect,TC=pi.useLayoutEffect,CC=pi.useDebugValue;function kC(e,t){var n=t(),r=SC({inst:{value:n,getSnapshot:t}}),s=r[0].inst,i=r[1];return TC(function(){s.value=n,s.getSnapshot=t,Tc(s)&&i({inst:s})},[e,n,t]),_C(function(){return Tc(s)&&i({inst:s}),e(function(){Tc(s)&&i({inst:s})})},[e]),CC(n),n}function Tc(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!wC(e,n)}catch{return!0}}function bC(e,t){return t()}var EC=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?bC:kC;tx.useSyncExternalStore=pi.useSyncExternalStore!==void 0?pi.useSyncExternalStore:EC;ex.exports=tx;var PC=ex.exports;const AC=o_.useInsertionEffect,RC=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",NC=RC?x.useLayoutEffect:x.useEffect,MC=AC||NC,nx=e=>{const t=x.useRef([e,(...n)=>t[0](...n)]).current;return MC(()=>{t[0]=e}),t[1]},DC="popstate",hh="pushState",ph="replaceState",OC="hashchange",Am=[DC,hh,ph,OC],LC=e=>{for(const t of Am)addEventListener(t,e);return()=>{for(const t of Am)removeEventListener(t,e)}},rx=(e,t)=>PC.useSyncExternalStore(LC,e,t),jC=()=>location.search,VC=({ssrSearch:e=""}={})=>rx(jC,()=>e),Rm=()=>location.pathname,FC=({ssrPath:e}={})=>rx(Rm,e?()=>e:Rm),IC=(e,{replace:t=!1,state:n=null}={})=>history[t?ph:hh](n,"",e),zC=(e={})=>[FC(e),IC],Nm=Symbol.for("wouter_v3");if(typeof history<"u"&&typeof window[Nm]>"u"){for(const e of[hh,ph]){const t=history[e];history[e]=function(){const n=t.apply(this,arguments),r=new Event(e);return r.arguments=arguments,dispatchEvent(r),n}}Object.defineProperty(window,Nm,{value:!0})}const BC=(e,t)=>t.toLowerCase().indexOf(e.toLowerCase())?"~"+t:t.slice(e.length)||"/",sx=(e="")=>e==="/"?"":e,$C=(e,t)=>e[0]==="~"?e.slice(1):sx(t)+e,UC=(e="",t)=>BC(Mm(sx(e)),Mm(t)),Mm=e=>{try{return decodeURI(e)}catch{return e}},ix={hook:zC,searchHook:VC,parser:vC,base:"",ssrPath:void 0,ssrSearch:void 0,hrefs:e=>e},ox=x.createContext(ix),ku=()=>x.useContext(ox),ax={},lx=x.createContext(ax),WC=()=>x.useContext(lx),mh=e=>{const[t,n]=e.hook(e);return[UC(e.base,t),nx((r,s)=>n($C(r,e.base),s))]},ux=(e,t,n,r)=>{const{pattern:s,keys:i}=t instanceof RegExp?{keys:!1,pattern:t}:e(t||"*",r),o=s.exec(n)||[],[a,...l]=o;return a!==void 0?[!0,(()=>{const u=i!==!1?Object.fromEntries(i.map((d,f)=>[d,l[f]])):o.groups;let c={...l};return u&&Object.assign(c,u),c})(),...r?[a]:[]]:[!1,null]},HC=({children:e,...t})=>{var c,d;const n=ku(),r=t.hook?ix:n;let s=r;const[i,o]=((c=t.ssrPath)==null?void 0:c.split("?"))??[];o&&(t.ssrSearch=o,t.ssrPath=i),t.hrefs=t.hrefs??((d=t.hook)==null?void 0:d.hrefs);let a=x.useRef({}),l=a.current,u=l;for(let f in r){const g=f==="base"?r[f]+(t[f]||""):t[f]||r[f];l===u&&g!==u[f]&&(a.current=u={...u}),u[f]=g,g!==r[f]&&(s=u)}return x.createElement(ox.Provider,{value:s,children:e})},Dm=({children:e,component:t},n)=>t?x.createElement(t,{params:n}):typeof e=="function"?e(n):e,ZC=e=>{let t=x.useRef(ax),n=t.current;for(const r in e)e[r]!==n[r]&&(n=e);return Object.keys(e).length===0&&(n=e),t.current=n},Om=({path:e,nest:t,match:n,...r})=>{const s=ku(),[i]=mh(s),[o,a,l]=n??ux(s.parser,e,i,t),u=ZC({...WC(),...a});if(!o)return null;const c=l?x.createElement(HC,{base:l},Dm(r,u)):Dm(r,u);return x.createElement(lx.Provider,{value:u,children:c})};x.forwardRef((e,t)=>{const n=ku(),[r,s]=mh(n),{to:i="",href:o=i,onClick:a,asChild:l,children:u,className:c,replace:d,state:f,...g}=e,v=nx(w=>{w.ctrlKey||w.metaKey||w.altKey||w.shiftKey||w.button!==0||(a==null||a(w),w.defaultPrevented||(w.preventDefault(),s(o,e)))}),y=n.hrefs(o[0]==="~"?o.slice(1):n.base+o,n);return l&&x.isValidElement(u)?x.cloneElement(u,{onClick:v,href:y}):x.createElement("a",{...g,onClick:v,href:y,className:c!=null&&c.call?c(r===o):c,children:u,ref:t})});const cx=e=>Array.isArray(e)?e.flatMap(t=>cx(t&&t.type===x.Fragment?t.props.children:t)):[e],KC=({children:e,location:t})=>{const n=ku(),[r]=mh(n);for(const s of cx(e)){let i=0;if(x.isValidElement(s)&&(i=ux(n.parser,s.props.path,t||r,s.props.nest))[0])return x.cloneElement(s,{match:i})}return null};var bu=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},Eu=typeof window>"u"||"Deno"in globalThis;function Gt(){}function qC(e,t){return typeof e=="function"?e(t):e}function GC(e){return typeof e=="number"&&e>=0&&e!==1/0}function QC(e,t){return Math.max(e+(t||0)-Date.now(),0)}function Lm(e,t){return typeof e=="function"?e(t):e}function YC(e,t){return typeof e=="function"?e(t):e}function jm(e,t){const{type:n="all",exact:r,fetchStatus:s,predicate:i,queryKey:o,stale:a}=e;if(o){if(r){if(t.queryHash!==gh(o,t.options))return!1}else if(!Oo(t.queryKey,o))return!1}if(n!=="all"){const l=t.isActive();if(n==="active"&&!l||n==="inactive"&&l)return!1}return!(typeof a=="boolean"&&t.isStale()!==a||s&&s!==t.state.fetchStatus||i&&!i(t))}function Vm(e,t){const{exact:n,status:r,predicate:s,mutationKey:i}=e;if(i){if(!t.options.mutationKey)return!1;if(n){if(Do(t.options.mutationKey)!==Do(i))return!1}else if(!Oo(t.options.mutationKey,i))return!1}return!(r&&t.state.status!==r||s&&!s(t))}function gh(e,t){return((t==null?void 0:t.queryKeyHashFn)||Do)(e)}function Do(e){return JSON.stringify(e,(t,n)=>Id(n)?Object.keys(n).sort().reduce((r,s)=>(r[s]=n[s],r),{}):n)}function Oo(e,t){return e===t?!0:typeof e!=typeof t?!1:e&&t&&typeof e=="object"&&typeof t=="object"?!Object.keys(t).some(n=>!Oo(e[n],t[n])):!1}function dx(e,t){if(e===t)return e;const n=Fm(e)&&Fm(t);if(n||Id(e)&&Id(t)){const r=n?e:Object.keys(e),s=r.length,i=n?t:Object.keys(t),o=i.length,a=n?[]:{};let l=0;for(let u=0;u<o;u++){const c=n?u:i[u];(!n&&r.includes(c)||n)&&e[c]===void 0&&t[c]===void 0?(a[c]=void 0,l++):(a[c]=dx(e[c],t[c]),a[c]===e[c]&&e[c]!==void 0&&l++)}return s===o&&l===s?e:a}return t}function Fm(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function Id(e){if(!Im(e))return!1;const t=e.constructor;if(t===void 0)return!0;const n=t.prototype;return!(!Im(n)||!n.hasOwnProperty("isPrototypeOf")||Object.getPrototypeOf(e)!==Object.prototype)}function Im(e){return Object.prototype.toString.call(e)==="[object Object]"}function XC(e){return new Promise(t=>{setTimeout(t,e)})}function JC(e,t,n){return typeof n.structuralSharing=="function"?n.structuralSharing(e,t):n.structuralSharing!==!1?dx(e,t):t}function ek(e,t,n=0){const r=[...e,t];return n&&r.length>n?r.slice(1):r}function tk(e,t,n=0){const r=[t,...e];return n&&r.length>n?r.slice(0,-1):r}var yh=Symbol();function fx(e,t){return!e.queryFn&&(t!=null&&t.initialPromise)?()=>t.initialPromise:!e.queryFn||e.queryFn===yh?()=>Promise.reject(new Error(`Missing queryFn: '${e.queryHash}'`)):e.queryFn}var Xr,ur,Xs,My,nk=(My=class extends bu{constructor(){super();he(this,Xr);he(this,ur);he(this,Xs);ae(this,Xs,t=>{if(!Eu&&window.addEventListener){const n=()=>t();return window.addEventListener("visibilitychange",n,!1),()=>{window.removeEventListener("visibilitychange",n)}}})}onSubscribe(){N(this,ur)||this.setEventListener(N(this,Xs))}onUnsubscribe(){var t;this.hasListeners()||((t=N(this,ur))==null||t.call(this),ae(this,ur,void 0))}setEventListener(t){var n;ae(this,Xs,t),(n=N(this,ur))==null||n.call(this),ae(this,ur,t(r=>{typeof r=="boolean"?this.setFocused(r):this.onFocus()}))}setFocused(t){N(this,Xr)!==t&&(ae(this,Xr,t),this.onFocus())}onFocus(){const t=this.isFocused();this.listeners.forEach(n=>{n(t)})}isFocused(){var t;return typeof N(this,Xr)=="boolean"?N(this,Xr):((t=globalThis.document)==null?void 0:t.visibilityState)!=="hidden"}},Xr=new WeakMap,ur=new WeakMap,Xs=new WeakMap,My),hx=new nk,Js,cr,ei,Dy,rk=(Dy=class extends bu{constructor(){super();he(this,Js,!0);he(this,cr);he(this,ei);ae(this,ei,t=>{if(!Eu&&window.addEventListener){const n=()=>t(!0),r=()=>t(!1);return window.addEventListener("online",n,!1),window.addEventListener("offline",r,!1),()=>{window.removeEventListener("online",n),window.removeEventListener("offline",r)}}})}onSubscribe(){N(this,cr)||this.setEventListener(N(this,ei))}onUnsubscribe(){var t;this.hasListeners()||((t=N(this,cr))==null||t.call(this),ae(this,cr,void 0))}setEventListener(t){var n;ae(this,ei,t),(n=N(this,cr))==null||n.call(this),ae(this,cr,t(this.setOnline.bind(this)))}setOnline(t){N(this,Js)!==t&&(ae(this,Js,t),this.listeners.forEach(r=>{r(t)}))}isOnline(){return N(this,Js)}},Js=new WeakMap,cr=new WeakMap,ei=new WeakMap,Dy),Vl=new rk;function sk(){let e,t;const n=new Promise((s,i)=>{e=s,t=i});n.status="pending",n.catch(()=>{});function r(s){Object.assign(n,s),delete n.resolve,delete n.reject}return n.resolve=s=>{r({status:"fulfilled",value:s}),e(s)},n.reject=s=>{r({status:"rejected",reason:s}),t(s)},n}function ik(e){return Math.min(1e3*2**e,3e4)}function px(e){return(e??"online")==="online"?Vl.isOnline():!0}var mx=class extends Error{constructor(e){super("CancelledError"),this.revert=e==null?void 0:e.revert,this.silent=e==null?void 0:e.silent}};function Cc(e){return e instanceof mx}function gx(e){let t=!1,n=0,r=!1,s;const i=sk(),o=y=>{var w;r||(f(new mx(y)),(w=e.abort)==null||w.call(e))},a=()=>{t=!0},l=()=>{t=!1},u=()=>hx.isFocused()&&(e.networkMode==="always"||Vl.isOnline())&&e.canRun(),c=()=>px(e.networkMode)&&e.canRun(),d=y=>{var w;r||(r=!0,(w=e.onSuccess)==null||w.call(e,y),s==null||s(),i.resolve(y))},f=y=>{var w;r||(r=!0,(w=e.onError)==null||w.call(e,y),s==null||s(),i.reject(y))},g=()=>new Promise(y=>{var w;s=p=>{(r||u())&&y(p)},(w=e.onPause)==null||w.call(e)}).then(()=>{var y;s=void 0,r||(y=e.onContinue)==null||y.call(e)}),v=()=>{if(r)return;let y;const w=n===0?e.initialPromise:void 0;try{y=w??e.fn()}catch(p){y=Promise.reject(p)}Promise.resolve(y).then(d).catch(p=>{var k;if(r)return;const h=e.retry??(Eu?0:3),m=e.retryDelay??ik,_=typeof m=="function"?m(n,p):m,C=h===!0||typeof h=="number"&&n<h||typeof h=="function"&&h(n,p);if(t||!C){f(p);return}n++,(k=e.onFail)==null||k.call(e,n,p),XC(_).then(()=>u()?void 0:g()).then(()=>{t?f(p):v()})})};return{promise:i,cancel:o,continue:()=>(s==null||s(),i),cancelRetry:a,continueRetry:l,canStart:c,start:()=>(c()?v():g().then(v),i)}}function ok(){let e=[],t=0,n=a=>{a()},r=a=>{a()},s=a=>setTimeout(a,0);const i=a=>{t?e.push(a):s(()=>{n(a)})},o=()=>{const a=e;e=[],a.length&&s(()=>{r(()=>{a.forEach(l=>{n(l)})})})};return{batch:a=>{let l;t++;try{l=a()}finally{t--,t||o()}return l},batchCalls:a=>(...l)=>{i(()=>{a(...l)})},schedule:i,setNotifyFunction:a=>{n=a},setBatchNotifyFunction:a=>{r=a},setScheduler:a=>{s=a}}}var dt=ok(),Jr,Oy,yx=(Oy=class{constructor(){he(this,Jr)}destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),GC(this.gcTime)&&ae(this,Jr,setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,e??(Eu?1/0:5*60*1e3))}clearGcTimeout(){N(this,Jr)&&(clearTimeout(N(this,Jr)),ae(this,Jr,void 0))}},Jr=new WeakMap,Oy),ti,ni,Ft,et,ea,es,Qt,Mn,Ly,ak=(Ly=class extends yx{constructor(t){super();he(this,Qt);he(this,ti);he(this,ni);he(this,Ft);he(this,et);he(this,ea);he(this,es);ae(this,es,!1),ae(this,ea,t.defaultOptions),this.setOptions(t.options),this.observers=[],ae(this,Ft,t.cache),this.queryKey=t.queryKey,this.queryHash=t.queryHash,ae(this,ti,uk(this.options)),this.state=t.state??N(this,ti),this.scheduleGc()}get meta(){return this.options.meta}get promise(){var t;return(t=N(this,et))==null?void 0:t.promise}setOptions(t){this.options={...N(this,ea),...t},this.updateGcTime(this.options.gcTime)}optionalRemove(){!this.observers.length&&this.state.fetchStatus==="idle"&&N(this,Ft).remove(this)}setData(t,n){const r=JC(this.state.data,t,this.options);return Ye(this,Qt,Mn).call(this,{data:r,type:"success",dataUpdatedAt:n==null?void 0:n.updatedAt,manual:n==null?void 0:n.manual}),r}setState(t,n){Ye(this,Qt,Mn).call(this,{type:"setState",state:t,setStateOptions:n})}cancel(t){var r,s;const n=(r=N(this,et))==null?void 0:r.promise;return(s=N(this,et))==null||s.cancel(t),n?n.then(Gt).catch(Gt):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(N(this,ti))}isActive(){return this.observers.some(t=>YC(t.options.enabled,this)!==!1)}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===yh||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStale(){return this.state.isInvalidated?!0:this.getObserversCount()>0?this.observers.some(t=>t.getCurrentResult().isStale):this.state.data===void 0}isStaleByTime(t=0){return this.state.isInvalidated||this.state.data===void 0||!QC(this.state.dataUpdatedAt,t)}onFocus(){var n;const t=this.observers.find(r=>r.shouldFetchOnWindowFocus());t==null||t.refetch({cancelRefetch:!1}),(n=N(this,et))==null||n.continue()}onOnline(){var n;const t=this.observers.find(r=>r.shouldFetchOnReconnect());t==null||t.refetch({cancelRefetch:!1}),(n=N(this,et))==null||n.continue()}addObserver(t){this.observers.includes(t)||(this.observers.push(t),this.clearGcTimeout(),N(this,Ft).notify({type:"observerAdded",query:this,observer:t}))}removeObserver(t){this.observers.includes(t)&&(this.observers=this.observers.filter(n=>n!==t),this.observers.length||(N(this,et)&&(N(this,es)?N(this,et).cancel({revert:!0}):N(this,et).cancelRetry()),this.scheduleGc()),N(this,Ft).notify({type:"observerRemoved",query:this,observer:t}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||Ye(this,Qt,Mn).call(this,{type:"invalidate"})}fetch(t,n){var l,u,c;if(this.state.fetchStatus!=="idle"){if(this.state.data!==void 0&&(n!=null&&n.cancelRefetch))this.cancel({silent:!0});else if(N(this,et))return N(this,et).continueRetry(),N(this,et).promise}if(t&&this.setOptions(t),!this.options.queryFn){const d=this.observers.find(f=>f.options.queryFn);d&&this.setOptions(d.options)}const r=new AbortController,s=d=>{Object.defineProperty(d,"signal",{enumerable:!0,get:()=>(ae(this,es,!0),r.signal)})},i=()=>{const d=fx(this.options,n),f={queryKey:this.queryKey,meta:this.meta};return s(f),ae(this,es,!1),this.options.persister?this.options.persister(d,f,this):d(f)},o={fetchOptions:n,options:this.options,queryKey:this.queryKey,state:this.state,fetchFn:i};s(o),(l=this.options.behavior)==null||l.onFetch(o,this),ae(this,ni,this.state),(this.state.fetchStatus==="idle"||this.state.fetchMeta!==((u=o.fetchOptions)==null?void 0:u.meta))&&Ye(this,Qt,Mn).call(this,{type:"fetch",meta:(c=o.fetchOptions)==null?void 0:c.meta});const a=d=>{var f,g,v,y;Cc(d)&&d.silent||Ye(this,Qt,Mn).call(this,{type:"error",error:d}),Cc(d)||((g=(f=N(this,Ft).config).onError)==null||g.call(f,d,this),(y=(v=N(this,Ft).config).onSettled)==null||y.call(v,this.state.data,d,this)),this.scheduleGc()};return ae(this,et,gx({initialPromise:n==null?void 0:n.initialPromise,fn:o.fetchFn,abort:r.abort.bind(r),onSuccess:d=>{var f,g,v,y;if(d===void 0){a(new Error(`${this.queryHash} data is undefined`));return}try{this.setData(d)}catch(w){a(w);return}(g=(f=N(this,Ft).config).onSuccess)==null||g.call(f,d,this),(y=(v=N(this,Ft).config).onSettled)==null||y.call(v,d,this.state.error,this),this.scheduleGc()},onError:a,onFail:(d,f)=>{Ye(this,Qt,Mn).call(this,{type:"failed",failureCount:d,error:f})},onPause:()=>{Ye(this,Qt,Mn).call(this,{type:"pause"})},onContinue:()=>{Ye(this,Qt,Mn).call(this,{type:"continue"})},retry:o.options.retry,retryDelay:o.options.retryDelay,networkMode:o.options.networkMode,canRun:()=>!0})),N(this,et).start()}},ti=new WeakMap,ni=new WeakMap,Ft=new WeakMap,et=new WeakMap,ea=new WeakMap,es=new WeakMap,Qt=new WeakSet,Mn=function(t){const n=r=>{switch(t.type){case"failed":return{...r,fetchFailureCount:t.failureCount,fetchFailureReason:t.error};case"pause":return{...r,fetchStatus:"paused"};case"continue":return{...r,fetchStatus:"fetching"};case"fetch":return{...r,...lk(r.data,this.options),fetchMeta:t.meta??null};case"success":return{...r,data:t.data,dataUpdateCount:r.dataUpdateCount+1,dataUpdatedAt:t.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!t.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const s=t.error;return Cc(s)&&s.revert&&N(this,ni)?{...N(this,ni),fetchStatus:"idle"}:{...r,error:s,errorUpdateCount:r.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:r.fetchFailureCount+1,fetchFailureReason:s,fetchStatus:"idle",status:"error"};case"invalidate":return{...r,isInvalidated:!0};case"setState":return{...r,...t.state}}};this.state=n(this.state),dt.batch(()=>{this.observers.forEach(r=>{r.onQueryUpdate()}),N(this,Ft).notify({query:this,type:"updated",action:t})})},Ly);function lk(e,t){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:px(t.networkMode)?"fetching":"paused",...e===void 0&&{error:null,status:"pending"}}}function uk(e){const t=typeof e.initialData=="function"?e.initialData():e.initialData,n=t!==void 0,r=n?typeof e.initialDataUpdatedAt=="function"?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:n?r??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:n?"success":"pending",fetchStatus:"idle"}}var pn,jy,ck=(jy=class extends bu{constructor(t={}){super();he(this,pn);this.config=t,ae(this,pn,new Map)}build(t,n,r){const s=n.queryKey,i=n.queryHash??gh(s,n);let o=this.get(i);return o||(o=new ak({cache:this,queryKey:s,queryHash:i,options:t.defaultQueryOptions(n),state:r,defaultOptions:t.getQueryDefaults(s)}),this.add(o)),o}add(t){N(this,pn).has(t.queryHash)||(N(this,pn).set(t.queryHash,t),this.notify({type:"added",query:t}))}remove(t){const n=N(this,pn).get(t.queryHash);n&&(t.destroy(),n===t&&N(this,pn).delete(t.queryHash),this.notify({type:"removed",query:t}))}clear(){dt.batch(()=>{this.getAll().forEach(t=>{this.remove(t)})})}get(t){return N(this,pn).get(t)}getAll(){return[...N(this,pn).values()]}find(t){const n={exact:!0,...t};return this.getAll().find(r=>jm(n,r))}findAll(t={}){const n=this.getAll();return Object.keys(t).length>0?n.filter(r=>jm(t,r)):n}notify(t){dt.batch(()=>{this.listeners.forEach(n=>{n(t)})})}onFocus(){dt.batch(()=>{this.getAll().forEach(t=>{t.onFocus()})})}onOnline(){dt.batch(()=>{this.getAll().forEach(t=>{t.onOnline()})})}},pn=new WeakMap,jy),mn,at,ts,gn,nr,Vy,dk=(Vy=class extends yx{constructor(t){super();he(this,gn);he(this,mn);he(this,at);he(this,ts);this.mutationId=t.mutationId,ae(this,at,t.mutationCache),ae(this,mn,[]),this.state=t.state||fk(),this.setOptions(t.options),this.scheduleGc()}setOptions(t){this.options=t,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(t){N(this,mn).includes(t)||(N(this,mn).push(t),this.clearGcTimeout(),N(this,at).notify({type:"observerAdded",mutation:this,observer:t}))}removeObserver(t){ae(this,mn,N(this,mn).filter(n=>n!==t)),this.scheduleGc(),N(this,at).notify({type:"observerRemoved",mutation:this,observer:t})}optionalRemove(){N(this,mn).length||(this.state.status==="pending"?this.scheduleGc():N(this,at).remove(this))}continue(){var t;return((t=N(this,ts))==null?void 0:t.continue())??this.execute(this.state.variables)}async execute(t){var s,i,o,a,l,u,c,d,f,g,v,y,w,p,h,m,_,C,k,P;ae(this,ts,gx({fn:()=>this.options.mutationFn?this.options.mutationFn(t):Promise.reject(new Error("No mutationFn found")),onFail:(b,F)=>{Ye(this,gn,nr).call(this,{type:"failed",failureCount:b,error:F})},onPause:()=>{Ye(this,gn,nr).call(this,{type:"pause"})},onContinue:()=>{Ye(this,gn,nr).call(this,{type:"continue"})},retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>N(this,at).canRun(this)}));const n=this.state.status==="pending",r=!N(this,ts).canStart();try{if(!n){Ye(this,gn,nr).call(this,{type:"pending",variables:t,isPaused:r}),await((i=(s=N(this,at).config).onMutate)==null?void 0:i.call(s,t,this));const F=await((a=(o=this.options).onMutate)==null?void 0:a.call(o,t));F!==this.state.context&&Ye(this,gn,nr).call(this,{type:"pending",context:F,variables:t,isPaused:r})}const b=await N(this,ts).start();return await((u=(l=N(this,at).config).onSuccess)==null?void 0:u.call(l,b,t,this.state.context,this)),await((d=(c=this.options).onSuccess)==null?void 0:d.call(c,b,t,this.state.context)),await((g=(f=N(this,at).config).onSettled)==null?void 0:g.call(f,b,null,this.state.variables,this.state.context,this)),await((y=(v=this.options).onSettled)==null?void 0:y.call(v,b,null,t,this.state.context)),Ye(this,gn,nr).call(this,{type:"success",data:b}),b}catch(b){try{throw await((p=(w=N(this,at).config).onError)==null?void 0:p.call(w,b,t,this.state.context,this)),await((m=(h=this.options).onError)==null?void 0:m.call(h,b,t,this.state.context)),await((C=(_=N(this,at).config).onSettled)==null?void 0:C.call(_,void 0,b,this.state.variables,this.state.context,this)),await((P=(k=this.options).onSettled)==null?void 0:P.call(k,void 0,b,t,this.state.context)),b}finally{Ye(this,gn,nr).call(this,{type:"error",error:b})}}finally{N(this,at).runNext(this)}}},mn=new WeakMap,at=new WeakMap,ts=new WeakMap,gn=new WeakSet,nr=function(t){const n=r=>{switch(t.type){case"failed":return{...r,failureCount:t.failureCount,failureReason:t.error};case"pause":return{...r,isPaused:!0};case"continue":return{...r,isPaused:!1};case"pending":return{...r,context:t.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:t.isPaused,status:"pending",variables:t.variables,submittedAt:Date.now()};case"success":return{...r,data:t.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...r,data:void 0,error:t.error,failureCount:r.failureCount+1,failureReason:t.error,isPaused:!1,status:"error"}}};this.state=n(this.state),dt.batch(()=>{N(this,mn).forEach(r=>{r.onMutationUpdate(t)}),N(this,at).notify({mutation:this,type:"updated",action:t})})},Vy);function fk(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}var Ct,ta,Fy,hk=(Fy=class extends bu{constructor(t={}){super();he(this,Ct);he(this,ta);this.config=t,ae(this,Ct,new Map),ae(this,ta,Date.now())}build(t,n,r){const s=new dk({mutationCache:this,mutationId:++va(this,ta)._,options:t.defaultMutationOptions(n),state:r});return this.add(s),s}add(t){const n=ja(t),r=N(this,Ct).get(n)??[];r.push(t),N(this,Ct).set(n,r),this.notify({type:"added",mutation:t})}remove(t){var r;const n=ja(t);if(N(this,Ct).has(n)){const s=(r=N(this,Ct).get(n))==null?void 0:r.filter(i=>i!==t);s&&(s.length===0?N(this,Ct).delete(n):N(this,Ct).set(n,s))}this.notify({type:"removed",mutation:t})}canRun(t){var r;const n=(r=N(this,Ct).get(ja(t)))==null?void 0:r.find(s=>s.state.status==="pending");return!n||n===t}runNext(t){var r;const n=(r=N(this,Ct).get(ja(t)))==null?void 0:r.find(s=>s!==t&&s.state.isPaused);return(n==null?void 0:n.continue())??Promise.resolve()}clear(){dt.batch(()=>{this.getAll().forEach(t=>{this.remove(t)})})}getAll(){return[...N(this,Ct).values()].flat()}find(t){const n={exact:!0,...t};return this.getAll().find(r=>Vm(n,r))}findAll(t={}){return this.getAll().filter(n=>Vm(t,n))}notify(t){dt.batch(()=>{this.listeners.forEach(n=>{n(t)})})}resumePausedMutations(){const t=this.getAll().filter(n=>n.state.isPaused);return dt.batch(()=>Promise.all(t.map(n=>n.continue().catch(Gt))))}},Ct=new WeakMap,ta=new WeakMap,Fy);function ja(e){var t;return((t=e.options.scope)==null?void 0:t.id)??String(e.mutationId)}function zm(e){return{onFetch:(t,n)=>{var c,d,f,g,v;const r=t.options,s=(f=(d=(c=t.fetchOptions)==null?void 0:c.meta)==null?void 0:d.fetchMore)==null?void 0:f.direction,i=((g=t.state.data)==null?void 0:g.pages)||[],o=((v=t.state.data)==null?void 0:v.pageParams)||[];let a={pages:[],pageParams:[]},l=0;const u=async()=>{let y=!1;const w=m=>{Object.defineProperty(m,"signal",{enumerable:!0,get:()=>(t.signal.aborted?y=!0:t.signal.addEventListener("abort",()=>{y=!0}),t.signal)})},p=fx(t.options,t.fetchOptions),h=async(m,_,C)=>{if(y)return Promise.reject();if(_==null&&m.pages.length)return Promise.resolve(m);const k={queryKey:t.queryKey,pageParam:_,direction:C?"backward":"forward",meta:t.options.meta};w(k);const P=await p(k),{maxPages:b}=t.options,F=C?tk:ek;return{pages:F(m.pages,P,b),pageParams:F(m.pageParams,_,b)}};if(s&&i.length){const m=s==="backward",_=m?pk:Bm,C={pages:i,pageParams:o},k=_(r,C);a=await h(C,k,m)}else{const m=e??i.length;do{const _=l===0?o[0]??r.initialPageParam:Bm(r,a);if(l>0&&_==null)break;a=await h(a,_),l++}while(l<m)}return a};t.options.persister?t.fetchFn=()=>{var y,w;return(w=(y=t.options).persister)==null?void 0:w.call(y,u,{queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},n)}:t.fetchFn=u}}}function Bm(e,{pages:t,pageParams:n}){const r=t.length-1;return t.length>0?e.getNextPageParam(t[r],t,n[r],n):void 0}function pk(e,{pages:t,pageParams:n}){var r;return t.length>0?(r=e.getPreviousPageParam)==null?void 0:r.call(e,t[0],t,n[0],n):void 0}var Ae,dr,fr,ri,si,hr,ii,oi,Iy,mk=(Iy=class{constructor(e={}){he(this,Ae);he(this,dr);he(this,fr);he(this,ri);he(this,si);he(this,hr);he(this,ii);he(this,oi);ae(this,Ae,e.queryCache||new ck),ae(this,dr,e.mutationCache||new hk),ae(this,fr,e.defaultOptions||{}),ae(this,ri,new Map),ae(this,si,new Map),ae(this,hr,0)}mount(){va(this,hr)._++,N(this,hr)===1&&(ae(this,ii,hx.subscribe(async e=>{e&&(await this.resumePausedMutations(),N(this,Ae).onFocus())})),ae(this,oi,Vl.subscribe(async e=>{e&&(await this.resumePausedMutations(),N(this,Ae).onOnline())})))}unmount(){var e,t;va(this,hr)._--,N(this,hr)===0&&((e=N(this,ii))==null||e.call(this),ae(this,ii,void 0),(t=N(this,oi))==null||t.call(this),ae(this,oi,void 0))}isFetching(e){return N(this,Ae).findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return N(this,dr).findAll({...e,status:"pending"}).length}getQueryData(e){var n;const t=this.defaultQueryOptions({queryKey:e});return(n=N(this,Ae).get(t.queryHash))==null?void 0:n.state.data}ensureQueryData(e){const t=this.getQueryData(e.queryKey);if(t===void 0)return this.fetchQuery(e);{const n=this.defaultQueryOptions(e),r=N(this,Ae).build(this,n);return e.revalidateIfStale&&r.isStaleByTime(Lm(n.staleTime,r))&&this.prefetchQuery(n),Promise.resolve(t)}}getQueriesData(e){return N(this,Ae).findAll(e).map(({queryKey:t,state:n})=>{const r=n.data;return[t,r]})}setQueryData(e,t,n){const r=this.defaultQueryOptions({queryKey:e}),s=N(this,Ae).get(r.queryHash),i=s==null?void 0:s.state.data,o=qC(t,i);if(o!==void 0)return N(this,Ae).build(this,r).setData(o,{...n,manual:!0})}setQueriesData(e,t,n){return dt.batch(()=>N(this,Ae).findAll(e).map(({queryKey:r})=>[r,this.setQueryData(r,t,n)]))}getQueryState(e){var n;const t=this.defaultQueryOptions({queryKey:e});return(n=N(this,Ae).get(t.queryHash))==null?void 0:n.state}removeQueries(e){const t=N(this,Ae);dt.batch(()=>{t.findAll(e).forEach(n=>{t.remove(n)})})}resetQueries(e,t){const n=N(this,Ae),r={type:"active",...e};return dt.batch(()=>(n.findAll(e).forEach(s=>{s.reset()}),this.refetchQueries(r,t)))}cancelQueries(e={},t={}){const n={revert:!0,...t},r=dt.batch(()=>N(this,Ae).findAll(e).map(s=>s.cancel(n)));return Promise.all(r).then(Gt).catch(Gt)}invalidateQueries(e={},t={}){return dt.batch(()=>{if(N(this,Ae).findAll(e).forEach(r=>{r.invalidate()}),e.refetchType==="none")return Promise.resolve();const n={...e,type:e.refetchType??e.type??"active"};return this.refetchQueries(n,t)})}refetchQueries(e={},t){const n={...t,cancelRefetch:(t==null?void 0:t.cancelRefetch)??!0},r=dt.batch(()=>N(this,Ae).findAll(e).filter(s=>!s.isDisabled()).map(s=>{let i=s.fetch(void 0,n);return n.throwOnError||(i=i.catch(Gt)),s.state.fetchStatus==="paused"?Promise.resolve():i}));return Promise.all(r).then(Gt)}fetchQuery(e){const t=this.defaultQueryOptions(e);t.retry===void 0&&(t.retry=!1);const n=N(this,Ae).build(this,t);return n.isStaleByTime(Lm(t.staleTime,n))?n.fetch(t):Promise.resolve(n.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(Gt).catch(Gt)}fetchInfiniteQuery(e){return e.behavior=zm(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(Gt).catch(Gt)}ensureInfiniteQueryData(e){return e.behavior=zm(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return Vl.isOnline()?N(this,dr).resumePausedMutations():Promise.resolve()}getQueryCache(){return N(this,Ae)}getMutationCache(){return N(this,dr)}getDefaultOptions(){return N(this,fr)}setDefaultOptions(e){ae(this,fr,e)}setQueryDefaults(e,t){N(this,ri).set(Do(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){const t=[...N(this,ri).values()];let n={};return t.forEach(r=>{Oo(e,r.queryKey)&&(n={...n,...r.defaultOptions})}),n}setMutationDefaults(e,t){N(this,si).set(Do(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){const t=[...N(this,si).values()];let n={};return t.forEach(r=>{Oo(e,r.mutationKey)&&(n={...n,...r.defaultOptions})}),n}defaultQueryOptions(e){if(e._defaulted)return e;const t={...N(this,fr).queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=gh(t.queryKey,t)),t.refetchOnReconnect===void 0&&(t.refetchOnReconnect=t.networkMode!=="always"),t.throwOnError===void 0&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t.enabled!==!0&&t.queryFn===yh&&(t.enabled=!1),t}defaultMutationOptions(e){return e!=null&&e._defaulted?e:{...N(this,fr).mutations,...(e==null?void 0:e.mutationKey)&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){N(this,Ae).clear(),N(this,dr).clear()}},Ae=new WeakMap,dr=new WeakMap,fr=new WeakMap,ri=new WeakMap,si=new WeakMap,hr=new WeakMap,ii=new WeakMap,oi=new WeakMap,Iy),gk=x.createContext(void 0),yk=({client:e,children:t})=>(x.useEffect(()=>(e.mount(),()=>{e.unmount()}),[e]),S.jsx(gk.Provider,{value:e,children:t}));async function vk(e){if(!e.ok){const t=await e.text()||e.statusText;throw new Error(`${e.status}: ${t}`)}}const xk=({on401:e})=>async({queryKey:t})=>{const n=await fetch(t[0],{credentials:"include"});return e==="returnNull"&&n.status===401?null:(await vk(n),await n.json())},wk=new mk({defaultOptions:{queries:{queryFn:xk({on401:"throw"}),refetchInterval:!1,refetchOnWindowFocus:!1,staleTime:1/0,retry:!1},mutations:{retry:!1}}}),Sk=1,_k=1e6;let kc=0;function Tk(){return kc=(kc+1)%Number.MAX_SAFE_INTEGER,kc.toString()}const bc=new Map,$m=e=>{if(bc.has(e))return;const t=setTimeout(()=>{bc.delete(e),uo({type:"REMOVE_TOAST",toastId:e})},_k);bc.set(e,t)},Ck=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,Sk)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(n=>n.id===t.toast.id?{...n,...t.toast}:n)};case"DISMISS_TOAST":{const{toastId:n}=t;return n?$m(n):e.toasts.forEach(r=>{$m(r.id)}),{...e,toasts:e.toasts.map(r=>r.id===n||n===void 0?{...r,open:!1}:r)}}case"REMOVE_TOAST":return t.toastId===void 0?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(n=>n.id!==t.toastId)}}},nl=[];let rl={toasts:[]};function uo(e){rl=Ck(rl,e),nl.forEach(t=>{t(rl)})}function kk({...e}){const t=Tk(),n=s=>uo({type:"UPDATE_TOAST",toast:{...s,id:t}}),r=()=>uo({type:"DISMISS_TOAST",toastId:t});return uo({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:s=>{s||r()}}}),{id:t,dismiss:r,update:n}}function vx(){const[e,t]=x.useState(rl);return x.useEffect(()=>(nl.push(t),()=>{const n=nl.indexOf(t);n>-1&&nl.splice(n,1)}),[e]),{...e,toast:kk,dismiss:n=>uo({type:"DISMISS_TOAST",toastId:n})}}function Be(e,t,{checkForDefaultPrevented:n=!0}={}){return function(s){if(e==null||e(s),n===!1||!s.defaultPrevented)return t==null?void 0:t(s)}}function Um(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}function xx(...e){return t=>{let n=!1;const r=e.map(s=>{const i=Um(s,t);return!n&&typeof i=="function"&&(n=!0),i});if(n)return()=>{for(let s=0;s<r.length;s++){const i=r[s];typeof i=="function"?i():Um(e[s],null)}}}}function an(...e){return x.useCallback(xx(...e),e)}function Pu(e,t=[]){let n=[];function r(i,o){const a=x.createContext(o),l=n.length;n=[...n,o];const u=d=>{var p;const{scope:f,children:g,...v}=d,y=((p=f==null?void 0:f[e])==null?void 0:p[l])||a,w=x.useMemo(()=>v,Object.values(v));return S.jsx(y.Provider,{value:w,children:g})};u.displayName=i+"Provider";function c(d,f){var y;const g=((y=f==null?void 0:f[e])==null?void 0:y[l])||a,v=x.useContext(g);if(v)return v;if(o!==void 0)return o;throw new Error(`\`${d}\` must be used within \`${i}\``)}return[u,c]}const s=()=>{const i=n.map(o=>x.createContext(o));return function(a){const l=(a==null?void 0:a[e])||i;return x.useMemo(()=>({[`__scope${e}`]:{...a,[e]:l}}),[a,l])}};return s.scopeName=e,[r,bk(s,...t)]}function bk(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(s=>({useScope:s(),scopeName:s.scopeName}));return function(i){const o=r.reduce((a,{useScope:l,scopeName:u})=>{const d=l(i)[`__scope${u}`];return{...a,...d}},{});return x.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}function Fl(e){const t=Ek(e),n=x.forwardRef((r,s)=>{const{children:i,...o}=r,a=x.Children.toArray(i),l=a.find(Ak);if(l){const u=l.props.children,c=a.map(d=>d===l?x.Children.count(u)>1?x.Children.only(null):x.isValidElement(u)?u.props.children:null:d);return S.jsx(t,{...o,ref:s,children:x.isValidElement(u)?x.cloneElement(u,void 0,c):null})}return S.jsx(t,{...o,ref:s,children:i})});return n.displayName=`${e}.Slot`,n}var wx=Fl("Slot");function Ek(e){const t=x.forwardRef((n,r)=>{const{children:s,...i}=n;if(x.isValidElement(s)){const o=Nk(s),a=Rk(i,s.props);return s.type!==x.Fragment&&(a.ref=r?xx(r,o):o),x.cloneElement(s,a)}return x.Children.count(s)>1?x.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}var Sx=Symbol("radix.slottable");function Pk(e){const t=({children:n})=>S.jsx(S.Fragment,{children:n});return t.displayName=`${e}.Slottable`,t.__radixId=Sx,t}function Ak(e){return x.isValidElement(e)&&typeof e.type=="function"&&"__radixId"in e.type&&e.type.__radixId===Sx}function Rk(e,t){const n={...t};for(const r in t){const s=e[r],i=t[r];/^on[A-Z]/.test(r)?s&&i?n[r]=(...a)=>{i(...a),s(...a)}:s&&(n[r]=s):r==="style"?n[r]={...s,...i}:r==="className"&&(n[r]=[s,i].filter(Boolean).join(" "))}return{...e,...n}}function Nk(e){var r,s;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(s=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:s.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}function Mk(e){const t=e+"CollectionProvider",[n,r]=Pu(t),[s,i]=n(t,{collectionRef:{current:null},itemMap:new Map}),o=y=>{const{scope:w,children:p}=y,h=se.useRef(null),m=se.useRef(new Map).current;return S.jsx(s,{scope:w,itemMap:m,collectionRef:h,children:p})};o.displayName=t;const a=e+"CollectionSlot",l=Fl(a),u=se.forwardRef((y,w)=>{const{scope:p,children:h}=y,m=i(a,p),_=an(w,m.collectionRef);return S.jsx(l,{ref:_,children:h})});u.displayName=a;const c=e+"CollectionItemSlot",d="data-radix-collection-item",f=Fl(c),g=se.forwardRef((y,w)=>{const{scope:p,children:h,...m}=y,_=se.useRef(null),C=an(w,_),k=i(c,p);return se.useEffect(()=>(k.itemMap.set(_,{ref:_,...m}),()=>void k.itemMap.delete(_))),S.jsx(f,{[d]:"",ref:C,children:h})});g.displayName=c;function v(y){const w=i(e+"CollectionConsumer",y);return se.useCallback(()=>{const h=w.collectionRef.current;if(!h)return[];const m=Array.from(h.querySelectorAll(`[${d}]`));return Array.from(w.itemMap.values()).sort((k,P)=>m.indexOf(k.ref.current)-m.indexOf(P.ref.current))},[w.collectionRef,w.itemMap])}return[{Provider:o,Slot:u,ItemSlot:g},v,r]}var Dk=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],gt=Dk.reduce((e,t)=>{const n=Fl(`Primitive.${t}`),r=x.forwardRef((s,i)=>{const{asChild:o,...a}=s,l=o?n:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),S.jsx(l,{...a,ref:i})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function _x(e,t){e&&aa.flushSync(()=>e.dispatchEvent(t))}function kn(e){const t=x.useRef(e);return x.useEffect(()=>{t.current=e}),x.useMemo(()=>(...n)=>{var r;return(r=t.current)==null?void 0:r.call(t,...n)},[])}function Ok(e,t=globalThis==null?void 0:globalThis.document){const n=kn(e);x.useEffect(()=>{const r=s=>{s.key==="Escape"&&n(s)};return t.addEventListener("keydown",r,{capture:!0}),()=>t.removeEventListener("keydown",r,{capture:!0})},[n,t])}var Lk="DismissableLayer",zd="dismissableLayer.update",jk="dismissableLayer.pointerDownOutside",Vk="dismissableLayer.focusOutside",Wm,Tx=x.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),vh=x.forwardRef((e,t)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:s,onFocusOutside:i,onInteractOutside:o,onDismiss:a,...l}=e,u=x.useContext(Tx),[c,d]=x.useState(null),f=(c==null?void 0:c.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,g]=x.useState({}),v=an(t,P=>d(P)),y=Array.from(u.layers),[w]=[...u.layersWithOutsidePointerEventsDisabled].slice(-1),p=y.indexOf(w),h=c?y.indexOf(c):-1,m=u.layersWithOutsidePointerEventsDisabled.size>0,_=h>=p,C=Ik(P=>{const b=P.target,F=[...u.branches].some(j=>j.contains(b));!_||F||(s==null||s(P),o==null||o(P),P.defaultPrevented||a==null||a())},f),k=zk(P=>{const b=P.target;[...u.branches].some(j=>j.contains(b))||(i==null||i(P),o==null||o(P),P.defaultPrevented||a==null||a())},f);return Ok(P=>{h===u.layers.size-1&&(r==null||r(P),!P.defaultPrevented&&a&&(P.preventDefault(),a()))},f),x.useEffect(()=>{if(c)return n&&(u.layersWithOutsidePointerEventsDisabled.size===0&&(Wm=f.body.style.pointerEvents,f.body.style.pointerEvents="none"),u.layersWithOutsidePointerEventsDisabled.add(c)),u.layers.add(c),Hm(),()=>{n&&u.layersWithOutsidePointerEventsDisabled.size===1&&(f.body.style.pointerEvents=Wm)}},[c,f,n,u]),x.useEffect(()=>()=>{c&&(u.layers.delete(c),u.layersWithOutsidePointerEventsDisabled.delete(c),Hm())},[c,u]),x.useEffect(()=>{const P=()=>g({});return document.addEventListener(zd,P),()=>document.removeEventListener(zd,P)},[]),S.jsx(gt.div,{...l,ref:v,style:{pointerEvents:m?_?"auto":"none":void 0,...e.style},onFocusCapture:Be(e.onFocusCapture,k.onFocusCapture),onBlurCapture:Be(e.onBlurCapture,k.onBlurCapture),onPointerDownCapture:Be(e.onPointerDownCapture,C.onPointerDownCapture)})});vh.displayName=Lk;var Fk="DismissableLayerBranch",Cx=x.forwardRef((e,t)=>{const n=x.useContext(Tx),r=x.useRef(null),s=an(t,r);return x.useEffect(()=>{const i=r.current;if(i)return n.branches.add(i),()=>{n.branches.delete(i)}},[n.branches]),S.jsx(gt.div,{...e,ref:s})});Cx.displayName=Fk;function Ik(e,t=globalThis==null?void 0:globalThis.document){const n=kn(e),r=x.useRef(!1),s=x.useRef(()=>{});return x.useEffect(()=>{const i=a=>{if(a.target&&!r.current){let l=function(){kx(jk,n,u,{discrete:!0})};const u={originalEvent:a};a.pointerType==="touch"?(t.removeEventListener("click",s.current),s.current=l,t.addEventListener("click",s.current,{once:!0})):l()}else t.removeEventListener("click",s.current);r.current=!1},o=window.setTimeout(()=>{t.addEventListener("pointerdown",i)},0);return()=>{window.clearTimeout(o),t.removeEventListener("pointerdown",i),t.removeEventListener("click",s.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}function zk(e,t=globalThis==null?void 0:globalThis.document){const n=kn(e),r=x.useRef(!1);return x.useEffect(()=>{const s=i=>{i.target&&!r.current&&kx(Vk,n,{originalEvent:i},{discrete:!1})};return t.addEventListener("focusin",s),()=>t.removeEventListener("focusin",s)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function Hm(){const e=new CustomEvent(zd);document.dispatchEvent(e)}function kx(e,t,n,{discrete:r}){const s=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&s.addEventListener(e,t,{once:!0}),r?_x(s,i):s.dispatchEvent(i)}var Bk=vh,$k=Cx,ds=globalThis!=null&&globalThis.document?x.useLayoutEffect:()=>{},Uk="Portal",bx=x.forwardRef((e,t)=>{var a;const{container:n,...r}=e,[s,i]=x.useState(!1);ds(()=>i(!0),[]);const o=n||s&&((a=globalThis==null?void 0:globalThis.document)==null?void 0:a.body);return o?yC.createPortal(S.jsx(gt.div,{...r,ref:t}),o):null});bx.displayName=Uk;function Wk(e,t){return x.useReducer((n,r)=>t[n][r]??n,e)}var xh=e=>{const{present:t,children:n}=e,r=Hk(t),s=typeof n=="function"?n({present:r.isPresent}):x.Children.only(n),i=an(r.ref,Zk(s));return typeof n=="function"||r.isPresent?x.cloneElement(s,{ref:i}):null};xh.displayName="Presence";function Hk(e){const[t,n]=x.useState(),r=x.useRef({}),s=x.useRef(e),i=x.useRef("none"),o=e?"mounted":"unmounted",[a,l]=Wk(o,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return x.useEffect(()=>{const u=Va(r.current);i.current=a==="mounted"?u:"none"},[a]),ds(()=>{const u=r.current,c=s.current;if(c!==e){const f=i.current,g=Va(u);e?l("MOUNT"):g==="none"||(u==null?void 0:u.display)==="none"?l("UNMOUNT"):l(c&&f!==g?"ANIMATION_OUT":"UNMOUNT"),s.current=e}},[e,l]),ds(()=>{if(t){let u;const c=t.ownerDocument.defaultView??window,d=g=>{const y=Va(r.current).includes(g.animationName);if(g.target===t&&y&&(l("ANIMATION_END"),!s.current)){const w=t.style.animationFillMode;t.style.animationFillMode="forwards",u=c.setTimeout(()=>{t.style.animationFillMode==="forwards"&&(t.style.animationFillMode=w)})}},f=g=>{g.target===t&&(i.current=Va(r.current))};return t.addEventListener("animationstart",f),t.addEventListener("animationcancel",d),t.addEventListener("animationend",d),()=>{c.clearTimeout(u),t.removeEventListener("animationstart",f),t.removeEventListener("animationcancel",d),t.removeEventListener("animationend",d)}}else l("ANIMATION_END")},[t,l]),{isPresent:["mounted","unmountSuspended"].includes(a),ref:x.useCallback(u=>{u&&(r.current=getComputedStyle(u)),n(u)},[])}}function Va(e){return(e==null?void 0:e.animationName)||"none"}function Zk(e){var r,s;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(s=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:s.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}function Kk({prop:e,defaultProp:t,onChange:n=()=>{}}){const[r,s]=qk({defaultProp:t,onChange:n}),i=e!==void 0,o=i?e:r,a=kn(n),l=x.useCallback(u=>{if(i){const d=typeof u=="function"?u(e):u;d!==e&&a(d)}else s(u)},[i,e,s,a]);return[o,l]}function qk({defaultProp:e,onChange:t}){const n=x.useState(e),[r]=n,s=x.useRef(r),i=kn(t);return x.useEffect(()=>{s.current!==r&&(i(r),s.current=r)},[r,s,i]),n}var Gk="VisuallyHidden",Au=x.forwardRef((e,t)=>S.jsx(gt.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));Au.displayName=Gk;var Qk=Au,wh="ToastProvider",[Sh,Yk,Xk]=Mk("Toast"),[Ex,GD]=Pu("Toast",[Xk]),[Jk,Ru]=Ex(wh),Px=e=>{const{__scopeToast:t,label:n="Notification",duration:r=5e3,swipeDirection:s="right",swipeThreshold:i=50,children:o}=e,[a,l]=x.useState(null),[u,c]=x.useState(0),d=x.useRef(!1),f=x.useRef(!1);return n.trim()||console.error(`Invalid prop \`label\` supplied to \`${wh}\`. Expected non-empty \`string\`.`),S.jsx(Sh.Provider,{scope:t,children:S.jsx(Jk,{scope:t,label:n,duration:r,swipeDirection:s,swipeThreshold:i,toastCount:u,viewport:a,onViewportChange:l,onToastAdd:x.useCallback(()=>c(g=>g+1),[]),onToastRemove:x.useCallback(()=>c(g=>g-1),[]),isFocusedToastEscapeKeyDownRef:d,isClosePausedRef:f,children:o})})};Px.displayName=wh;var Ax="ToastViewport",eb=["F8"],Bd="toast.viewportPause",$d="toast.viewportResume",Rx=x.forwardRef((e,t)=>{const{__scopeToast:n,hotkey:r=eb,label:s="Notifications ({hotkey})",...i}=e,o=Ru(Ax,n),a=Yk(n),l=x.useRef(null),u=x.useRef(null),c=x.useRef(null),d=x.useRef(null),f=an(t,d,o.onViewportChange),g=r.join("+").replace(/Key/g,"").replace(/Digit/g,""),v=o.toastCount>0;x.useEffect(()=>{const w=p=>{var m;r.length!==0&&r.every(_=>p[_]||p.code===_)&&((m=d.current)==null||m.focus())};return document.addEventListener("keydown",w),()=>document.removeEventListener("keydown",w)},[r]),x.useEffect(()=>{const w=l.current,p=d.current;if(v&&w&&p){const h=()=>{if(!o.isClosePausedRef.current){const k=new CustomEvent(Bd);p.dispatchEvent(k),o.isClosePausedRef.current=!0}},m=()=>{if(o.isClosePausedRef.current){const k=new CustomEvent($d);p.dispatchEvent(k),o.isClosePausedRef.current=!1}},_=k=>{!w.contains(k.relatedTarget)&&m()},C=()=>{w.contains(document.activeElement)||m()};return w.addEventListener("focusin",h),w.addEventListener("focusout",_),w.addEventListener("pointermove",h),w.addEventListener("pointerleave",C),window.addEventListener("blur",h),window.addEventListener("focus",m),()=>{w.removeEventListener("focusin",h),w.removeEventListener("focusout",_),w.removeEventListener("pointermove",h),w.removeEventListener("pointerleave",C),window.removeEventListener("blur",h),window.removeEventListener("focus",m)}}},[v,o.isClosePausedRef]);const y=x.useCallback(({tabbingDirection:w})=>{const h=a().map(m=>{const _=m.ref.current,C=[_,...hb(_)];return w==="forwards"?C:C.reverse()});return(w==="forwards"?h.reverse():h).flat()},[a]);return x.useEffect(()=>{const w=d.current;if(w){const p=h=>{var C,k,P;const m=h.altKey||h.ctrlKey||h.metaKey;if(h.key==="Tab"&&!m){const b=document.activeElement,F=h.shiftKey;if(h.target===w&&F){(C=u.current)==null||C.focus();return}const L=y({tabbingDirection:F?"backwards":"forwards"}),U=L.findIndex(O=>O===b);Ec(L.slice(U+1))?h.preventDefault():F?(k=u.current)==null||k.focus():(P=c.current)==null||P.focus()}};return w.addEventListener("keydown",p),()=>w.removeEventListener("keydown",p)}},[a,y]),S.jsxs($k,{ref:l,role:"region","aria-label":s.replace("{hotkey}",g),tabIndex:-1,style:{pointerEvents:v?void 0:"none"},children:[v&&S.jsx(Ud,{ref:u,onFocusFromOutsideViewport:()=>{const w=y({tabbingDirection:"forwards"});Ec(w)}}),S.jsx(Sh.Slot,{scope:n,children:S.jsx(gt.ol,{tabIndex:-1,...i,ref:f})}),v&&S.jsx(Ud,{ref:c,onFocusFromOutsideViewport:()=>{const w=y({tabbingDirection:"backwards"});Ec(w)}})]})});Rx.displayName=Ax;var Nx="ToastFocusProxy",Ud=x.forwardRef((e,t)=>{const{__scopeToast:n,onFocusFromOutsideViewport:r,...s}=e,i=Ru(Nx,n);return S.jsx(Au,{"aria-hidden":!0,tabIndex:0,...s,ref:t,style:{position:"fixed"},onFocus:o=>{var u;const a=o.relatedTarget;!((u=i.viewport)!=null&&u.contains(a))&&r()}})});Ud.displayName=Nx;var Nu="Toast",tb="toast.swipeStart",nb="toast.swipeMove",rb="toast.swipeCancel",sb="toast.swipeEnd",Mx=x.forwardRef((e,t)=>{const{forceMount:n,open:r,defaultOpen:s,onOpenChange:i,...o}=e,[a=!0,l]=Kk({prop:r,defaultProp:s,onChange:i});return S.jsx(xh,{present:n||a,children:S.jsx(ab,{open:a,...o,ref:t,onClose:()=>l(!1),onPause:kn(e.onPause),onResume:kn(e.onResume),onSwipeStart:Be(e.onSwipeStart,u=>{u.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:Be(e.onSwipeMove,u=>{const{x:c,y:d}=u.detail.delta;u.currentTarget.setAttribute("data-swipe","move"),u.currentTarget.style.setProperty("--radix-toast-swipe-move-x",`${c}px`),u.currentTarget.style.setProperty("--radix-toast-swipe-move-y",`${d}px`)}),onSwipeCancel:Be(e.onSwipeCancel,u=>{u.currentTarget.setAttribute("data-swipe","cancel"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),u.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),u.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:Be(e.onSwipeEnd,u=>{const{x:c,y:d}=u.detail.delta;u.currentTarget.setAttribute("data-swipe","end"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),u.currentTarget.style.setProperty("--radix-toast-swipe-end-x",`${c}px`),u.currentTarget.style.setProperty("--radix-toast-swipe-end-y",`${d}px`),l(!1)})})})});Mx.displayName=Nu;var[ib,ob]=Ex(Nu,{onClose(){}}),ab=x.forwardRef((e,t)=>{const{__scopeToast:n,type:r="foreground",duration:s,open:i,onClose:o,onEscapeKeyDown:a,onPause:l,onResume:u,onSwipeStart:c,onSwipeMove:d,onSwipeCancel:f,onSwipeEnd:g,...v}=e,y=Ru(Nu,n),[w,p]=x.useState(null),h=an(t,O=>p(O)),m=x.useRef(null),_=x.useRef(null),C=s||y.duration,k=x.useRef(0),P=x.useRef(C),b=x.useRef(0),{onToastAdd:F,onToastRemove:j}=y,Y=kn(()=>{var ne;(w==null?void 0:w.contains(document.activeElement))&&((ne=y.viewport)==null||ne.focus()),o()}),L=x.useCallback(O=>{!O||O===1/0||(window.clearTimeout(b.current),k.current=new Date().getTime(),b.current=window.setTimeout(Y,O))},[Y]);x.useEffect(()=>{const O=y.viewport;if(O){const ne=()=>{L(P.current),u==null||u()},X=()=>{const Q=new Date().getTime()-k.current;P.current=P.current-Q,window.clearTimeout(b.current),l==null||l()};return O.addEventListener(Bd,X),O.addEventListener($d,ne),()=>{O.removeEventListener(Bd,X),O.removeEventListener($d,ne)}}},[y.viewport,C,l,u,L]),x.useEffect(()=>{i&&!y.isClosePausedRef.current&&L(C)},[i,C,y.isClosePausedRef,L]),x.useEffect(()=>(F(),()=>j()),[F,j]);const U=x.useMemo(()=>w?Ix(w):null,[w]);return y.viewport?S.jsxs(S.Fragment,{children:[U&&S.jsx(lb,{__scopeToast:n,role:"status","aria-live":r==="foreground"?"assertive":"polite","aria-atomic":!0,children:U}),S.jsx(ib,{scope:n,onClose:Y,children:aa.createPortal(S.jsx(Sh.ItemSlot,{scope:n,children:S.jsx(Bk,{asChild:!0,onEscapeKeyDown:Be(a,()=>{y.isFocusedToastEscapeKeyDownRef.current||Y(),y.isFocusedToastEscapeKeyDownRef.current=!1}),children:S.jsx(gt.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":i?"open":"closed","data-swipe-direction":y.swipeDirection,...v,ref:h,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:Be(e.onKeyDown,O=>{O.key==="Escape"&&(a==null||a(O.nativeEvent),O.nativeEvent.defaultPrevented||(y.isFocusedToastEscapeKeyDownRef.current=!0,Y()))}),onPointerDown:Be(e.onPointerDown,O=>{O.button===0&&(m.current={x:O.clientX,y:O.clientY})}),onPointerMove:Be(e.onPointerMove,O=>{if(!m.current)return;const ne=O.clientX-m.current.x,X=O.clientY-m.current.y,Q=!!_.current,A=["left","right"].includes(y.swipeDirection),I=["left","up"].includes(y.swipeDirection)?Math.min:Math.max,K=A?I(0,ne):0,re=A?0:I(0,X),ye=O.pointerType==="touch"?10:2,yt={x:K,y:re},vt={originalEvent:O,delta:yt};Q?(_.current=yt,Fa(nb,d,vt,{discrete:!1})):Zm(yt,y.swipeDirection,ye)?(_.current=yt,Fa(tb,c,vt,{discrete:!1}),O.target.setPointerCapture(O.pointerId)):(Math.abs(ne)>ye||Math.abs(X)>ye)&&(m.current=null)}),onPointerUp:Be(e.onPointerUp,O=>{const ne=_.current,X=O.target;if(X.hasPointerCapture(O.pointerId)&&X.releasePointerCapture(O.pointerId),_.current=null,m.current=null,ne){const Q=O.currentTarget,A={originalEvent:O,delta:ne};Zm(ne,y.swipeDirection,y.swipeThreshold)?Fa(sb,g,A,{discrete:!0}):Fa(rb,f,A,{discrete:!0}),Q.addEventListener("click",I=>I.preventDefault(),{once:!0})}})})})}),y.viewport)})]}):null}),lb=e=>{const{__scopeToast:t,children:n,...r}=e,s=Ru(Nu,t),[i,o]=x.useState(!1),[a,l]=x.useState(!1);return db(()=>o(!0)),x.useEffect(()=>{const u=window.setTimeout(()=>l(!0),1e3);return()=>window.clearTimeout(u)},[]),a?null:S.jsx(bx,{asChild:!0,children:S.jsx(Au,{...r,children:i&&S.jsxs(S.Fragment,{children:[s.label," ",n]})})})},ub="ToastTitle",Dx=x.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e;return S.jsx(gt.div,{...r,ref:t})});Dx.displayName=ub;var cb="ToastDescription",Ox=x.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e;return S.jsx(gt.div,{...r,ref:t})});Ox.displayName=cb;var Lx="ToastAction",jx=x.forwardRef((e,t)=>{const{altText:n,...r}=e;return n.trim()?S.jsx(Fx,{altText:n,asChild:!0,children:S.jsx(_h,{...r,ref:t})}):(console.error(`Invalid prop \`altText\` supplied to \`${Lx}\`. Expected non-empty \`string\`.`),null)});jx.displayName=Lx;var Vx="ToastClose",_h=x.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e,s=ob(Vx,n);return S.jsx(Fx,{asChild:!0,children:S.jsx(gt.button,{type:"button",...r,ref:t,onClick:Be(e.onClick,s.onClose)})})});_h.displayName=Vx;var Fx=x.forwardRef((e,t)=>{const{__scopeToast:n,altText:r,...s}=e;return S.jsx(gt.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":r||void 0,...s,ref:t})});function Ix(e){const t=[];return Array.from(e.childNodes).forEach(r=>{if(r.nodeType===r.TEXT_NODE&&r.textContent&&t.push(r.textContent),fb(r)){const s=r.ariaHidden||r.hidden||r.style.display==="none",i=r.dataset.radixToastAnnounceExclude==="";if(!s)if(i){const o=r.dataset.radixToastAnnounceAlt;o&&t.push(o)}else t.push(...Ix(r))}}),t}function Fa(e,t,n,{discrete:r}){const s=n.originalEvent.currentTarget,i=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:n});t&&s.addEventListener(e,t,{once:!0}),r?_x(s,i):s.dispatchEvent(i)}var Zm=(e,t,n=0)=>{const r=Math.abs(e.x),s=Math.abs(e.y),i=r>s;return t==="left"||t==="right"?i&&r>n:!i&&s>n};function db(e=()=>{}){const t=kn(e);ds(()=>{let n=0,r=0;return n=window.requestAnimationFrame(()=>r=window.requestAnimationFrame(t)),()=>{window.cancelAnimationFrame(n),window.cancelAnimationFrame(r)}},[t])}function fb(e){return e.nodeType===e.ELEMENT_NODE}function hb(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const s=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||s?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function Ec(e){const t=document.activeElement;return e.some(n=>n===t?!0:(n.focus(),document.activeElement!==t))}var pb=Px,zx=Rx,Bx=Mx,$x=Dx,Ux=Ox,Wx=jx,Hx=_h;function Zx(e){var t,n,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e)){var s=e.length;for(t=0;t<s;t++)e[t]&&(n=Zx(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}function Kx(){for(var e,t,n=0,r="",s=arguments.length;n<s;n++)(e=arguments[n])&&(t=Zx(e))&&(r&&(r+=" "),r+=t);return r}const Km=e=>typeof e=="boolean"?`${e}`:e===0?"0":e,qm=Kx,Th=(e,t)=>n=>{var r;if((t==null?void 0:t.variants)==null)return qm(e,n==null?void 0:n.class,n==null?void 0:n.className);const{variants:s,defaultVariants:i}=t,o=Object.keys(s).map(u=>{const c=n==null?void 0:n[u],d=i==null?void 0:i[u];if(c===null)return null;const f=Km(c)||Km(d);return s[u][f]}),a=n&&Object.entries(n).reduce((u,c)=>{let[d,f]=c;return f===void 0||(u[d]=f),u},{}),l=t==null||(r=t.compoundVariants)===null||r===void 0?void 0:r.reduce((u,c)=>{let{class:d,className:f,...g}=c;return Object.entries(g).every(v=>{let[y,w]=v;return Array.isArray(w)?w.includes({...i,...a}[y]):{...i,...a}[y]===w})?[...u,d,f]:u},[]);return qm(e,o,l,n==null?void 0:n.class,n==null?void 0:n.className)};/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mb=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),qx=(...e)=>e.filter((t,n,r)=>!!t&&r.indexOf(t)===n).join(" ");/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var gb={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yb=x.forwardRef(({color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:r,className:s="",children:i,iconNode:o,...a},l)=>x.createElement("svg",{ref:l,...gb,width:t,height:t,stroke:e,strokeWidth:r?Number(n)*24/Number(t):n,className:qx("lucide",s),...a},[...o.map(([u,c])=>x.createElement(u,c)),...Array.isArray(i)?i:[i]]));/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Gx=(e,t)=>{const n=x.forwardRef(({className:r,...s},i)=>x.createElement(yb,{ref:i,iconNode:t,className:qx(`lucide-${mb(e)}`,r),...s}));return n.displayName=`${e}`,n};/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vb=Gx("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xb=Gx("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),Ch="-",wb=e=>{const t=_b(e),{conflictingClassGroups:n,conflictingClassGroupModifiers:r}=e;return{getClassGroupId:o=>{const a=o.split(Ch);return a[0]===""&&a.length!==1&&a.shift(),Qx(a,t)||Sb(o)},getConflictingClassGroupIds:(o,a)=>{const l=n[o]||[];return a&&r[o]?[...l,...r[o]]:l}}},Qx=(e,t)=>{var o;if(e.length===0)return t.classGroupId;const n=e[0],r=t.nextPart.get(n),s=r?Qx(e.slice(1),r):void 0;if(s)return s;if(t.validators.length===0)return;const i=e.join(Ch);return(o=t.validators.find(({validator:a})=>a(i)))==null?void 0:o.classGroupId},Gm=/^\[(.+)\]$/,Sb=e=>{if(Gm.test(e)){const t=Gm.exec(e)[1],n=t==null?void 0:t.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}},_b=e=>{const{theme:t,prefix:n}=e,r={nextPart:new Map,validators:[]};return Cb(Object.entries(e.classGroups),n).forEach(([i,o])=>{Wd(o,r,i,t)}),r},Wd=(e,t,n,r)=>{e.forEach(s=>{if(typeof s=="string"){const i=s===""?t:Qm(t,s);i.classGroupId=n;return}if(typeof s=="function"){if(Tb(s)){Wd(s(r),t,n,r);return}t.validators.push({validator:s,classGroupId:n});return}Object.entries(s).forEach(([i,o])=>{Wd(o,Qm(t,i),n,r)})})},Qm=(e,t)=>{let n=e;return t.split(Ch).forEach(r=>{n.nextPart.has(r)||n.nextPart.set(r,{nextPart:new Map,validators:[]}),n=n.nextPart.get(r)}),n},Tb=e=>e.isThemeGetter,Cb=(e,t)=>t?e.map(([n,r])=>{const s=r.map(i=>typeof i=="string"?t+i:typeof i=="object"?Object.fromEntries(Object.entries(i).map(([o,a])=>[t+o,a])):i);return[n,s]}):e,kb=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,n=new Map,r=new Map;const s=(i,o)=>{n.set(i,o),t++,t>e&&(t=0,r=n,n=new Map)};return{get(i){let o=n.get(i);if(o!==void 0)return o;if((o=r.get(i))!==void 0)return s(i,o),o},set(i,o){n.has(i)?n.set(i,o):s(i,o)}}},Yx="!",bb=e=>{const{separator:t,experimentalParseClassName:n}=e,r=t.length===1,s=t[0],i=t.length,o=a=>{const l=[];let u=0,c=0,d;for(let w=0;w<a.length;w++){let p=a[w];if(u===0){if(p===s&&(r||a.slice(w,w+i)===t)){l.push(a.slice(c,w)),c=w+i;continue}if(p==="/"){d=w;continue}}p==="["?u++:p==="]"&&u--}const f=l.length===0?a:a.substring(c),g=f.startsWith(Yx),v=g?f.substring(1):f,y=d&&d>c?d-c:void 0;return{modifiers:l,hasImportantModifier:g,baseClassName:v,maybePostfixModifierPosition:y}};return n?a=>n({className:a,parseClassName:o}):o},Eb=e=>{if(e.length<=1)return e;const t=[];let n=[];return e.forEach(r=>{r[0]==="["?(t.push(...n.sort(),r),n=[]):n.push(r)}),t.push(...n.sort()),t},Pb=e=>({cache:kb(e.cacheSize),parseClassName:bb(e),...wb(e)}),Ab=/\s+/,Rb=(e,t)=>{const{parseClassName:n,getClassGroupId:r,getConflictingClassGroupIds:s}=t,i=[],o=e.trim().split(Ab);let a="";for(let l=o.length-1;l>=0;l-=1){const u=o[l],{modifiers:c,hasImportantModifier:d,baseClassName:f,maybePostfixModifierPosition:g}=n(u);let v=!!g,y=r(v?f.substring(0,g):f);if(!y){if(!v){a=u+(a.length>0?" "+a:a);continue}if(y=r(f),!y){a=u+(a.length>0?" "+a:a);continue}v=!1}const w=Eb(c).join(":"),p=d?w+Yx:w,h=p+y;if(i.includes(h))continue;i.push(h);const m=s(y,v);for(let _=0;_<m.length;++_){const C=m[_];i.push(p+C)}a=u+(a.length>0?" "+a:a)}return a};function Nb(){let e=0,t,n,r="";for(;e<arguments.length;)(t=arguments[e++])&&(n=Xx(t))&&(r&&(r+=" "),r+=n);return r}const Xx=e=>{if(typeof e=="string")return e;let t,n="";for(let r=0;r<e.length;r++)e[r]&&(t=Xx(e[r]))&&(n&&(n+=" "),n+=t);return n};function Mb(e,...t){let n,r,s,i=o;function o(l){const u=t.reduce((c,d)=>d(c),e());return n=Pb(u),r=n.cache.get,s=n.cache.set,i=a,a(l)}function a(l){const u=r(l);if(u)return u;const c=Rb(l,n);return s(l,c),c}return function(){return i(Nb.apply(null,arguments))}}const we=e=>{const t=n=>n[e]||[];return t.isThemeGetter=!0,t},Jx=/^\[(?:([a-z-]+):)?(.+)\]$/i,Db=/^\d+\/\d+$/,Ob=new Set(["px","full","screen"]),Lb=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,jb=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,Vb=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,Fb=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,Ib=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Rn=e=>Gs(e)||Ob.has(e)||Db.test(e),er=e=>bi(e,"length",Kb),Gs=e=>!!e&&!Number.isNaN(Number(e)),Pc=e=>bi(e,"number",Gs),Bi=e=>!!e&&Number.isInteger(Number(e)),zb=e=>e.endsWith("%")&&Gs(e.slice(0,-1)),oe=e=>Jx.test(e),tr=e=>Lb.test(e),Bb=new Set(["length","size","percentage"]),$b=e=>bi(e,Bb,ew),Ub=e=>bi(e,"position",ew),Wb=new Set(["image","url"]),Hb=e=>bi(e,Wb,Gb),Zb=e=>bi(e,"",qb),$i=()=>!0,bi=(e,t,n)=>{const r=Jx.exec(e);return r?r[1]?typeof t=="string"?r[1]===t:t.has(r[1]):n(r[2]):!1},Kb=e=>jb.test(e)&&!Vb.test(e),ew=()=>!1,qb=e=>Fb.test(e),Gb=e=>Ib.test(e),Qb=()=>{const e=we("colors"),t=we("spacing"),n=we("blur"),r=we("brightness"),s=we("borderColor"),i=we("borderRadius"),o=we("borderSpacing"),a=we("borderWidth"),l=we("contrast"),u=we("grayscale"),c=we("hueRotate"),d=we("invert"),f=we("gap"),g=we("gradientColorStops"),v=we("gradientColorStopPositions"),y=we("inset"),w=we("margin"),p=we("opacity"),h=we("padding"),m=we("saturate"),_=we("scale"),C=we("sepia"),k=we("skew"),P=we("space"),b=we("translate"),F=()=>["auto","contain","none"],j=()=>["auto","hidden","clip","visible","scroll"],Y=()=>["auto",oe,t],L=()=>[oe,t],U=()=>["",Rn,er],O=()=>["auto",Gs,oe],ne=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],X=()=>["solid","dashed","dotted","double","none"],Q=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],A=()=>["start","end","center","between","around","evenly","stretch"],I=()=>["","0",oe],K=()=>["auto","avoid","all","avoid-page","page","left","right","column"],re=()=>[Gs,oe];return{cacheSize:500,separator:":",theme:{colors:[$i],spacing:[Rn,er],blur:["none","",tr,oe],brightness:re(),borderColor:[e],borderRadius:["none","","full",tr,oe],borderSpacing:L(),borderWidth:U(),contrast:re(),grayscale:I(),hueRotate:re(),invert:I(),gap:L(),gradientColorStops:[e],gradientColorStopPositions:[zb,er],inset:Y(),margin:Y(),opacity:re(),padding:L(),saturate:re(),scale:re(),sepia:I(),skew:re(),space:L(),translate:L()},classGroups:{aspect:[{aspect:["auto","square","video",oe]}],container:["container"],columns:[{columns:[tr]}],"break-after":[{"break-after":K()}],"break-before":[{"break-before":K()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...ne(),oe]}],overflow:[{overflow:j()}],"overflow-x":[{"overflow-x":j()}],"overflow-y":[{"overflow-y":j()}],overscroll:[{overscroll:F()}],"overscroll-x":[{"overscroll-x":F()}],"overscroll-y":[{"overscroll-y":F()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[y]}],"inset-x":[{"inset-x":[y]}],"inset-y":[{"inset-y":[y]}],start:[{start:[y]}],end:[{end:[y]}],top:[{top:[y]}],right:[{right:[y]}],bottom:[{bottom:[y]}],left:[{left:[y]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",Bi,oe]}],basis:[{basis:Y()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",oe]}],grow:[{grow:I()}],shrink:[{shrink:I()}],order:[{order:["first","last","none",Bi,oe]}],"grid-cols":[{"grid-cols":[$i]}],"col-start-end":[{col:["auto",{span:["full",Bi,oe]},oe]}],"col-start":[{"col-start":O()}],"col-end":[{"col-end":O()}],"grid-rows":[{"grid-rows":[$i]}],"row-start-end":[{row:["auto",{span:[Bi,oe]},oe]}],"row-start":[{"row-start":O()}],"row-end":[{"row-end":O()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",oe]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",oe]}],gap:[{gap:[f]}],"gap-x":[{"gap-x":[f]}],"gap-y":[{"gap-y":[f]}],"justify-content":[{justify:["normal",...A()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...A(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...A(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[h]}],px:[{px:[h]}],py:[{py:[h]}],ps:[{ps:[h]}],pe:[{pe:[h]}],pt:[{pt:[h]}],pr:[{pr:[h]}],pb:[{pb:[h]}],pl:[{pl:[h]}],m:[{m:[w]}],mx:[{mx:[w]}],my:[{my:[w]}],ms:[{ms:[w]}],me:[{me:[w]}],mt:[{mt:[w]}],mr:[{mr:[w]}],mb:[{mb:[w]}],ml:[{ml:[w]}],"space-x":[{"space-x":[P]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[P]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",oe,t]}],"min-w":[{"min-w":[oe,t,"min","max","fit"]}],"max-w":[{"max-w":[oe,t,"none","full","min","max","fit","prose",{screen:[tr]},tr]}],h:[{h:[oe,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[oe,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[oe,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[oe,t,"auto","min","max","fit"]}],"font-size":[{text:["base",tr,er]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",Pc]}],"font-family":[{font:[$i]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",oe]}],"line-clamp":[{"line-clamp":["none",Gs,Pc]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",Rn,oe]}],"list-image":[{"list-image":["none",oe]}],"list-style-type":[{list:["none","disc","decimal",oe]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[p]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[p]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...X(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",Rn,er]}],"underline-offset":[{"underline-offset":["auto",Rn,oe]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:L()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",oe]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",oe]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[p]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...ne(),Ub]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",$b]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},Hb]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[v]}],"gradient-via-pos":[{via:[v]}],"gradient-to-pos":[{to:[v]}],"gradient-from":[{from:[g]}],"gradient-via":[{via:[g]}],"gradient-to":[{to:[g]}],rounded:[{rounded:[i]}],"rounded-s":[{"rounded-s":[i]}],"rounded-e":[{"rounded-e":[i]}],"rounded-t":[{"rounded-t":[i]}],"rounded-r":[{"rounded-r":[i]}],"rounded-b":[{"rounded-b":[i]}],"rounded-l":[{"rounded-l":[i]}],"rounded-ss":[{"rounded-ss":[i]}],"rounded-se":[{"rounded-se":[i]}],"rounded-ee":[{"rounded-ee":[i]}],"rounded-es":[{"rounded-es":[i]}],"rounded-tl":[{"rounded-tl":[i]}],"rounded-tr":[{"rounded-tr":[i]}],"rounded-br":[{"rounded-br":[i]}],"rounded-bl":[{"rounded-bl":[i]}],"border-w":[{border:[a]}],"border-w-x":[{"border-x":[a]}],"border-w-y":[{"border-y":[a]}],"border-w-s":[{"border-s":[a]}],"border-w-e":[{"border-e":[a]}],"border-w-t":[{"border-t":[a]}],"border-w-r":[{"border-r":[a]}],"border-w-b":[{"border-b":[a]}],"border-w-l":[{"border-l":[a]}],"border-opacity":[{"border-opacity":[p]}],"border-style":[{border:[...X(),"hidden"]}],"divide-x":[{"divide-x":[a]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[a]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[p]}],"divide-style":[{divide:X()}],"border-color":[{border:[s]}],"border-color-x":[{"border-x":[s]}],"border-color-y":[{"border-y":[s]}],"border-color-s":[{"border-s":[s]}],"border-color-e":[{"border-e":[s]}],"border-color-t":[{"border-t":[s]}],"border-color-r":[{"border-r":[s]}],"border-color-b":[{"border-b":[s]}],"border-color-l":[{"border-l":[s]}],"divide-color":[{divide:[s]}],"outline-style":[{outline:["",...X()]}],"outline-offset":[{"outline-offset":[Rn,oe]}],"outline-w":[{outline:[Rn,er]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:U()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[p]}],"ring-offset-w":[{"ring-offset":[Rn,er]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",tr,Zb]}],"shadow-color":[{shadow:[$i]}],opacity:[{opacity:[p]}],"mix-blend":[{"mix-blend":[...Q(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":Q()}],filter:[{filter:["","none"]}],blur:[{blur:[n]}],brightness:[{brightness:[r]}],contrast:[{contrast:[l]}],"drop-shadow":[{"drop-shadow":["","none",tr,oe]}],grayscale:[{grayscale:[u]}],"hue-rotate":[{"hue-rotate":[c]}],invert:[{invert:[d]}],saturate:[{saturate:[m]}],sepia:[{sepia:[C]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[n]}],"backdrop-brightness":[{"backdrop-brightness":[r]}],"backdrop-contrast":[{"backdrop-contrast":[l]}],"backdrop-grayscale":[{"backdrop-grayscale":[u]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[c]}],"backdrop-invert":[{"backdrop-invert":[d]}],"backdrop-opacity":[{"backdrop-opacity":[p]}],"backdrop-saturate":[{"backdrop-saturate":[m]}],"backdrop-sepia":[{"backdrop-sepia":[C]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[o]}],"border-spacing-x":[{"border-spacing-x":[o]}],"border-spacing-y":[{"border-spacing-y":[o]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",oe]}],duration:[{duration:re()}],ease:[{ease:["linear","in","out","in-out",oe]}],delay:[{delay:re()}],animate:[{animate:["none","spin","ping","pulse","bounce",oe]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[_]}],"scale-x":[{"scale-x":[_]}],"scale-y":[{"scale-y":[_]}],rotate:[{rotate:[Bi,oe]}],"translate-x":[{"translate-x":[b]}],"translate-y":[{"translate-y":[b]}],"skew-x":[{"skew-x":[k]}],"skew-y":[{"skew-y":[k]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",oe]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",oe]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":L()}],"scroll-mx":[{"scroll-mx":L()}],"scroll-my":[{"scroll-my":L()}],"scroll-ms":[{"scroll-ms":L()}],"scroll-me":[{"scroll-me":L()}],"scroll-mt":[{"scroll-mt":L()}],"scroll-mr":[{"scroll-mr":L()}],"scroll-mb":[{"scroll-mb":L()}],"scroll-ml":[{"scroll-ml":L()}],"scroll-p":[{"scroll-p":L()}],"scroll-px":[{"scroll-px":L()}],"scroll-py":[{"scroll-py":L()}],"scroll-ps":[{"scroll-ps":L()}],"scroll-pe":[{"scroll-pe":L()}],"scroll-pt":[{"scroll-pt":L()}],"scroll-pr":[{"scroll-pr":L()}],"scroll-pb":[{"scroll-pb":L()}],"scroll-pl":[{"scroll-pl":L()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",oe]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[Rn,er,Pc]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}},Yb=Mb(Qb);function je(...e){return Yb(Kx(e))}const Xb=pb,tw=x.forwardRef(({className:e,...t},n)=>S.jsx(zx,{ref:n,className:je("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",e),...t}));tw.displayName=zx.displayName;const Jb=Th("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),nw=x.forwardRef(({className:e,variant:t,...n},r)=>S.jsx(Bx,{ref:r,className:je(Jb({variant:t}),e),...n}));nw.displayName=Bx.displayName;const eE=x.forwardRef(({className:e,...t},n)=>S.jsx(Wx,{ref:n,className:je("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",e),...t}));eE.displayName=Wx.displayName;const rw=x.forwardRef(({className:e,...t},n)=>S.jsx(Hx,{ref:n,className:je("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",e),"toast-close":"",...t,children:S.jsx(xb,{className:"h-4 w-4"})}));rw.displayName=Hx.displayName;const sw=x.forwardRef(({className:e,...t},n)=>S.jsx($x,{ref:n,className:je("text-sm font-semibold",e),...t}));sw.displayName=$x.displayName;const iw=x.forwardRef(({className:e,...t},n)=>S.jsx(Ux,{ref:n,className:je("text-sm opacity-90",e),...t}));iw.displayName=Ux.displayName;function tE(){const{toasts:e}=vx();return S.jsxs(Xb,{children:[e.map(function({id:t,title:n,description:r,action:s,...i}){return S.jsxs(nw,{...i,children:[S.jsxs("div",{className:"grid gap-1",children:[n&&S.jsx(sw,{children:n}),r&&S.jsx(iw,{children:r})]}),s,S.jsx(rw,{})]},t)}),S.jsx(tw,{})]})}const nE=["top","right","bottom","left"],Pr=Math.min,Et=Math.max,Il=Math.round,Ia=Math.floor,Sn=e=>({x:e,y:e}),rE={left:"right",right:"left",bottom:"top",top:"bottom"},sE={start:"end",end:"start"};function Hd(e,t,n){return Et(e,Pr(t,n))}function Kn(e,t){return typeof e=="function"?e(t):e}function qn(e){return e.split("-")[0]}function Ei(e){return e.split("-")[1]}function kh(e){return e==="x"?"y":"x"}function bh(e){return e==="y"?"height":"width"}function Ar(e){return["top","bottom"].includes(qn(e))?"y":"x"}function Eh(e){return kh(Ar(e))}function iE(e,t,n){n===void 0&&(n=!1);const r=Ei(e),s=Eh(e),i=bh(s);let o=s==="x"?r===(n?"end":"start")?"right":"left":r==="start"?"bottom":"top";return t.reference[i]>t.floating[i]&&(o=zl(o)),[o,zl(o)]}function oE(e){const t=zl(e);return[Zd(e),t,Zd(t)]}function Zd(e){return e.replace(/start|end/g,t=>sE[t])}function aE(e,t,n){const r=["left","right"],s=["right","left"],i=["top","bottom"],o=["bottom","top"];switch(e){case"top":case"bottom":return n?t?s:r:t?r:s;case"left":case"right":return t?i:o;default:return[]}}function lE(e,t,n,r){const s=Ei(e);let i=aE(qn(e),n==="start",r);return s&&(i=i.map(o=>o+"-"+s),t&&(i=i.concat(i.map(Zd)))),i}function zl(e){return e.replace(/left|right|bottom|top/g,t=>rE[t])}function uE(e){return{top:0,right:0,bottom:0,left:0,...e}}function ow(e){return typeof e!="number"?uE(e):{top:e,right:e,bottom:e,left:e}}function Bl(e){const{x:t,y:n,width:r,height:s}=e;return{width:r,height:s,top:n,left:t,right:t+r,bottom:n+s,x:t,y:n}}function Ym(e,t,n){let{reference:r,floating:s}=e;const i=Ar(t),o=Eh(t),a=bh(o),l=qn(t),u=i==="y",c=r.x+r.width/2-s.width/2,d=r.y+r.height/2-s.height/2,f=r[a]/2-s[a]/2;let g;switch(l){case"top":g={x:c,y:r.y-s.height};break;case"bottom":g={x:c,y:r.y+r.height};break;case"right":g={x:r.x+r.width,y:d};break;case"left":g={x:r.x-s.width,y:d};break;default:g={x:r.x,y:r.y}}switch(Ei(t)){case"start":g[o]-=f*(n&&u?-1:1);break;case"end":g[o]+=f*(n&&u?-1:1);break}return g}const cE=async(e,t,n)=>{const{placement:r="bottom",strategy:s="absolute",middleware:i=[],platform:o}=n,a=i.filter(Boolean),l=await(o.isRTL==null?void 0:o.isRTL(t));let u=await o.getElementRects({reference:e,floating:t,strategy:s}),{x:c,y:d}=Ym(u,r,l),f=r,g={},v=0;for(let y=0;y<a.length;y++){const{name:w,fn:p}=a[y],{x:h,y:m,data:_,reset:C}=await p({x:c,y:d,initialPlacement:r,placement:f,strategy:s,middlewareData:g,rects:u,platform:o,elements:{reference:e,floating:t}});c=h??c,d=m??d,g={...g,[w]:{...g[w],..._}},C&&v<=50&&(v++,typeof C=="object"&&(C.placement&&(f=C.placement),C.rects&&(u=C.rects===!0?await o.getElementRects({reference:e,floating:t,strategy:s}):C.rects),{x:c,y:d}=Ym(u,f,l)),y=-1)}return{x:c,y:d,placement:f,strategy:s,middlewareData:g}};async function Lo(e,t){var n;t===void 0&&(t={});const{x:r,y:s,platform:i,rects:o,elements:a,strategy:l}=e,{boundary:u="clippingAncestors",rootBoundary:c="viewport",elementContext:d="floating",altBoundary:f=!1,padding:g=0}=Kn(t,e),v=ow(g),w=a[f?d==="floating"?"reference":"floating":d],p=Bl(await i.getClippingRect({element:(n=await(i.isElement==null?void 0:i.isElement(w)))==null||n?w:w.contextElement||await(i.getDocumentElement==null?void 0:i.getDocumentElement(a.floating)),boundary:u,rootBoundary:c,strategy:l})),h=d==="floating"?{x:r,y:s,width:o.floating.width,height:o.floating.height}:o.reference,m=await(i.getOffsetParent==null?void 0:i.getOffsetParent(a.floating)),_=await(i.isElement==null?void 0:i.isElement(m))?await(i.getScale==null?void 0:i.getScale(m))||{x:1,y:1}:{x:1,y:1},C=Bl(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:h,offsetParent:m,strategy:l}):h);return{top:(p.top-C.top+v.top)/_.y,bottom:(C.bottom-p.bottom+v.bottom)/_.y,left:(p.left-C.left+v.left)/_.x,right:(C.right-p.right+v.right)/_.x}}const dE=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:r,placement:s,rects:i,platform:o,elements:a,middlewareData:l}=t,{element:u,padding:c=0}=Kn(e,t)||{};if(u==null)return{};const d=ow(c),f={x:n,y:r},g=Eh(s),v=bh(g),y=await o.getDimensions(u),w=g==="y",p=w?"top":"left",h=w?"bottom":"right",m=w?"clientHeight":"clientWidth",_=i.reference[v]+i.reference[g]-f[g]-i.floating[v],C=f[g]-i.reference[g],k=await(o.getOffsetParent==null?void 0:o.getOffsetParent(u));let P=k?k[m]:0;(!P||!await(o.isElement==null?void 0:o.isElement(k)))&&(P=a.floating[m]||i.floating[v]);const b=_/2-C/2,F=P/2-y[v]/2-1,j=Pr(d[p],F),Y=Pr(d[h],F),L=j,U=P-y[v]-Y,O=P/2-y[v]/2+b,ne=Hd(L,O,U),X=!l.arrow&&Ei(s)!=null&&O!==ne&&i.reference[v]/2-(O<L?j:Y)-y[v]/2<0,Q=X?O<L?O-L:O-U:0;return{[g]:f[g]+Q,data:{[g]:ne,centerOffset:O-ne-Q,...X&&{alignmentOffset:Q}},reset:X}}}),fE=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n,r;const{placement:s,middlewareData:i,rects:o,initialPlacement:a,platform:l,elements:u}=t,{mainAxis:c=!0,crossAxis:d=!0,fallbackPlacements:f,fallbackStrategy:g="bestFit",fallbackAxisSideDirection:v="none",flipAlignment:y=!0,...w}=Kn(e,t);if((n=i.arrow)!=null&&n.alignmentOffset)return{};const p=qn(s),h=Ar(a),m=qn(a)===a,_=await(l.isRTL==null?void 0:l.isRTL(u.floating)),C=f||(m||!y?[zl(a)]:oE(a)),k=v!=="none";!f&&k&&C.push(...lE(a,y,v,_));const P=[a,...C],b=await Lo(t,w),F=[];let j=((r=i.flip)==null?void 0:r.overflows)||[];if(c&&F.push(b[p]),d){const O=iE(s,o,_);F.push(b[O[0]],b[O[1]])}if(j=[...j,{placement:s,overflows:F}],!F.every(O=>O<=0)){var Y,L;const O=(((Y=i.flip)==null?void 0:Y.index)||0)+1,ne=P[O];if(ne)return{data:{index:O,overflows:j},reset:{placement:ne}};let X=(L=j.filter(Q=>Q.overflows[0]<=0).sort((Q,A)=>Q.overflows[1]-A.overflows[1])[0])==null?void 0:L.placement;if(!X)switch(g){case"bestFit":{var U;const Q=(U=j.filter(A=>{if(k){const I=Ar(A.placement);return I===h||I==="y"}return!0}).map(A=>[A.placement,A.overflows.filter(I=>I>0).reduce((I,K)=>I+K,0)]).sort((A,I)=>A[1]-I[1])[0])==null?void 0:U[0];Q&&(X=Q);break}case"initialPlacement":X=a;break}if(s!==X)return{reset:{placement:X}}}return{}}}};function Xm(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function Jm(e){return nE.some(t=>e[t]>=0)}const hE=function(e){return e===void 0&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:r="referenceHidden",...s}=Kn(e,t);switch(r){case"referenceHidden":{const i=await Lo(t,{...s,elementContext:"reference"}),o=Xm(i,n.reference);return{data:{referenceHiddenOffsets:o,referenceHidden:Jm(o)}}}case"escaped":{const i=await Lo(t,{...s,altBoundary:!0}),o=Xm(i,n.floating);return{data:{escapedOffsets:o,escaped:Jm(o)}}}default:return{}}}}};async function pE(e,t){const{placement:n,platform:r,elements:s}=e,i=await(r.isRTL==null?void 0:r.isRTL(s.floating)),o=qn(n),a=Ei(n),l=Ar(n)==="y",u=["left","top"].includes(o)?-1:1,c=i&&l?-1:1,d=Kn(t,e);let{mainAxis:f,crossAxis:g,alignmentAxis:v}=typeof d=="number"?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return a&&typeof v=="number"&&(g=a==="end"?v*-1:v),l?{x:g*c,y:f*u}:{x:f*u,y:g*c}}const mE=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var n,r;const{x:s,y:i,placement:o,middlewareData:a}=t,l=await pE(t,e);return o===((n=a.offset)==null?void 0:n.placement)&&(r=a.arrow)!=null&&r.alignmentOffset?{}:{x:s+l.x,y:i+l.y,data:{...l,placement:o}}}}},gE=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:r,placement:s}=t,{mainAxis:i=!0,crossAxis:o=!1,limiter:a={fn:w=>{let{x:p,y:h}=w;return{x:p,y:h}}},...l}=Kn(e,t),u={x:n,y:r},c=await Lo(t,l),d=Ar(qn(s)),f=kh(d);let g=u[f],v=u[d];if(i){const w=f==="y"?"top":"left",p=f==="y"?"bottom":"right",h=g+c[w],m=g-c[p];g=Hd(h,g,m)}if(o){const w=d==="y"?"top":"left",p=d==="y"?"bottom":"right",h=v+c[w],m=v-c[p];v=Hd(h,v,m)}const y=a.fn({...t,[f]:g,[d]:v});return{...y,data:{x:y.x-n,y:y.y-r,enabled:{[f]:i,[d]:o}}}}}},yE=function(e){return e===void 0&&(e={}),{options:e,fn(t){const{x:n,y:r,placement:s,rects:i,middlewareData:o}=t,{offset:a=0,mainAxis:l=!0,crossAxis:u=!0}=Kn(e,t),c={x:n,y:r},d=Ar(s),f=kh(d);let g=c[f],v=c[d];const y=Kn(a,t),w=typeof y=="number"?{mainAxis:y,crossAxis:0}:{mainAxis:0,crossAxis:0,...y};if(l){const m=f==="y"?"height":"width",_=i.reference[f]-i.floating[m]+w.mainAxis,C=i.reference[f]+i.reference[m]-w.mainAxis;g<_?g=_:g>C&&(g=C)}if(u){var p,h;const m=f==="y"?"width":"height",_=["top","left"].includes(qn(s)),C=i.reference[d]-i.floating[m]+(_&&((p=o.offset)==null?void 0:p[d])||0)+(_?0:w.crossAxis),k=i.reference[d]+i.reference[m]+(_?0:((h=o.offset)==null?void 0:h[d])||0)-(_?w.crossAxis:0);v<C?v=C:v>k&&(v=k)}return{[f]:g,[d]:v}}}},vE=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(t){var n,r;const{placement:s,rects:i,platform:o,elements:a}=t,{apply:l=()=>{},...u}=Kn(e,t),c=await Lo(t,u),d=qn(s),f=Ei(s),g=Ar(s)==="y",{width:v,height:y}=i.floating;let w,p;d==="top"||d==="bottom"?(w=d,p=f===(await(o.isRTL==null?void 0:o.isRTL(a.floating))?"start":"end")?"left":"right"):(p=d,w=f==="end"?"top":"bottom");const h=y-c.top-c.bottom,m=v-c.left-c.right,_=Pr(y-c[w],h),C=Pr(v-c[p],m),k=!t.middlewareData.shift;let P=_,b=C;if((n=t.middlewareData.shift)!=null&&n.enabled.x&&(b=m),(r=t.middlewareData.shift)!=null&&r.enabled.y&&(P=h),k&&!f){const j=Et(c.left,0),Y=Et(c.right,0),L=Et(c.top,0),U=Et(c.bottom,0);g?b=v-2*(j!==0||Y!==0?j+Y:Et(c.left,c.right)):P=y-2*(L!==0||U!==0?L+U:Et(c.top,c.bottom))}await l({...t,availableWidth:b,availableHeight:P});const F=await o.getDimensions(a.floating);return v!==F.width||y!==F.height?{reset:{rects:!0}}:{}}}};function Mu(){return typeof window<"u"}function Pi(e){return aw(e)?(e.nodeName||"").toLowerCase():"#document"}function Rt(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function An(e){var t;return(t=(aw(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function aw(e){return Mu()?e instanceof Node||e instanceof Rt(e).Node:!1}function ln(e){return Mu()?e instanceof Element||e instanceof Rt(e).Element:!1}function bn(e){return Mu()?e instanceof HTMLElement||e instanceof Rt(e).HTMLElement:!1}function eg(e){return!Mu()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof Rt(e).ShadowRoot}function la(e){const{overflow:t,overflowX:n,overflowY:r,display:s}=un(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(s)}function xE(e){return["table","td","th"].includes(Pi(e))}function Du(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch{return!1}})}function Ph(e){const t=Ah(),n=ln(e)?un(e):e;return["transform","translate","scale","rotate","perspective"].some(r=>n[r]?n[r]!=="none":!1)||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||["transform","translate","scale","rotate","perspective","filter"].some(r=>(n.willChange||"").includes(r))||["paint","layout","strict","content"].some(r=>(n.contain||"").includes(r))}function wE(e){let t=Rr(e);for(;bn(t)&&!mi(t);){if(Ph(t))return t;if(Du(t))return null;t=Rr(t)}return null}function Ah(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function mi(e){return["html","body","#document"].includes(Pi(e))}function un(e){return Rt(e).getComputedStyle(e)}function Ou(e){return ln(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function Rr(e){if(Pi(e)==="html")return e;const t=e.assignedSlot||e.parentNode||eg(e)&&e.host||An(e);return eg(t)?t.host:t}function lw(e){const t=Rr(e);return mi(t)?e.ownerDocument?e.ownerDocument.body:e.body:bn(t)&&la(t)?t:lw(t)}function jo(e,t,n){var r;t===void 0&&(t=[]),n===void 0&&(n=!0);const s=lw(e),i=s===((r=e.ownerDocument)==null?void 0:r.body),o=Rt(s);if(i){const a=Kd(o);return t.concat(o,o.visualViewport||[],la(s)?s:[],a&&n?jo(a):[])}return t.concat(s,jo(s,[],n))}function Kd(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function uw(e){const t=un(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const s=bn(e),i=s?e.offsetWidth:n,o=s?e.offsetHeight:r,a=Il(n)!==i||Il(r)!==o;return a&&(n=i,r=o),{width:n,height:r,$:a}}function Rh(e){return ln(e)?e:e.contextElement}function Qs(e){const t=Rh(e);if(!bn(t))return Sn(1);const n=t.getBoundingClientRect(),{width:r,height:s,$:i}=uw(t);let o=(i?Il(n.width):n.width)/r,a=(i?Il(n.height):n.height)/s;return(!o||!Number.isFinite(o))&&(o=1),(!a||!Number.isFinite(a))&&(a=1),{x:o,y:a}}const SE=Sn(0);function cw(e){const t=Rt(e);return!Ah()||!t.visualViewport?SE:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function _E(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==Rt(e)?!1:t}function fs(e,t,n,r){t===void 0&&(t=!1),n===void 0&&(n=!1);const s=e.getBoundingClientRect(),i=Rh(e);let o=Sn(1);t&&(r?ln(r)&&(o=Qs(r)):o=Qs(e));const a=_E(i,n,r)?cw(i):Sn(0);let l=(s.left+a.x)/o.x,u=(s.top+a.y)/o.y,c=s.width/o.x,d=s.height/o.y;if(i){const f=Rt(i),g=r&&ln(r)?Rt(r):r;let v=f,y=Kd(v);for(;y&&r&&g!==v;){const w=Qs(y),p=y.getBoundingClientRect(),h=un(y),m=p.left+(y.clientLeft+parseFloat(h.paddingLeft))*w.x,_=p.top+(y.clientTop+parseFloat(h.paddingTop))*w.y;l*=w.x,u*=w.y,c*=w.x,d*=w.y,l+=m,u+=_,v=Rt(y),y=Kd(v)}}return Bl({width:c,height:d,x:l,y:u})}function Nh(e,t){const n=Ou(e).scrollLeft;return t?t.left+n:fs(An(e)).left+n}function dw(e,t,n){n===void 0&&(n=!1);const r=e.getBoundingClientRect(),s=r.left+t.scrollLeft-(n?0:Nh(e,r)),i=r.top+t.scrollTop;return{x:s,y:i}}function TE(e){let{elements:t,rect:n,offsetParent:r,strategy:s}=e;const i=s==="fixed",o=An(r),a=t?Du(t.floating):!1;if(r===o||a&&i)return n;let l={scrollLeft:0,scrollTop:0},u=Sn(1);const c=Sn(0),d=bn(r);if((d||!d&&!i)&&((Pi(r)!=="body"||la(o))&&(l=Ou(r)),bn(r))){const g=fs(r);u=Qs(r),c.x=g.x+r.clientLeft,c.y=g.y+r.clientTop}const f=o&&!d&&!i?dw(o,l,!0):Sn(0);return{width:n.width*u.x,height:n.height*u.y,x:n.x*u.x-l.scrollLeft*u.x+c.x+f.x,y:n.y*u.y-l.scrollTop*u.y+c.y+f.y}}function CE(e){return Array.from(e.getClientRects())}function kE(e){const t=An(e),n=Ou(e),r=e.ownerDocument.body,s=Et(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=Et(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let o=-n.scrollLeft+Nh(e);const a=-n.scrollTop;return un(r).direction==="rtl"&&(o+=Et(t.clientWidth,r.clientWidth)-s),{width:s,height:i,x:o,y:a}}function bE(e,t){const n=Rt(e),r=An(e),s=n.visualViewport;let i=r.clientWidth,o=r.clientHeight,a=0,l=0;if(s){i=s.width,o=s.height;const u=Ah();(!u||u&&t==="fixed")&&(a=s.offsetLeft,l=s.offsetTop)}return{width:i,height:o,x:a,y:l}}function EE(e,t){const n=fs(e,!0,t==="fixed"),r=n.top+e.clientTop,s=n.left+e.clientLeft,i=bn(e)?Qs(e):Sn(1),o=e.clientWidth*i.x,a=e.clientHeight*i.y,l=s*i.x,u=r*i.y;return{width:o,height:a,x:l,y:u}}function tg(e,t,n){let r;if(t==="viewport")r=bE(e,n);else if(t==="document")r=kE(An(e));else if(ln(t))r=EE(t,n);else{const s=cw(e);r={x:t.x-s.x,y:t.y-s.y,width:t.width,height:t.height}}return Bl(r)}function fw(e,t){const n=Rr(e);return n===t||!ln(n)||mi(n)?!1:un(n).position==="fixed"||fw(n,t)}function PE(e,t){const n=t.get(e);if(n)return n;let r=jo(e,[],!1).filter(a=>ln(a)&&Pi(a)!=="body"),s=null;const i=un(e).position==="fixed";let o=i?Rr(e):e;for(;ln(o)&&!mi(o);){const a=un(o),l=Ph(o);!l&&a.position==="fixed"&&(s=null),(i?!l&&!s:!l&&a.position==="static"&&!!s&&["absolute","fixed"].includes(s.position)||la(o)&&!l&&fw(e,o))?r=r.filter(c=>c!==o):s=a,o=Rr(o)}return t.set(e,r),r}function AE(e){let{element:t,boundary:n,rootBoundary:r,strategy:s}=e;const o=[...n==="clippingAncestors"?Du(t)?[]:PE(t,this._c):[].concat(n),r],a=o[0],l=o.reduce((u,c)=>{const d=tg(t,c,s);return u.top=Et(d.top,u.top),u.right=Pr(d.right,u.right),u.bottom=Pr(d.bottom,u.bottom),u.left=Et(d.left,u.left),u},tg(t,a,s));return{width:l.right-l.left,height:l.bottom-l.top,x:l.left,y:l.top}}function RE(e){const{width:t,height:n}=uw(e);return{width:t,height:n}}function NE(e,t,n){const r=bn(t),s=An(t),i=n==="fixed",o=fs(e,!0,i,t);let a={scrollLeft:0,scrollTop:0};const l=Sn(0);if(r||!r&&!i)if((Pi(t)!=="body"||la(s))&&(a=Ou(t)),r){const f=fs(t,!0,i,t);l.x=f.x+t.clientLeft,l.y=f.y+t.clientTop}else s&&(l.x=Nh(s));const u=s&&!r&&!i?dw(s,a):Sn(0),c=o.left+a.scrollLeft-l.x-u.x,d=o.top+a.scrollTop-l.y-u.y;return{x:c,y:d,width:o.width,height:o.height}}function Ac(e){return un(e).position==="static"}function ng(e,t){if(!bn(e)||un(e).position==="fixed")return null;if(t)return t(e);let n=e.offsetParent;return An(e)===n&&(n=n.ownerDocument.body),n}function hw(e,t){const n=Rt(e);if(Du(e))return n;if(!bn(e)){let s=Rr(e);for(;s&&!mi(s);){if(ln(s)&&!Ac(s))return s;s=Rr(s)}return n}let r=ng(e,t);for(;r&&xE(r)&&Ac(r);)r=ng(r,t);return r&&mi(r)&&Ac(r)&&!Ph(r)?n:r||wE(e)||n}const ME=async function(e){const t=this.getOffsetParent||hw,n=this.getDimensions,r=await n(e.floating);return{reference:NE(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}};function DE(e){return un(e).direction==="rtl"}const OE={convertOffsetParentRelativeRectToViewportRelativeRect:TE,getDocumentElement:An,getClippingRect:AE,getOffsetParent:hw,getElementRects:ME,getClientRects:CE,getDimensions:RE,getScale:Qs,isElement:ln,isRTL:DE};function pw(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function LE(e,t){let n=null,r;const s=An(e);function i(){var a;clearTimeout(r),(a=n)==null||a.disconnect(),n=null}function o(a,l){a===void 0&&(a=!1),l===void 0&&(l=1),i();const u=e.getBoundingClientRect(),{left:c,top:d,width:f,height:g}=u;if(a||t(),!f||!g)return;const v=Ia(d),y=Ia(s.clientWidth-(c+f)),w=Ia(s.clientHeight-(d+g)),p=Ia(c),m={rootMargin:-v+"px "+-y+"px "+-w+"px "+-p+"px",threshold:Et(0,Pr(1,l))||1};let _=!0;function C(k){const P=k[0].intersectionRatio;if(P!==l){if(!_)return o();P?o(!1,P):r=setTimeout(()=>{o(!1,1e-7)},1e3)}P===1&&!pw(u,e.getBoundingClientRect())&&o(),_=!1}try{n=new IntersectionObserver(C,{...m,root:s.ownerDocument})}catch{n=new IntersectionObserver(C,m)}n.observe(e)}return o(!0),i}function jE(e,t,n,r){r===void 0&&(r={});const{ancestorScroll:s=!0,ancestorResize:i=!0,elementResize:o=typeof ResizeObserver=="function",layoutShift:a=typeof IntersectionObserver=="function",animationFrame:l=!1}=r,u=Rh(e),c=s||i?[...u?jo(u):[],...jo(t)]:[];c.forEach(p=>{s&&p.addEventListener("scroll",n,{passive:!0}),i&&p.addEventListener("resize",n)});const d=u&&a?LE(u,n):null;let f=-1,g=null;o&&(g=new ResizeObserver(p=>{let[h]=p;h&&h.target===u&&g&&(g.unobserve(t),cancelAnimationFrame(f),f=requestAnimationFrame(()=>{var m;(m=g)==null||m.observe(t)})),n()}),u&&!l&&g.observe(u),g.observe(t));let v,y=l?fs(e):null;l&&w();function w(){const p=fs(e);y&&!pw(y,p)&&n(),y=p,v=requestAnimationFrame(w)}return n(),()=>{var p;c.forEach(h=>{s&&h.removeEventListener("scroll",n),i&&h.removeEventListener("resize",n)}),d==null||d(),(p=g)==null||p.disconnect(),g=null,l&&cancelAnimationFrame(v)}}const VE=mE,FE=gE,IE=fE,zE=vE,BE=hE,rg=dE,$E=yE,UE=(e,t,n)=>{const r=new Map,s={platform:OE,...n},i={...s.platform,_c:r};return cE(e,t,{...s,platform:i})};var sl=typeof document<"u"?x.useLayoutEffect:x.useEffect;function $l(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if(typeof e=="function"&&e.toString()===t.toString())return!0;let n,r,s;if(e&&t&&typeof e=="object"){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(r=n;r--!==0;)if(!$l(e[r],t[r]))return!1;return!0}if(s=Object.keys(e),n=s.length,n!==Object.keys(t).length)return!1;for(r=n;r--!==0;)if(!{}.hasOwnProperty.call(t,s[r]))return!1;for(r=n;r--!==0;){const i=s[r];if(!(i==="_owner"&&e.$$typeof)&&!$l(e[i],t[i]))return!1}return!0}return e!==e&&t!==t}function mw(e){return typeof window>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function sg(e,t){const n=mw(e);return Math.round(t*n)/n}function Rc(e){const t=x.useRef(e);return sl(()=>{t.current=e}),t}function WE(e){e===void 0&&(e={});const{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:s,elements:{reference:i,floating:o}={},transform:a=!0,whileElementsMounted:l,open:u}=e,[c,d]=x.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[f,g]=x.useState(r);$l(f,r)||g(r);const[v,y]=x.useState(null),[w,p]=x.useState(null),h=x.useCallback(A=>{A!==k.current&&(k.current=A,y(A))},[]),m=x.useCallback(A=>{A!==P.current&&(P.current=A,p(A))},[]),_=i||v,C=o||w,k=x.useRef(null),P=x.useRef(null),b=x.useRef(c),F=l!=null,j=Rc(l),Y=Rc(s),L=Rc(u),U=x.useCallback(()=>{if(!k.current||!P.current)return;const A={placement:t,strategy:n,middleware:f};Y.current&&(A.platform=Y.current),UE(k.current,P.current,A).then(I=>{const K={...I,isPositioned:L.current!==!1};O.current&&!$l(b.current,K)&&(b.current=K,aa.flushSync(()=>{d(K)}))})},[f,t,n,Y,L]);sl(()=>{u===!1&&b.current.isPositioned&&(b.current.isPositioned=!1,d(A=>({...A,isPositioned:!1})))},[u]);const O=x.useRef(!1);sl(()=>(O.current=!0,()=>{O.current=!1}),[]),sl(()=>{if(_&&(k.current=_),C&&(P.current=C),_&&C){if(j.current)return j.current(_,C,U);U()}},[_,C,U,j,F]);const ne=x.useMemo(()=>({reference:k,floating:P,setReference:h,setFloating:m}),[h,m]),X=x.useMemo(()=>({reference:_,floating:C}),[_,C]),Q=x.useMemo(()=>{const A={position:n,left:0,top:0};if(!X.floating)return A;const I=sg(X.floating,c.x),K=sg(X.floating,c.y);return a?{...A,transform:"translate("+I+"px, "+K+"px)",...mw(X.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:I,top:K}},[n,a,X.floating,c.x,c.y]);return x.useMemo(()=>({...c,update:U,refs:ne,elements:X,floatingStyles:Q}),[c,U,ne,X,Q])}const HE=e=>{function t(n){return{}.hasOwnProperty.call(n,"current")}return{name:"arrow",options:e,fn(n){const{element:r,padding:s}=typeof e=="function"?e(n):e;return r&&t(r)?r.current!=null?rg({element:r.current,padding:s}).fn(n):{}:r?rg({element:r,padding:s}).fn(n):{}}}},ZE=(e,t)=>({...VE(e),options:[e,t]}),KE=(e,t)=>({...FE(e),options:[e,t]}),qE=(e,t)=>({...$E(e),options:[e,t]}),GE=(e,t)=>({...IE(e),options:[e,t]}),QE=(e,t)=>({...zE(e),options:[e,t]}),YE=(e,t)=>({...BE(e),options:[e,t]}),XE=(e,t)=>({...HE(e),options:[e,t]});var JE="Arrow",gw=x.forwardRef((e,t)=>{const{children:n,width:r=10,height:s=5,...i}=e;return S.jsx(gt.svg,{...i,ref:t,width:r,height:s,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:S.jsx("polygon",{points:"0,0 30,0 15,10"})})});gw.displayName=JE;var eP=gw;function tP(e){const[t,n]=x.useState(void 0);return ds(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});const r=new ResizeObserver(s=>{if(!Array.isArray(s)||!s.length)return;const i=s[0];let o,a;if("borderBoxSize"in i){const l=i.borderBoxSize,u=Array.isArray(l)?l[0]:l;o=u.inlineSize,a=u.blockSize}else o=e.offsetWidth,a=e.offsetHeight;n({width:o,height:a})});return r.observe(e,{box:"border-box"}),()=>r.unobserve(e)}else n(void 0)},[e]),t}var yw="Popper",[vw,xw]=Pu(yw),[QD,ww]=vw(yw),Sw="PopperAnchor",_w=x.forwardRef((e,t)=>{const{__scopePopper:n,virtualRef:r,...s}=e,i=ww(Sw,n),o=x.useRef(null),a=an(t,o);return x.useEffect(()=>{i.onAnchorChange((r==null?void 0:r.current)||o.current)}),r?null:S.jsx(gt.div,{...s,ref:a})});_w.displayName=Sw;var Mh="PopperContent",[nP,rP]=vw(Mh),Tw=x.forwardRef((e,t)=>{var dn,ma,xs,Ri,ws,Ni;const{__scopePopper:n,side:r="bottom",sideOffset:s=0,align:i="center",alignOffset:o=0,arrowPadding:a=0,avoidCollisions:l=!0,collisionBoundary:u=[],collisionPadding:c=0,sticky:d="partial",hideWhenDetached:f=!1,updatePositionStrategy:g="optimized",onPlaced:v,...y}=e,w=ww(Mh,n),[p,h]=x.useState(null),m=an(t,Br=>h(Br)),[_,C]=x.useState(null),k=tP(_),P=(k==null?void 0:k.width)??0,b=(k==null?void 0:k.height)??0,F=r+(i!=="center"?"-"+i:""),j=typeof c=="number"?c:{top:0,right:0,bottom:0,left:0,...c},Y=Array.isArray(u)?u:[u],L=Y.length>0,U={padding:j,boundary:Y.filter(iP),altBoundary:L},{refs:O,floatingStyles:ne,placement:X,isPositioned:Q,middlewareData:A}=WE({strategy:"fixed",placement:F,whileElementsMounted:(...Br)=>jE(...Br,{animationFrame:g==="always"}),elements:{reference:w.anchor},middleware:[ZE({mainAxis:s+b,alignmentAxis:o}),l&&KE({mainAxis:!0,crossAxis:!1,limiter:d==="partial"?qE():void 0,...U}),l&&GE({...U}),QE({...U,apply:({elements:Br,rects:Mi,availableWidth:Zu,availableHeight:ga})=>{const{width:ya,height:Ku}=Mi.reference,Ss=Br.floating.style;Ss.setProperty("--radix-popper-available-width",`${Zu}px`),Ss.setProperty("--radix-popper-available-height",`${ga}px`),Ss.setProperty("--radix-popper-anchor-width",`${ya}px`),Ss.setProperty("--radix-popper-anchor-height",`${Ku}px`)}}),_&&XE({element:_,padding:a}),oP({arrowWidth:P,arrowHeight:b}),f&&YE({strategy:"referenceHidden",...U})]}),[I,K]=bw(X),re=kn(v);ds(()=>{Q&&(re==null||re())},[Q,re]);const ye=(dn=A.arrow)==null?void 0:dn.x,yt=(ma=A.arrow)==null?void 0:ma.y,vt=((xs=A.arrow)==null?void 0:xs.centerOffset)!==0,[Xn,Ht]=x.useState();return ds(()=>{p&&Ht(window.getComputedStyle(p).zIndex)},[p]),S.jsx("div",{ref:O.setFloating,"data-radix-popper-content-wrapper":"",style:{...ne,transform:Q?ne.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:Xn,"--radix-popper-transform-origin":[(Ri=A.transformOrigin)==null?void 0:Ri.x,(ws=A.transformOrigin)==null?void 0:ws.y].join(" "),...((Ni=A.hide)==null?void 0:Ni.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:S.jsx(nP,{scope:n,placedSide:I,onArrowChange:C,arrowX:ye,arrowY:yt,shouldHideArrow:vt,children:S.jsx(gt.div,{"data-side":I,"data-align":K,...y,ref:m,style:{...y.style,animation:Q?void 0:"none"}})})})});Tw.displayName=Mh;var Cw="PopperArrow",sP={top:"bottom",right:"left",bottom:"top",left:"right"},kw=x.forwardRef(function(t,n){const{__scopePopper:r,...s}=t,i=rP(Cw,r),o=sP[i.placedSide];return S.jsx("span",{ref:i.onArrowChange,style:{position:"absolute",left:i.arrowX,top:i.arrowY,[o]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[i.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[i.placedSide],visibility:i.shouldHideArrow?"hidden":void 0},children:S.jsx(eP,{...s,ref:n,style:{...s.style,display:"block"}})})});kw.displayName=Cw;function iP(e){return e!==null}var oP=e=>({name:"transformOrigin",options:e,fn(t){var w,p,h;const{placement:n,rects:r,middlewareData:s}=t,o=((w=s.arrow)==null?void 0:w.centerOffset)!==0,a=o?0:e.arrowWidth,l=o?0:e.arrowHeight,[u,c]=bw(n),d={start:"0%",center:"50%",end:"100%"}[c],f=(((p=s.arrow)==null?void 0:p.x)??0)+a/2,g=(((h=s.arrow)==null?void 0:h.y)??0)+l/2;let v="",y="";return u==="bottom"?(v=o?d:`${f}px`,y=`${-l}px`):u==="top"?(v=o?d:`${f}px`,y=`${r.floating.height+l}px`):u==="right"?(v=`${-l}px`,y=o?d:`${g}px`):u==="left"&&(v=`${r.floating.width+l}px`,y=o?d:`${g}px`),{data:{x:v,y}}}});function bw(e){const[t,n="center"]=e.split("-");return[t,n]}var aP=_w,lP=Tw,uP=kw,[Lu,YD]=Pu("Tooltip",[xw]),Dh=xw(),Ew="TooltipProvider",cP=700,ig="tooltip.open",[dP,Pw]=Lu(Ew),Aw=e=>{const{__scopeTooltip:t,delayDuration:n=cP,skipDelayDuration:r=300,disableHoverableContent:s=!1,children:i}=e,o=x.useRef(!0),a=x.useRef(!1),l=x.useRef(0);return x.useEffect(()=>{const u=l.current;return()=>window.clearTimeout(u)},[]),S.jsx(dP,{scope:t,isOpenDelayedRef:o,delayDuration:n,onOpen:x.useCallback(()=>{window.clearTimeout(l.current),o.current=!1},[]),onClose:x.useCallback(()=>{window.clearTimeout(l.current),l.current=window.setTimeout(()=>o.current=!0,r)},[r]),isPointerInTransitRef:a,onPointerInTransitChange:x.useCallback(u=>{a.current=u},[]),disableHoverableContent:s,children:i})};Aw.displayName=Ew;var Rw="Tooltip",[XD,ju]=Lu(Rw),qd="TooltipTrigger",fP=x.forwardRef((e,t)=>{const{__scopeTooltip:n,...r}=e,s=ju(qd,n),i=Pw(qd,n),o=Dh(n),a=x.useRef(null),l=an(t,a,s.onTriggerChange),u=x.useRef(!1),c=x.useRef(!1),d=x.useCallback(()=>u.current=!1,[]);return x.useEffect(()=>()=>document.removeEventListener("pointerup",d),[d]),S.jsx(aP,{asChild:!0,...o,children:S.jsx(gt.button,{"aria-describedby":s.open?s.contentId:void 0,"data-state":s.stateAttribute,...r,ref:l,onPointerMove:Be(e.onPointerMove,f=>{f.pointerType!=="touch"&&!c.current&&!i.isPointerInTransitRef.current&&(s.onTriggerEnter(),c.current=!0)}),onPointerLeave:Be(e.onPointerLeave,()=>{s.onTriggerLeave(),c.current=!1}),onPointerDown:Be(e.onPointerDown,()=>{s.open&&s.onClose(),u.current=!0,document.addEventListener("pointerup",d,{once:!0})}),onFocus:Be(e.onFocus,()=>{u.current||s.onOpen()}),onBlur:Be(e.onBlur,s.onClose),onClick:Be(e.onClick,s.onClose)})})});fP.displayName=qd;var hP="TooltipPortal",[JD,pP]=Lu(hP,{forceMount:void 0}),gi="TooltipContent",Nw=x.forwardRef((e,t)=>{const n=pP(gi,e.__scopeTooltip),{forceMount:r=n.forceMount,side:s="top",...i}=e,o=ju(gi,e.__scopeTooltip);return S.jsx(xh,{present:r||o.open,children:o.disableHoverableContent?S.jsx(Mw,{side:s,...i,ref:t}):S.jsx(mP,{side:s,...i,ref:t})})}),mP=x.forwardRef((e,t)=>{const n=ju(gi,e.__scopeTooltip),r=Pw(gi,e.__scopeTooltip),s=x.useRef(null),i=an(t,s),[o,a]=x.useState(null),{trigger:l,onClose:u}=n,c=s.current,{onPointerInTransitChange:d}=r,f=x.useCallback(()=>{a(null),d(!1)},[d]),g=x.useCallback((v,y)=>{const w=v.currentTarget,p={x:v.clientX,y:v.clientY},h=wP(p,w.getBoundingClientRect()),m=SP(p,h),_=_P(y.getBoundingClientRect()),C=CP([...m,..._]);a(C),d(!0)},[d]);return x.useEffect(()=>()=>f(),[f]),x.useEffect(()=>{if(l&&c){const v=w=>g(w,c),y=w=>g(w,l);return l.addEventListener("pointerleave",v),c.addEventListener("pointerleave",y),()=>{l.removeEventListener("pointerleave",v),c.removeEventListener("pointerleave",y)}}},[l,c,g,f]),x.useEffect(()=>{if(o){const v=y=>{const w=y.target,p={x:y.clientX,y:y.clientY},h=(l==null?void 0:l.contains(w))||(c==null?void 0:c.contains(w)),m=!TP(p,o);h?f():m&&(f(),u())};return document.addEventListener("pointermove",v),()=>document.removeEventListener("pointermove",v)}},[l,c,o,u,f]),S.jsx(Mw,{...e,ref:i})}),[gP,yP]=Lu(Rw,{isInside:!1}),vP=Pk("TooltipContent"),Mw=x.forwardRef((e,t)=>{const{__scopeTooltip:n,children:r,"aria-label":s,onEscapeKeyDown:i,onPointerDownOutside:o,...a}=e,l=ju(gi,n),u=Dh(n),{onClose:c}=l;return x.useEffect(()=>(document.addEventListener(ig,c),()=>document.removeEventListener(ig,c)),[c]),x.useEffect(()=>{if(l.trigger){const d=f=>{const g=f.target;g!=null&&g.contains(l.trigger)&&c()};return window.addEventListener("scroll",d,{capture:!0}),()=>window.removeEventListener("scroll",d,{capture:!0})}},[l.trigger,c]),S.jsx(vh,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:i,onPointerDownOutside:o,onFocusOutside:d=>d.preventDefault(),onDismiss:c,children:S.jsxs(lP,{"data-state":l.stateAttribute,...u,...a,ref:t,style:{...a.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[S.jsx(vP,{children:r}),S.jsx(gP,{scope:n,isInside:!0,children:S.jsx(Qk,{id:l.contentId,role:"tooltip",children:s||r})})]})})});Nw.displayName=gi;var Dw="TooltipArrow",xP=x.forwardRef((e,t)=>{const{__scopeTooltip:n,...r}=e,s=Dh(n);return yP(Dw,n).isInside?null:S.jsx(uP,{...s,...r,ref:t})});xP.displayName=Dw;function wP(e,t){const n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),s=Math.abs(t.right-e.x),i=Math.abs(t.left-e.x);switch(Math.min(n,r,s,i)){case i:return"left";case s:return"right";case n:return"top";case r:return"bottom";default:throw new Error("unreachable")}}function SP(e,t,n=5){const r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n});break}return r}function _P(e){const{top:t,right:n,bottom:r,left:s}=e;return[{x:s,y:t},{x:n,y:t},{x:n,y:r},{x:s,y:r}]}function TP(e,t){const{x:n,y:r}=e;let s=!1;for(let i=0,o=t.length-1;i<t.length;o=i++){const a=t[i].x,l=t[i].y,u=t[o].x,c=t[o].y;l>r!=c>r&&n<(u-a)*(r-l)/(c-l)+a&&(s=!s)}return s}function CP(e){const t=e.slice();return t.sort((n,r)=>n.x<r.x?-1:n.x>r.x?1:n.y<r.y?-1:n.y>r.y?1:0),kP(t)}function kP(e){if(e.length<=1)return e.slice();const t=[];for(let r=0;r<e.length;r++){const s=e[r];for(;t.length>=2;){const i=t[t.length-1],o=t[t.length-2];if((i.x-o.x)*(s.y-o.y)>=(i.y-o.y)*(s.x-o.x))t.pop();else break}t.push(s)}t.pop();const n=[];for(let r=e.length-1;r>=0;r--){const s=e[r];for(;n.length>=2;){const i=n[n.length-1],o=n[n.length-2];if((i.x-o.x)*(s.y-o.y)>=(i.y-o.y)*(s.x-o.x))n.pop();else break}n.push(s)}return n.pop(),t.length===1&&n.length===1&&t[0].x===n[0].x&&t[0].y===n[0].y?t:t.concat(n)}var bP=Aw,Ow=Nw;const EP=bP,PP=x.forwardRef(({className:e,sideOffset:t=4,...n},r)=>S.jsx(Ow,{ref:r,sideOffset:t,className:je("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-tooltip-content-transform-origin]",e),...n}));PP.displayName=Ow.displayName;function AP(e){if(typeof Proxy>"u")return e;const t=new Map,n=(...r)=>e(...r);return new Proxy(n,{get:(r,s)=>s==="create"?e:(t.has(s)||t.set(s,e(s)),t.get(s))})}function Vu(e){return e!==null&&typeof e=="object"&&typeof e.start=="function"}const Gd=e=>Array.isArray(e);function Lw(e,t){if(!Array.isArray(t))return!1;const n=t.length;if(n!==e.length)return!1;for(let r=0;r<n;r++)if(t[r]!==e[r])return!1;return!0}function Vo(e){return typeof e=="string"||Array.isArray(e)}function og(e){const t=[{},{}];return e==null||e.values.forEach((n,r)=>{t[0][r]=n.get(),t[1][r]=n.getVelocity()}),t}function Oh(e,t,n,r){if(typeof t=="function"){const[s,i]=og(r);t=t(n!==void 0?n:e.custom,s,i)}if(typeof t=="string"&&(t=e.variants&&e.variants[t]),typeof t=="function"){const[s,i]=og(r);t=t(n!==void 0?n:e.custom,s,i)}return t}function Fu(e,t,n){const r=e.getProps();return Oh(r,t,n!==void 0?n:r.custom,e)}const Lh=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],jh=["initial",...Lh],ua=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],vs=new Set(ua),Bn=e=>e*1e3,$n=e=>e/1e3,RP={type:"spring",stiffness:500,damping:25,restSpeed:10},NP=e=>({type:"spring",stiffness:550,damping:e===0?2*Math.sqrt(550):30,restSpeed:10}),MP={type:"keyframes",duration:.8},DP={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},OP=(e,{keyframes:t})=>t.length>2?MP:vs.has(e)?e.startsWith("scale")?NP(t[1]):RP:DP;function Vh(e,t){return e?e[t]||e.default||e:void 0}const LP={skipAnimations:!1,useManualTiming:!1},jP=e=>e!==null;function Iu(e,{repeat:t,repeatType:n="loop"},r){const s=e.filter(jP),i=t&&n!=="loop"&&t%2===1?0:s.length-1;return!i||r===void 0?s[i]:r}const st=e=>e;let Qd=st;function VP(e){let t=new Set,n=new Set,r=!1,s=!1;const i=new WeakSet;let o={delta:0,timestamp:0,isProcessing:!1};function a(u){i.has(u)&&(l.schedule(u),e()),u(o)}const l={schedule:(u,c=!1,d=!1)=>{const g=d&&r?t:n;return c&&i.add(u),g.has(u)||g.add(u),u},cancel:u=>{n.delete(u),i.delete(u)},process:u=>{if(o=u,r){s=!0;return}r=!0,[t,n]=[n,t],n.clear(),t.forEach(a),r=!1,s&&(s=!1,l.process(u))}};return l}const za=["read","resolveKeyframes","update","preRender","render","postRender"],FP=40;function jw(e,t){let n=!1,r=!0;const s={delta:0,timestamp:0,isProcessing:!1},i=()=>n=!0,o=za.reduce((p,h)=>(p[h]=VP(i),p),{}),{read:a,resolveKeyframes:l,update:u,preRender:c,render:d,postRender:f}=o,g=()=>{const p=performance.now();n=!1,s.delta=r?1e3/60:Math.max(Math.min(p-s.timestamp,FP),1),s.timestamp=p,s.isProcessing=!0,a.process(s),l.process(s),u.process(s),c.process(s),d.process(s),f.process(s),s.isProcessing=!1,n&&t&&(r=!1,e(g))},v=()=>{n=!0,r=!0,s.isProcessing||e(g)};return{schedule:za.reduce((p,h)=>{const m=o[h];return p[h]=(_,C=!1,k=!1)=>(n||v(),m.schedule(_,C,k)),p},{}),cancel:p=>{for(let h=0;h<za.length;h++)o[za[h]].cancel(p)},state:s,steps:o}}const{schedule:ge,cancel:Nr,state:Ke,steps:Nc}=jw(typeof requestAnimationFrame<"u"?requestAnimationFrame:st,!0),Vw=(e,t,n)=>(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e,IP=1e-7,zP=12;function BP(e,t,n,r,s){let i,o,a=0;do o=t+(n-t)/2,i=Vw(o,r,s)-e,i>0?n=o:t=o;while(Math.abs(i)>IP&&++a<zP);return o}function ca(e,t,n,r){if(e===t&&n===r)return st;const s=i=>BP(i,0,1,e,n);return i=>i===0||i===1?i:Vw(s(i),t,r)}const Fw=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,Iw=e=>t=>1-e(1-t),zw=ca(.33,1.53,.69,.99),Fh=Iw(zw),Bw=Fw(Fh),$w=e=>(e*=2)<1?.5*Fh(e):.5*(2-Math.pow(2,-10*(e-1))),Ih=e=>1-Math.sin(Math.acos(e)),Uw=Iw(Ih),Ww=Fw(Ih),Hw=e=>/^0[^.\s]+$/u.test(e);function $P(e){return typeof e=="number"?e===0:e!==null?e==="none"||e==="0"||Hw(e):!0}const Zw=e=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(e),Kw=e=>t=>typeof t=="string"&&t.startsWith(e),qw=Kw("--"),UP=Kw("var(--"),zh=e=>UP(e)?WP.test(e.split("/*")[0].trim()):!1,WP=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,HP=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function ZP(e){const t=HP.exec(e);if(!t)return[,];const[,n,r,s]=t;return[`--${n??r}`,s]}function Gw(e,t,n=1){const[r,s]=ZP(e);if(!r)return;const i=window.getComputedStyle(t).getPropertyValue(r);if(i){const o=i.trim();return Zw(o)?parseFloat(o):o}return zh(s)?Gw(s,t,n+1):s}const Gn=(e,t,n)=>n>t?t:n<e?e:n,Ai={test:e=>typeof e=="number",parse:parseFloat,transform:e=>e},Fo={...Ai,transform:e=>Gn(0,1,e)},Ba={...Ai,default:1},da=e=>({test:t=>typeof t=="string"&&t.endsWith(e)&&t.split(" ").length===1,parse:parseFloat,transform:t=>`${t}${e}`}),rr=da("deg"),_n=da("%"),J=da("px"),KP=da("vh"),qP=da("vw"),ag={..._n,parse:e=>_n.parse(e)/100,transform:e=>_n.transform(e*100)},GP=new Set(["width","height","top","left","right","bottom","x","y","translateX","translateY"]),lg=e=>e===Ai||e===J,ug=(e,t)=>parseFloat(e.split(", ")[t]),cg=(e,t)=>(n,{transform:r})=>{if(r==="none"||!r)return 0;const s=r.match(/^matrix3d\((.+)\)$/u);if(s)return ug(s[1],t);{const i=r.match(/^matrix\((.+)\)$/u);return i?ug(i[1],e):0}},QP=new Set(["x","y","z"]),YP=ua.filter(e=>!QP.has(e));function XP(e){const t=[];return YP.forEach(n=>{const r=e.getValue(n);r!==void 0&&(t.push([n,r.get()]),r.set(n.startsWith("scale")?1:0))}),t}const yi={width:({x:e},{paddingLeft:t="0",paddingRight:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),height:({y:e},{paddingTop:t="0",paddingBottom:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:cg(4,13),y:cg(5,14)};yi.translateX=yi.x;yi.translateY=yi.y;const Qw=e=>t=>t.test(e),JP={test:e=>e==="auto",parse:e=>e},Yw=[Ai,J,_n,rr,qP,KP,JP],dg=e=>Yw.find(Qw(e)),ss=new Set;let Yd=!1,Xd=!1;function Xw(){if(Xd){const e=Array.from(ss).filter(r=>r.needsMeasurement),t=new Set(e.map(r=>r.element)),n=new Map;t.forEach(r=>{const s=XP(r);s.length&&(n.set(r,s),r.render())}),e.forEach(r=>r.measureInitialState()),t.forEach(r=>{r.render();const s=n.get(r);s&&s.forEach(([i,o])=>{var a;(a=r.getValue(i))===null||a===void 0||a.set(o)})}),e.forEach(r=>r.measureEndState()),e.forEach(r=>{r.suspendedScrollY!==void 0&&window.scrollTo(0,r.suspendedScrollY)})}Xd=!1,Yd=!1,ss.forEach(e=>e.complete()),ss.clear()}function Jw(){ss.forEach(e=>{e.readKeyframes(),e.needsMeasurement&&(Xd=!0)})}function eA(){Jw(),Xw()}class Bh{constructor(t,n,r,s,i,o=!1){this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=[...t],this.onComplete=n,this.name=r,this.motionValue=s,this.element=i,this.isAsync=o}scheduleResolve(){this.isScheduled=!0,this.isAsync?(ss.add(this),Yd||(Yd=!0,ge.read(Jw),ge.resolveKeyframes(Xw))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:t,name:n,element:r,motionValue:s}=this;for(let i=0;i<t.length;i++)if(t[i]===null)if(i===0){const o=s==null?void 0:s.get(),a=t[t.length-1];if(o!==void 0)t[0]=o;else if(r&&n){const l=r.readValue(n,a);l!=null&&(t[0]=l)}t[0]===void 0&&(t[0]=a),s&&o===void 0&&s.set(t[0])}else t[i]=t[i-1]}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe),ss.delete(this)}cancel(){this.isComplete||(this.isScheduled=!1,ss.delete(this))}resume(){this.isComplete||this.scheduleResolve()}}const co=e=>Math.round(e*1e5)/1e5,$h=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;function tA(e){return e==null}const nA=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,Uh=(e,t)=>n=>!!(typeof n=="string"&&nA.test(n)&&n.startsWith(e)||t&&!tA(n)&&Object.prototype.hasOwnProperty.call(n,t)),e1=(e,t,n)=>r=>{if(typeof r!="string")return r;const[s,i,o,a]=r.match($h);return{[e]:parseFloat(s),[t]:parseFloat(i),[n]:parseFloat(o),alpha:a!==void 0?parseFloat(a):1}},rA=e=>Gn(0,255,e),Mc={...Ai,transform:e=>Math.round(rA(e))},Qr={test:Uh("rgb","red"),parse:e1("red","green","blue"),transform:({red:e,green:t,blue:n,alpha:r=1})=>"rgba("+Mc.transform(e)+", "+Mc.transform(t)+", "+Mc.transform(n)+", "+co(Fo.transform(r))+")"};function sA(e){let t="",n="",r="",s="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),r=e.substring(5,7),s=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),r=e.substring(3,4),s=e.substring(4,5),t+=t,n+=n,r+=r,s+=s),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:s?parseInt(s,16)/255:1}}const Jd={test:Uh("#"),parse:sA,transform:Qr.transform},Vs={test:Uh("hsl","hue"),parse:e1("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:n,alpha:r=1})=>"hsla("+Math.round(e)+", "+_n.transform(co(t))+", "+_n.transform(co(n))+", "+co(Fo.transform(r))+")"},tt={test:e=>Qr.test(e)||Jd.test(e)||Vs.test(e),parse:e=>Qr.test(e)?Qr.parse(e):Vs.test(e)?Vs.parse(e):Jd.parse(e),transform:e=>typeof e=="string"?e:e.hasOwnProperty("red")?Qr.transform(e):Vs.transform(e)},iA=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;function oA(e){var t,n;return isNaN(e)&&typeof e=="string"&&(((t=e.match($h))===null||t===void 0?void 0:t.length)||0)+(((n=e.match(iA))===null||n===void 0?void 0:n.length)||0)>0}const t1="number",n1="color",aA="var",lA="var(",fg="${}",uA=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function Io(e){const t=e.toString(),n=[],r={color:[],number:[],var:[]},s=[];let i=0;const a=t.replace(uA,l=>(tt.test(l)?(r.color.push(i),s.push(n1),n.push(tt.parse(l))):l.startsWith(lA)?(r.var.push(i),s.push(aA),n.push(l)):(r.number.push(i),s.push(t1),n.push(parseFloat(l))),++i,fg)).split(fg);return{values:n,split:a,indexes:r,types:s}}function r1(e){return Io(e).values}function s1(e){const{split:t,types:n}=Io(e),r=t.length;return s=>{let i="";for(let o=0;o<r;o++)if(i+=t[o],s[o]!==void 0){const a=n[o];a===t1?i+=co(s[o]):a===n1?i+=tt.transform(s[o]):i+=s[o]}return i}}const cA=e=>typeof e=="number"?0:e;function dA(e){const t=r1(e);return s1(e)(t.map(cA))}const Mr={test:oA,parse:r1,createTransformer:s1,getAnimatableNone:dA},fA=new Set(["brightness","contrast","saturate","opacity"]);function hA(e){const[t,n]=e.slice(0,-1).split("(");if(t==="drop-shadow")return e;const[r]=n.match($h)||[];if(!r)return e;const s=n.replace(r,"");let i=fA.has(t)?1:0;return r!==n&&(i*=100),t+"("+i+s+")"}const pA=/\b([a-z-]*)\(.*?\)/gu,ef={...Mr,getAnimatableNone:e=>{const t=e.match(pA);return t?t.map(hA).join(" "):e}},mA={borderWidth:J,borderTopWidth:J,borderRightWidth:J,borderBottomWidth:J,borderLeftWidth:J,borderRadius:J,radius:J,borderTopLeftRadius:J,borderTopRightRadius:J,borderBottomRightRadius:J,borderBottomLeftRadius:J,width:J,maxWidth:J,height:J,maxHeight:J,top:J,right:J,bottom:J,left:J,padding:J,paddingTop:J,paddingRight:J,paddingBottom:J,paddingLeft:J,margin:J,marginTop:J,marginRight:J,marginBottom:J,marginLeft:J,backgroundPositionX:J,backgroundPositionY:J},gA={rotate:rr,rotateX:rr,rotateY:rr,rotateZ:rr,scale:Ba,scaleX:Ba,scaleY:Ba,scaleZ:Ba,skew:rr,skewX:rr,skewY:rr,distance:J,translateX:J,translateY:J,translateZ:J,x:J,y:J,z:J,perspective:J,transformPerspective:J,opacity:Fo,originX:ag,originY:ag,originZ:J},hg={...Ai,transform:Math.round},Wh={...mA,...gA,zIndex:hg,size:J,fillOpacity:Fo,strokeOpacity:Fo,numOctaves:hg},yA={...Wh,color:tt,backgroundColor:tt,outlineColor:tt,fill:tt,stroke:tt,borderColor:tt,borderTopColor:tt,borderRightColor:tt,borderBottomColor:tt,borderLeftColor:tt,filter:ef,WebkitFilter:ef},Hh=e=>yA[e];function i1(e,t){let n=Hh(e);return n!==ef&&(n=Mr),n.getAnimatableNone?n.getAnimatableNone(t):void 0}const vA=new Set(["auto","none","0"]);function xA(e,t,n){let r=0,s;for(;r<e.length&&!s;){const i=e[r];typeof i=="string"&&!vA.has(i)&&Io(i).values.length&&(s=e[r]),r++}if(s&&n)for(const i of t)e[i]=i1(n,s)}class o1 extends Bh{constructor(t,n,r,s,i){super(t,n,r,s,i,!0)}readKeyframes(){const{unresolvedKeyframes:t,element:n,name:r}=this;if(!n||!n.current)return;super.readKeyframes();for(let l=0;l<t.length;l++){let u=t[l];if(typeof u=="string"&&(u=u.trim(),zh(u))){const c=Gw(u,n.current);c!==void 0&&(t[l]=c),l===t.length-1&&(this.finalKeyframe=u)}}if(this.resolveNoneKeyframes(),!GP.has(r)||t.length!==2)return;const[s,i]=t,o=dg(s),a=dg(i);if(o!==a)if(lg(o)&&lg(a))for(let l=0;l<t.length;l++){const u=t[l];typeof u=="string"&&(t[l]=parseFloat(u))}else this.needsMeasurement=!0}resolveNoneKeyframes(){const{unresolvedKeyframes:t,name:n}=this,r=[];for(let s=0;s<t.length;s++)$P(t[s])&&r.push(s);r.length&&xA(t,r,n)}measureInitialState(){const{element:t,unresolvedKeyframes:n,name:r}=this;if(!t||!t.current)return;r==="height"&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=yi[r](t.measureViewportBox(),window.getComputedStyle(t.current)),n[0]=this.measuredOrigin;const s=n[n.length-1];s!==void 0&&t.getValue(r,s).jump(s,!1)}measureEndState(){var t;const{element:n,name:r,unresolvedKeyframes:s}=this;if(!n||!n.current)return;const i=n.getValue(r);i&&i.jump(this.measuredOrigin,!1);const o=s.length-1,a=s[o];s[o]=yi[r](n.measureViewportBox(),window.getComputedStyle(n.current)),a!==null&&this.finalKeyframe===void 0&&(this.finalKeyframe=a),!((t=this.removedTransforms)===null||t===void 0)&&t.length&&this.removedTransforms.forEach(([l,u])=>{n.getValue(l).set(u)}),this.resolveNoneKeyframes()}}function Zh(e){return typeof e=="function"}let il;function wA(){il=void 0}const Tn={now:()=>(il===void 0&&Tn.set(Ke.isProcessing||LP.useManualTiming?Ke.timestamp:performance.now()),il),set:e=>{il=e,queueMicrotask(wA)}},pg=(e,t)=>t==="zIndex"?!1:!!(typeof e=="number"||Array.isArray(e)||typeof e=="string"&&(Mr.test(e)||e==="0")&&!e.startsWith("url("));function SA(e){const t=e[0];if(e.length===1)return!0;for(let n=0;n<e.length;n++)if(e[n]!==t)return!0}function _A(e,t,n,r){const s=e[0];if(s===null)return!1;if(t==="display"||t==="visibility")return!0;const i=e[e.length-1],o=pg(s,t),a=pg(i,t);return!o||!a?!1:SA(e)||(n==="spring"||Zh(n))&&r}const TA=40;class a1{constructor({autoplay:t=!0,delay:n=0,type:r="keyframes",repeat:s=0,repeatDelay:i=0,repeatType:o="loop",...a}){this.isStopped=!1,this.hasAttemptedResolve=!1,this.createdAt=Tn.now(),this.options={autoplay:t,delay:n,type:r,repeat:s,repeatDelay:i,repeatType:o,...a},this.updateFinishedPromise()}calcStartTime(){return this.resolvedAt?this.resolvedAt-this.createdAt>TA?this.resolvedAt:this.createdAt:this.createdAt}get resolved(){return!this._resolved&&!this.hasAttemptedResolve&&eA(),this._resolved}onKeyframesResolved(t,n){this.resolvedAt=Tn.now(),this.hasAttemptedResolve=!0;const{name:r,type:s,velocity:i,delay:o,onComplete:a,onUpdate:l,isGenerator:u}=this.options;if(!u&&!_A(t,r,s,i))if(o)this.options.duration=0;else{l==null||l(Iu(t,this.options,n)),a==null||a(),this.resolveFinishedPromise();return}const c=this.initPlayback(t,n);c!==!1&&(this._resolved={keyframes:t,finalKeyframe:n,...c},this.onPostResolved())}onPostResolved(){}then(t,n){return this.currentFinishedPromise.then(t,n)}flatten(){this.options.type="keyframes",this.options.ease="linear"}updateFinishedPromise(){this.currentFinishedPromise=new Promise(t=>{this.resolveFinishedPromise=t})}}const vi=(e,t,n)=>{const r=t-e;return r===0?1:(n-e)/r},l1=(e,t,n=10)=>{let r="";const s=Math.max(Math.round(t/n),2);for(let i=0;i<s;i++)r+=e(vi(0,s-1,i))+", ";return`linear(${r.substring(0,r.length-2)})`};function u1(e,t){return t?e*(1e3/t):0}const CA=5;function c1(e,t,n){const r=Math.max(t-CA,0);return u1(n-e(r),t-r)}const Re={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1},Dc=.001;function kA({duration:e=Re.duration,bounce:t=Re.bounce,velocity:n=Re.velocity,mass:r=Re.mass}){let s,i,o=1-t;o=Gn(Re.minDamping,Re.maxDamping,o),e=Gn(Re.minDuration,Re.maxDuration,$n(e)),o<1?(s=u=>{const c=u*o,d=c*e,f=c-n,g=tf(u,o),v=Math.exp(-d);return Dc-f/g*v},i=u=>{const d=u*o*e,f=d*n+n,g=Math.pow(o,2)*Math.pow(u,2)*e,v=Math.exp(-d),y=tf(Math.pow(u,2),o);return(-s(u)+Dc>0?-1:1)*((f-g)*v)/y}):(s=u=>{const c=Math.exp(-u*e),d=(u-n)*e+1;return-Dc+c*d},i=u=>{const c=Math.exp(-u*e),d=(n-u)*(e*e);return c*d});const a=5/e,l=EA(s,i,a);if(e=Bn(e),isNaN(l))return{stiffness:Re.stiffness,damping:Re.damping,duration:e};{const u=Math.pow(l,2)*r;return{stiffness:u,damping:o*2*Math.sqrt(r*u),duration:e}}}const bA=12;function EA(e,t,n){let r=n;for(let s=1;s<bA;s++)r=r-e(r)/t(r);return r}function tf(e,t){return e*Math.sqrt(1-t*t)}const nf=2e4;function d1(e){let t=0;const n=50;let r=e.next(t);for(;!r.done&&t<nf;)t+=n,r=e.next(t);return t>=nf?1/0:t}const PA=["duration","bounce"],AA=["stiffness","damping","mass"];function mg(e,t){return t.some(n=>e[n]!==void 0)}function RA(e){let t={velocity:Re.velocity,stiffness:Re.stiffness,damping:Re.damping,mass:Re.mass,isResolvedFromDuration:!1,...e};if(!mg(e,AA)&&mg(e,PA))if(e.visualDuration){const n=e.visualDuration,r=2*Math.PI/(n*1.2),s=r*r,i=2*Gn(.05,1,1-e.bounce)*Math.sqrt(s);t={...t,mass:Re.mass,stiffness:s,damping:i}}else{const n=kA(e);t={...t,...n,mass:Re.mass},t.isResolvedFromDuration=!0}return t}function f1(e=Re.visualDuration,t=Re.bounce){const n=typeof e!="object"?{visualDuration:e,keyframes:[0,1],bounce:t}:e;let{restSpeed:r,restDelta:s}=n;const i=n.keyframes[0],o=n.keyframes[n.keyframes.length-1],a={done:!1,value:i},{stiffness:l,damping:u,mass:c,duration:d,velocity:f,isResolvedFromDuration:g}=RA({...n,velocity:-$n(n.velocity||0)}),v=f||0,y=u/(2*Math.sqrt(l*c)),w=o-i,p=$n(Math.sqrt(l/c)),h=Math.abs(w)<5;r||(r=h?Re.restSpeed.granular:Re.restSpeed.default),s||(s=h?Re.restDelta.granular:Re.restDelta.default);let m;if(y<1){const C=tf(p,y);m=k=>{const P=Math.exp(-y*p*k);return o-P*((v+y*p*w)/C*Math.sin(C*k)+w*Math.cos(C*k))}}else if(y===1)m=C=>o-Math.exp(-p*C)*(w+(v+p*w)*C);else{const C=p*Math.sqrt(y*y-1);m=k=>{const P=Math.exp(-y*p*k),b=Math.min(C*k,300);return o-P*((v+y*p*w)*Math.sinh(b)+C*w*Math.cosh(b))/C}}const _={calculatedDuration:g&&d||null,next:C=>{const k=m(C);if(g)a.done=C>=d;else{let P=0;y<1&&(P=C===0?Bn(v):c1(m,C,k));const b=Math.abs(P)<=r,F=Math.abs(o-k)<=s;a.done=b&&F}return a.value=a.done?o:k,a},toString:()=>{const C=Math.min(d1(_),nf),k=l1(P=>_.next(C*P).value,C,30);return C+"ms "+k}};return _}function gg({keyframes:e,velocity:t=0,power:n=.8,timeConstant:r=325,bounceDamping:s=10,bounceStiffness:i=500,modifyTarget:o,min:a,max:l,restDelta:u=.5,restSpeed:c}){const d=e[0],f={done:!1,value:d},g=b=>a!==void 0&&b<a||l!==void 0&&b>l,v=b=>a===void 0?l:l===void 0||Math.abs(a-b)<Math.abs(l-b)?a:l;let y=n*t;const w=d+y,p=o===void 0?w:o(w);p!==w&&(y=p-d);const h=b=>-y*Math.exp(-b/r),m=b=>p+h(b),_=b=>{const F=h(b),j=m(b);f.done=Math.abs(F)<=u,f.value=f.done?p:j};let C,k;const P=b=>{g(f.value)&&(C=b,k=f1({keyframes:[f.value,v(f.value)],velocity:c1(m,b,f.value),damping:s,stiffness:i,restDelta:u,restSpeed:c}))};return P(0),{calculatedDuration:null,next:b=>{let F=!1;return!k&&C===void 0&&(F=!0,_(b),P(b)),C!==void 0&&b>=C?k.next(b-C):(!F&&_(b),f)}}}const NA=ca(.42,0,1,1),MA=ca(0,0,.58,1),h1=ca(.42,0,.58,1),DA=e=>Array.isArray(e)&&typeof e[0]!="number",Kh=e=>Array.isArray(e)&&typeof e[0]=="number",yg={linear:st,easeIn:NA,easeInOut:h1,easeOut:MA,circIn:Ih,circInOut:Ww,circOut:Uw,backIn:Fh,backInOut:Bw,backOut:zw,anticipate:$w},vg=e=>{if(Kh(e)){Qd(e.length===4);const[t,n,r,s]=e;return ca(t,n,r,s)}else if(typeof e=="string")return Qd(yg[e]!==void 0),yg[e];return e},OA=(e,t)=>n=>t(e(n)),Cr=(...e)=>e.reduce(OA),be=(e,t,n)=>e+(t-e)*n;function Oc(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+(t-e)*6*n:n<1/2?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function LA({hue:e,saturation:t,lightness:n,alpha:r}){e/=360,t/=100,n/=100;let s=0,i=0,o=0;if(!t)s=i=o=n;else{const a=n<.5?n*(1+t):n+t-n*t,l=2*n-a;s=Oc(l,a,e+1/3),i=Oc(l,a,e),o=Oc(l,a,e-1/3)}return{red:Math.round(s*255),green:Math.round(i*255),blue:Math.round(o*255),alpha:r}}function Ul(e,t){return n=>n>0?t:e}const Lc=(e,t,n)=>{const r=e*e,s=n*(t*t-r)+r;return s<0?0:Math.sqrt(s)},jA=[Jd,Qr,Vs],VA=e=>jA.find(t=>t.test(e));function xg(e){const t=VA(e);if(!t)return!1;let n=t.parse(e);return t===Vs&&(n=LA(n)),n}const wg=(e,t)=>{const n=xg(e),r=xg(t);if(!n||!r)return Ul(e,t);const s={...n};return i=>(s.red=Lc(n.red,r.red,i),s.green=Lc(n.green,r.green,i),s.blue=Lc(n.blue,r.blue,i),s.alpha=be(n.alpha,r.alpha,i),Qr.transform(s))},rf=new Set(["none","hidden"]);function FA(e,t){return rf.has(e)?n=>n<=0?e:t:n=>n>=1?t:e}function IA(e,t){return n=>be(e,t,n)}function qh(e){return typeof e=="number"?IA:typeof e=="string"?zh(e)?Ul:tt.test(e)?wg:$A:Array.isArray(e)?p1:typeof e=="object"?tt.test(e)?wg:zA:Ul}function p1(e,t){const n=[...e],r=n.length,s=e.map((i,o)=>qh(i)(i,t[o]));return i=>{for(let o=0;o<r;o++)n[o]=s[o](i);return n}}function zA(e,t){const n={...e,...t},r={};for(const s in n)e[s]!==void 0&&t[s]!==void 0&&(r[s]=qh(e[s])(e[s],t[s]));return s=>{for(const i in r)n[i]=r[i](s);return n}}function BA(e,t){var n;const r=[],s={color:0,var:0,number:0};for(let i=0;i<t.values.length;i++){const o=t.types[i],a=e.indexes[o][s[o]],l=(n=e.values[a])!==null&&n!==void 0?n:0;r[i]=l,s[o]++}return r}const $A=(e,t)=>{const n=Mr.createTransformer(t),r=Io(e),s=Io(t);return r.indexes.var.length===s.indexes.var.length&&r.indexes.color.length===s.indexes.color.length&&r.indexes.number.length>=s.indexes.number.length?rf.has(e)&&!s.values.length||rf.has(t)&&!r.values.length?FA(e,t):Cr(p1(BA(r,s),s.values),n):Ul(e,t)};function m1(e,t,n){return typeof e=="number"&&typeof t=="number"&&typeof n=="number"?be(e,t,n):qh(e)(e,t)}function UA(e,t,n){const r=[],s=n||m1,i=e.length-1;for(let o=0;o<i;o++){let a=s(e[o],e[o+1]);if(t){const l=Array.isArray(t)?t[o]||st:t;a=Cr(l,a)}r.push(a)}return r}function WA(e,t,{clamp:n=!0,ease:r,mixer:s}={}){const i=e.length;if(Qd(i===t.length),i===1)return()=>t[0];if(i===2&&e[0]===e[1])return()=>t[1];e[0]>e[i-1]&&(e=[...e].reverse(),t=[...t].reverse());const o=UA(t,r,s),a=o.length,l=u=>{let c=0;if(a>1)for(;c<e.length-2&&!(u<e[c+1]);c++);const d=vi(e[c],e[c+1],u);return o[c](d)};return n?u=>l(Gn(e[0],e[i-1],u)):l}function HA(e,t){const n=e[e.length-1];for(let r=1;r<=t;r++){const s=vi(0,t,r);e.push(be(n,1,s))}}function ZA(e){const t=[0];return HA(t,e.length-1),t}function KA(e,t){return e.map(n=>n*t)}function qA(e,t){return e.map(()=>t||h1).splice(0,e.length-1)}function Wl({duration:e=300,keyframes:t,times:n,ease:r="easeInOut"}){const s=DA(r)?r.map(vg):vg(r),i={done:!1,value:t[0]},o=KA(n&&n.length===t.length?n:ZA(t),e),a=WA(o,t,{ease:Array.isArray(s)?s:qA(t,s)});return{calculatedDuration:e,next:l=>(i.value=a(l),i.done=l>=e,i)}}const GA=e=>{const t=({timestamp:n})=>e(n);return{start:()=>ge.update(t,!0),stop:()=>Nr(t),now:()=>Ke.isProcessing?Ke.timestamp:Tn.now()}},QA={decay:gg,inertia:gg,tween:Wl,keyframes:Wl,spring:f1},YA=e=>e/100;class Gh extends a1{constructor(t){super(t),this.holdTime=null,this.cancelTime=null,this.currentTime=0,this.playbackSpeed=1,this.pendingPlayState="running",this.startTime=null,this.state="idle",this.stop=()=>{if(this.resolver.cancel(),this.isStopped=!0,this.state==="idle")return;this.teardown();const{onStop:l}=this.options;l&&l()};const{name:n,motionValue:r,element:s,keyframes:i}=this.options,o=(s==null?void 0:s.KeyframeResolver)||Bh,a=(l,u)=>this.onKeyframesResolved(l,u);this.resolver=new o(i,a,n,r,s),this.resolver.scheduleResolve()}flatten(){super.flatten(),this._resolved&&Object.assign(this._resolved,this.initPlayback(this._resolved.keyframes))}initPlayback(t){const{type:n="keyframes",repeat:r=0,repeatDelay:s=0,repeatType:i,velocity:o=0}=this.options,a=Zh(n)?n:QA[n]||Wl;let l,u;a!==Wl&&typeof t[0]!="number"&&(l=Cr(YA,m1(t[0],t[1])),t=[0,100]);const c=a({...this.options,keyframes:t});i==="mirror"&&(u=a({...this.options,keyframes:[...t].reverse(),velocity:-o})),c.calculatedDuration===null&&(c.calculatedDuration=d1(c));const{calculatedDuration:d}=c,f=d+s,g=f*(r+1)-s;return{generator:c,mirroredGenerator:u,mapPercentToKeyframes:l,calculatedDuration:d,resolvedDuration:f,totalDuration:g}}onPostResolved(){const{autoplay:t=!0}=this.options;this.play(),this.pendingPlayState==="paused"||!t?this.pause():this.state=this.pendingPlayState}tick(t,n=!1){const{resolved:r}=this;if(!r){const{keyframes:b}=this.options;return{done:!0,value:b[b.length-1]}}const{finalKeyframe:s,generator:i,mirroredGenerator:o,mapPercentToKeyframes:a,keyframes:l,calculatedDuration:u,totalDuration:c,resolvedDuration:d}=r;if(this.startTime===null)return i.next(0);const{delay:f,repeat:g,repeatType:v,repeatDelay:y,onUpdate:w}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-c/this.speed,this.startTime)),n?this.currentTime=t:this.holdTime!==null?this.currentTime=this.holdTime:this.currentTime=Math.round(t-this.startTime)*this.speed;const p=this.currentTime-f*(this.speed>=0?1:-1),h=this.speed>=0?p<0:p>c;this.currentTime=Math.max(p,0),this.state==="finished"&&this.holdTime===null&&(this.currentTime=c);let m=this.currentTime,_=i;if(g){const b=Math.min(this.currentTime,c)/d;let F=Math.floor(b),j=b%1;!j&&b>=1&&(j=1),j===1&&F--,F=Math.min(F,g+1),!!(F%2)&&(v==="reverse"?(j=1-j,y&&(j-=y/d)):v==="mirror"&&(_=o)),m=Gn(0,1,j)*d}const C=h?{done:!1,value:l[0]}:_.next(m);a&&(C.value=a(C.value));let{done:k}=C;!h&&u!==null&&(k=this.speed>=0?this.currentTime>=c:this.currentTime<=0);const P=this.holdTime===null&&(this.state==="finished"||this.state==="running"&&k);return P&&s!==void 0&&(C.value=Iu(l,this.options,s)),w&&w(C.value),P&&this.finish(),C}get duration(){const{resolved:t}=this;return t?$n(t.calculatedDuration):0}get time(){return $n(this.currentTime)}set time(t){t=Bn(t),this.currentTime=t,this.holdTime!==null||this.speed===0?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.speed)}get speed(){return this.playbackSpeed}set speed(t){const n=this.playbackSpeed!==t;this.playbackSpeed=t,n&&(this.time=$n(this.currentTime))}play(){if(this.resolver.isScheduled||this.resolver.resume(),!this._resolved){this.pendingPlayState="running";return}if(this.isStopped)return;const{driver:t=GA,onPlay:n,startTime:r}=this.options;this.driver||(this.driver=t(i=>this.tick(i))),n&&n();const s=this.driver.now();this.holdTime!==null?this.startTime=s-this.holdTime:this.startTime?this.state==="finished"&&(this.startTime=s):this.startTime=r??this.calcStartTime(),this.state==="finished"&&this.updateFinishedPromise(),this.cancelTime=this.startTime,this.holdTime=null,this.state="running",this.driver.start()}pause(){var t;if(!this._resolved){this.pendingPlayState="paused";return}this.state="paused",this.holdTime=(t=this.currentTime)!==null&&t!==void 0?t:0}complete(){this.state!=="running"&&this.play(),this.pendingPlayState=this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";const{onComplete:t}=this.options;t&&t()}cancel(){this.cancelTime!==null&&this.tick(this.cancelTime),this.teardown(),this.updateFinishedPromise()}teardown(){this.state="idle",this.stopDriver(),this.resolveFinishedPromise(),this.updateFinishedPromise(),this.startTime=this.cancelTime=null,this.resolver.cancel()}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}}const XA=new Set(["opacity","clipPath","filter","transform"]);function Qh(e){let t;return()=>(t===void 0&&(t=e()),t)}const JA={linearEasing:void 0};function eR(e,t){const n=Qh(e);return()=>{var r;return(r=JA[t])!==null&&r!==void 0?r:n()}}const Hl=eR(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch{return!1}return!0},"linearEasing");function g1(e){return!!(typeof e=="function"&&Hl()||!e||typeof e=="string"&&(e in sf||Hl())||Kh(e)||Array.isArray(e)&&e.every(g1))}const Qi=([e,t,n,r])=>`cubic-bezier(${e}, ${t}, ${n}, ${r})`,sf={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:Qi([0,.65,.55,1]),circOut:Qi([.55,0,1,.45]),backIn:Qi([.31,.01,.66,-.59]),backOut:Qi([.33,1.53,.69,.99])};function y1(e,t){if(e)return typeof e=="function"&&Hl()?l1(e,t):Kh(e)?Qi(e):Array.isArray(e)?e.map(n=>y1(n,t)||sf.easeOut):sf[e]}function tR(e,t,n,{delay:r=0,duration:s=300,repeat:i=0,repeatType:o="loop",ease:a="easeInOut",times:l}={}){const u={[t]:n};l&&(u.offset=l);const c=y1(a,s);return Array.isArray(c)&&(u.easing=c),e.animate(u,{delay:r,duration:s,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:i+1,direction:o==="reverse"?"alternate":"normal"})}function Sg(e,t){e.timeline=t,e.onfinish=null}const nR=Qh(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),Zl=10,rR=2e4;function sR(e){return Zh(e.type)||e.type==="spring"||!g1(e.ease)}function iR(e,t){const n=new Gh({...t,keyframes:e,repeat:0,delay:0,isGenerator:!0});let r={done:!1,value:e[0]};const s=[];let i=0;for(;!r.done&&i<rR;)r=n.sample(i),s.push(r.value),i+=Zl;return{times:void 0,keyframes:s,duration:i-Zl,ease:"linear"}}const v1={anticipate:$w,backInOut:Bw,circInOut:Ww};function oR(e){return e in v1}class _g extends a1{constructor(t){super(t);const{name:n,motionValue:r,element:s,keyframes:i}=this.options;this.resolver=new o1(i,(o,a)=>this.onKeyframesResolved(o,a),n,r,s),this.resolver.scheduleResolve()}initPlayback(t,n){var r;let{duration:s=300,times:i,ease:o,type:a,motionValue:l,name:u,startTime:c}=this.options;if(!(!((r=l.owner)===null||r===void 0)&&r.current))return!1;if(typeof o=="string"&&Hl()&&oR(o)&&(o=v1[o]),sR(this.options)){const{onComplete:f,onUpdate:g,motionValue:v,element:y,...w}=this.options,p=iR(t,w);t=p.keyframes,t.length===1&&(t[1]=t[0]),s=p.duration,i=p.times,o=p.ease,a="keyframes"}const d=tR(l.owner.current,u,t,{...this.options,duration:s,times:i,ease:o});return d.startTime=c??this.calcStartTime(),this.pendingTimeline?(Sg(d,this.pendingTimeline),this.pendingTimeline=void 0):d.onfinish=()=>{const{onComplete:f}=this.options;l.set(Iu(t,this.options,n)),f&&f(),this.cancel(),this.resolveFinishedPromise()},{animation:d,duration:s,times:i,type:a,ease:o,keyframes:t}}get duration(){const{resolved:t}=this;if(!t)return 0;const{duration:n}=t;return $n(n)}get time(){const{resolved:t}=this;if(!t)return 0;const{animation:n}=t;return $n(n.currentTime||0)}set time(t){const{resolved:n}=this;if(!n)return;const{animation:r}=n;r.currentTime=Bn(t)}get speed(){const{resolved:t}=this;if(!t)return 1;const{animation:n}=t;return n.playbackRate}set speed(t){const{resolved:n}=this;if(!n)return;const{animation:r}=n;r.playbackRate=t}get state(){const{resolved:t}=this;if(!t)return"idle";const{animation:n}=t;return n.playState}get startTime(){const{resolved:t}=this;if(!t)return null;const{animation:n}=t;return n.startTime}attachTimeline(t){if(!this._resolved)this.pendingTimeline=t;else{const{resolved:n}=this;if(!n)return st;const{animation:r}=n;Sg(r,t)}return st}play(){if(this.isStopped)return;const{resolved:t}=this;if(!t)return;const{animation:n}=t;n.playState==="finished"&&this.updateFinishedPromise(),n.play()}pause(){const{resolved:t}=this;if(!t)return;const{animation:n}=t;n.pause()}stop(){if(this.resolver.cancel(),this.isStopped=!0,this.state==="idle")return;this.resolveFinishedPromise(),this.updateFinishedPromise();const{resolved:t}=this;if(!t)return;const{animation:n,keyframes:r,duration:s,type:i,ease:o,times:a}=t;if(n.playState==="idle"||n.playState==="finished")return;if(this.time){const{motionValue:u,onUpdate:c,onComplete:d,element:f,...g}=this.options,v=new Gh({...g,keyframes:r,duration:s,type:i,ease:o,times:a,isGenerator:!0}),y=Bn(this.time);u.setWithVelocity(v.sample(y-Zl).value,v.sample(y).value,Zl)}const{onStop:l}=this.options;l&&l(),this.cancel()}complete(){const{resolved:t}=this;t&&t.animation.finish()}cancel(){const{resolved:t}=this;t&&t.animation.cancel()}static supports(t){const{motionValue:n,name:r,repeatDelay:s,repeatType:i,damping:o,type:a}=t;return nR()&&r&&XA.has(r)&&n&&n.owner&&n.owner.current instanceof HTMLElement&&!n.owner.getProps().onUpdate&&!s&&i!=="mirror"&&o!==0&&a!=="inertia"}}const aR=Qh(()=>window.ScrollTimeline!==void 0);class lR{constructor(t){this.stop=()=>this.runAll("stop"),this.animations=t.filter(Boolean)}then(t,n){return Promise.all(this.animations).then(t).catch(n)}getAll(t){return this.animations[0][t]}setAll(t,n){for(let r=0;r<this.animations.length;r++)this.animations[r][t]=n}attachTimeline(t,n){const r=this.animations.map(s=>aR()&&s.attachTimeline?s.attachTimeline(t):n(s));return()=>{r.forEach((s,i)=>{s&&s(),this.animations[i].stop()})}}get time(){return this.getAll("time")}set time(t){this.setAll("time",t)}get speed(){return this.getAll("speed")}set speed(t){this.setAll("speed",t)}get startTime(){return this.getAll("startTime")}get duration(){let t=0;for(let n=0;n<this.animations.length;n++)t=Math.max(t,this.animations[n].duration);return t}runAll(t){this.animations.forEach(n=>n[t]())}flatten(){this.runAll("flatten")}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}function uR({when:e,delay:t,delayChildren:n,staggerChildren:r,staggerDirection:s,repeat:i,repeatType:o,repeatDelay:a,from:l,elapsed:u,...c}){return!!Object.keys(c).length}const Yh=(e,t,n,r={},s,i)=>o=>{const a=Vh(r,e)||{},l=a.delay||r.delay||0;let{elapsed:u=0}=r;u=u-Bn(l);let c={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:t.getVelocity(),...a,delay:-u,onUpdate:f=>{t.set(f),a.onUpdate&&a.onUpdate(f)},onComplete:()=>{o(),a.onComplete&&a.onComplete()},name:e,motionValue:t,element:i?void 0:s};uR(a)||(c={...c,...OP(e,c)}),c.duration&&(c.duration=Bn(c.duration)),c.repeatDelay&&(c.repeatDelay=Bn(c.repeatDelay)),c.from!==void 0&&(c.keyframes[0]=c.from);let d=!1;if((c.type===!1||c.duration===0&&!c.repeatDelay)&&(c.duration=0,c.delay===0&&(d=!0)),d&&!i&&t.get()!==void 0){const f=Iu(c.keyframes,a);if(f!==void 0)return ge.update(()=>{c.onUpdate(f),c.onComplete()}),new lR([])}return!i&&_g.supports(c)?new _g(c):new Gh(c)},cR=e=>!!(e&&typeof e=="object"&&e.mix&&e.toValue),dR=e=>Gd(e)?e[e.length-1]||0:e;function Xh(e,t){e.indexOf(t)===-1&&e.push(t)}function Jh(e,t){const n=e.indexOf(t);n>-1&&e.splice(n,1)}class ep{constructor(){this.subscriptions=[]}add(t){return Xh(this.subscriptions,t),()=>Jh(this.subscriptions,t)}notify(t,n,r){const s=this.subscriptions.length;if(s)if(s===1)this.subscriptions[0](t,n,r);else for(let i=0;i<s;i++){const o=this.subscriptions[i];o&&o(t,n,r)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const Tg=30,fR=e=>!isNaN(parseFloat(e));class hR{constructor(t,n={}){this.version="11.13.1",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(r,s=!0)=>{const i=Tn.now();this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(r),this.current!==this.prev&&this.events.change&&this.events.change.notify(this.current),s&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=n.owner}setCurrent(t){this.current=t,this.updatedAt=Tn.now(),this.canTrackVelocity===null&&t!==void 0&&(this.canTrackVelocity=fR(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,n){this.events[t]||(this.events[t]=new ep);const r=this.events[t].add(n);return t==="change"?()=>{r(),ge.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,n){this.passiveEffect=t,this.stopPassiveEffect=n}set(t,n=!0){!n||!this.passiveEffect?this.updateAndNotify(t,n):this.passiveEffect(t,this.updateAndNotify)}setWithVelocity(t,n,r){this.set(n),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-r}jump(t,n=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,n&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const t=Tn.now();if(!this.canTrackVelocity||this.prevFrameValue===void 0||t-this.updatedAt>Tg)return 0;const n=Math.min(this.updatedAt-this.prevUpdatedAt,Tg);return u1(parseFloat(this.current)-parseFloat(this.prevFrameValue),n)}start(t){return this.stop(),new Promise(n=>{this.hasAnimated=!0,this.animation=t(n),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function zo(e,t){return new hR(e,t)}function pR(e,t,n){e.hasValue(t)?e.getValue(t).set(n):e.addValue(t,zo(n))}function mR(e,t){const n=Fu(e,t);let{transitionEnd:r={},transition:s={},...i}=n||{};i={...i,...r};for(const o in i){const a=dR(i[o]);pR(e,o,a)}}const tp=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),gR="framerAppearId",x1="data-"+tp(gR);function w1(e){return e.props[x1]}const rt=e=>!!(e&&e.getVelocity);function yR(e){return!!(rt(e)&&e.add)}function of(e,t){const n=e.getValue("willChange");if(yR(n))return n.add(t)}function vR({protectedKeys:e,needsAnimating:t},n){const r=e.hasOwnProperty(n)&&t[n]!==!0;return t[n]=!1,r}function S1(e,t,{delay:n=0,transitionOverride:r,type:s}={}){var i;let{transition:o=e.getDefaultTransition(),transitionEnd:a,...l}=t;r&&(o=r);const u=[],c=s&&e.animationState&&e.animationState.getState()[s];for(const d in l){const f=e.getValue(d,(i=e.latestValues[d])!==null&&i!==void 0?i:null),g=l[d];if(g===void 0||c&&vR(c,d))continue;const v={delay:n,...Vh(o||{},d)};let y=!1;if(window.MotionHandoffAnimation){const p=w1(e);if(p){const h=window.MotionHandoffAnimation(p,d,ge);h!==null&&(v.startTime=h,y=!0)}}of(e,d),f.start(Yh(d,f,g,e.shouldReduceMotion&&vs.has(d)?{type:!1}:v,e,y));const w=f.animation;w&&u.push(w)}return a&&Promise.all(u).then(()=>{ge.update(()=>{a&&mR(e,a)})}),u}function af(e,t,n={}){var r;const s=Fu(e,t,n.type==="exit"?(r=e.presenceContext)===null||r===void 0?void 0:r.custom:void 0);let{transition:i=e.getDefaultTransition()||{}}=s||{};n.transitionOverride&&(i=n.transitionOverride);const o=s?()=>Promise.all(S1(e,s,n)):()=>Promise.resolve(),a=e.variantChildren&&e.variantChildren.size?(u=0)=>{const{delayChildren:c=0,staggerChildren:d,staggerDirection:f}=i;return xR(e,t,c+u,d,f,n)}:()=>Promise.resolve(),{when:l}=i;if(l){const[u,c]=l==="beforeChildren"?[o,a]:[a,o];return u().then(()=>c())}else return Promise.all([o(),a(n.delay)])}function xR(e,t,n=0,r=0,s=1,i){const o=[],a=(e.variantChildren.size-1)*r,l=s===1?(u=0)=>u*r:(u=0)=>a-u*r;return Array.from(e.variantChildren).sort(wR).forEach((u,c)=>{u.notify("AnimationStart",t),o.push(af(u,t,{...i,delay:n+l(c)}).then(()=>u.notify("AnimationComplete",t)))}),Promise.all(o)}function wR(e,t){return e.sortNodePosition(t)}function SR(e,t,n={}){e.notify("AnimationStart",t);let r;if(Array.isArray(t)){const s=t.map(i=>af(e,i,n));r=Promise.all(s)}else if(typeof t=="string")r=af(e,t,n);else{const s=typeof t=="function"?Fu(e,t,n.custom):t;r=Promise.all(S1(e,s,n))}return r.then(()=>{e.notify("AnimationComplete",t)})}const _R=jh.length;function _1(e){if(!e)return;if(!e.isControllingVariants){const n=e.parent?_1(e.parent)||{}:{};return e.props.initial!==void 0&&(n.initial=e.props.initial),n}const t={};for(let n=0;n<_R;n++){const r=jh[n],s=e.props[r];(Vo(s)||s===!1)&&(t[r]=s)}return t}const TR=[...Lh].reverse(),CR=Lh.length;function kR(e){return t=>Promise.all(t.map(({animation:n,options:r})=>SR(e,n,r)))}function bR(e){let t=kR(e),n=Cg(),r=!0;const s=l=>(u,c)=>{var d;const f=Fu(e,c,l==="exit"?(d=e.presenceContext)===null||d===void 0?void 0:d.custom:void 0);if(f){const{transition:g,transitionEnd:v,...y}=f;u={...u,...y,...v}}return u};function i(l){t=l(e)}function o(l){const{props:u}=e,c=_1(e.parent)||{},d=[],f=new Set;let g={},v=1/0;for(let w=0;w<CR;w++){const p=TR[w],h=n[p],m=u[p]!==void 0?u[p]:c[p],_=Vo(m),C=p===l?h.isActive:null;C===!1&&(v=w);let k=m===c[p]&&m!==u[p]&&_;if(k&&r&&e.manuallyAnimateOnMount&&(k=!1),h.protectedKeys={...g},!h.isActive&&C===null||!m&&!h.prevProp||Vu(m)||typeof m=="boolean")continue;const P=ER(h.prevProp,m);let b=P||p===l&&h.isActive&&!k&&_||w>v&&_,F=!1;const j=Array.isArray(m)?m:[m];let Y=j.reduce(s(p),{});C===!1&&(Y={});const{prevResolvedValues:L={}}=h,U={...L,...Y},O=Q=>{b=!0,f.has(Q)&&(F=!0,f.delete(Q)),h.needsAnimating[Q]=!0;const A=e.getValue(Q);A&&(A.liveStyle=!1)};for(const Q in U){const A=Y[Q],I=L[Q];if(g.hasOwnProperty(Q))continue;let K=!1;Gd(A)&&Gd(I)?K=!Lw(A,I):K=A!==I,K?A!=null?O(Q):f.add(Q):A!==void 0&&f.has(Q)?O(Q):h.protectedKeys[Q]=!0}h.prevProp=m,h.prevResolvedValues=Y,h.isActive&&(g={...g,...Y}),r&&e.blockInitialAnimation&&(b=!1),b&&(!(k&&P)||F)&&d.push(...j.map(Q=>({animation:Q,options:{type:p}})))}if(f.size){const w={};f.forEach(p=>{const h=e.getBaseTarget(p),m=e.getValue(p);m&&(m.liveStyle=!0),w[p]=h??null}),d.push({animation:w})}let y=!!d.length;return r&&(u.initial===!1||u.initial===u.animate)&&!e.manuallyAnimateOnMount&&(y=!1),r=!1,y?t(d):Promise.resolve()}function a(l,u){var c;if(n[l].isActive===u)return Promise.resolve();(c=e.variantChildren)===null||c===void 0||c.forEach(f=>{var g;return(g=f.animationState)===null||g===void 0?void 0:g.setActive(l,u)}),n[l].isActive=u;const d=o(l);for(const f in n)n[f].protectedKeys={};return d}return{animateChanges:o,setActive:a,setAnimateFunction:i,getState:()=>n,reset:()=>{n=Cg(),r=!0}}}function ER(e,t){return typeof t=="string"?t!==e:Array.isArray(t)?!Lw(t,e):!1}function $r(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function Cg(){return{animate:$r(!0),whileInView:$r(),whileHover:$r(),whileTap:$r(),whileDrag:$r(),whileFocus:$r(),exit:$r()}}class zr{constructor(t){this.isMounted=!1,this.node=t}update(){}}class PR extends zr{constructor(t){super(t),t.animationState||(t.animationState=bR(t))}updateAnimationControlsSubscription(){const{animate:t}=this.node.getProps();Vu(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:t}=this.node.getProps(),{animate:n}=this.node.prevProps||{};t!==n&&this.updateAnimationControlsSubscription()}unmount(){var t;this.node.animationState.reset(),(t=this.unmountControls)===null||t===void 0||t.call(this)}}let AR=0;class RR extends zr{constructor(){super(...arguments),this.id=AR++}update(){if(!this.node.presenceContext)return;const{isPresent:t,onExitComplete:n}=this.node.presenceContext,{isPresent:r}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===r)return;const s=this.node.animationState.setActive("exit",!t);n&&!t&&s.then(()=>n(this.id))}mount(){const{register:t}=this.node.presenceContext||{};t&&(this.unmount=t(this.id))}unmount(){}}const NR={animation:{Feature:PR},exit:{Feature:RR}};function MR(e,t,n){var r;if(e instanceof Element)return[e];if(typeof e=="string"){let s=document;const i=(r=void 0)!==null&&r!==void 0?r:s.querySelectorAll(e);return i?Array.from(i):[]}return Array.from(e)}const Kt={x:!1,y:!1};function T1(){return Kt.x||Kt.y}function kg(e){return t=>{t.pointerType==="touch"||T1()||e(t)}}function DR(e,t,n={}){const r=new AbortController,s={passive:!0,...n,signal:r.signal},i=kg(o=>{const{target:a}=o,l=t(o);if(!l||!a)return;const u=kg(c=>{l(c),a.removeEventListener("pointerleave",u)});a.addEventListener("pointerleave",u,s)});return MR(e).forEach(o=>{o.addEventListener("pointerenter",i,s)}),()=>r.abort()}function OR(e){return e==="x"||e==="y"?Kt[e]?null:(Kt[e]=!0,()=>{Kt[e]=!1}):Kt.x||Kt.y?null:(Kt.x=Kt.y=!0,()=>{Kt.x=Kt.y=!1})}const C1=e=>e.pointerType==="mouse"?typeof e.button!="number"||e.button<=0:e.isPrimary!==!1;function fa(e){return{point:{x:e.pageX,y:e.pageY}}}const LR=e=>t=>C1(t)&&e(t,fa(t));function In(e,t,n,r={passive:!0}){return e.addEventListener(t,n,r),()=>e.removeEventListener(t,n)}function kr(e,t,n,r){return In(e,t,LR(n),r)}const bg=(e,t)=>Math.abs(e-t);function jR(e,t){const n=bg(e.x,t.x),r=bg(e.y,t.y);return Math.sqrt(n**2+r**2)}class k1{constructor(t,n,{transformPagePoint:r,contextWindow:s,dragSnapToOrigin:i=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const d=Vc(this.lastMoveEventInfo,this.history),f=this.startEvent!==null,g=jR(d.offset,{x:0,y:0})>=3;if(!f&&!g)return;const{point:v}=d,{timestamp:y}=Ke;this.history.push({...v,timestamp:y});const{onStart:w,onMove:p}=this.handlers;f||(w&&w(this.lastMoveEvent,d),this.startEvent=this.lastMoveEvent),p&&p(this.lastMoveEvent,d)},this.handlePointerMove=(d,f)=>{this.lastMoveEvent=d,this.lastMoveEventInfo=jc(f,this.transformPagePoint),ge.update(this.updatePoint,!0)},this.handlePointerUp=(d,f)=>{this.end();const{onEnd:g,onSessionEnd:v,resumeAnimation:y}=this.handlers;if(this.dragSnapToOrigin&&y&&y(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const w=Vc(d.type==="pointercancel"?this.lastMoveEventInfo:jc(f,this.transformPagePoint),this.history);this.startEvent&&g&&g(d,w),v&&v(d,w)},!C1(t))return;this.dragSnapToOrigin=i,this.handlers=n,this.transformPagePoint=r,this.contextWindow=s||window;const o=fa(t),a=jc(o,this.transformPagePoint),{point:l}=a,{timestamp:u}=Ke;this.history=[{...l,timestamp:u}];const{onSessionStart:c}=n;c&&c(t,Vc(a,this.history)),this.removeListeners=Cr(kr(this.contextWindow,"pointermove",this.handlePointerMove),kr(this.contextWindow,"pointerup",this.handlePointerUp),kr(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),Nr(this.updatePoint)}}function jc(e,t){return t?{point:t(e.point)}:e}function Eg(e,t){return{x:e.x-t.x,y:e.y-t.y}}function Vc({point:e},t){return{point:e,delta:Eg(e,b1(t)),offset:Eg(e,VR(t)),velocity:FR(t,.1)}}function VR(e){return e[0]}function b1(e){return e[e.length-1]}function FR(e,t){if(e.length<2)return{x:0,y:0};let n=e.length-1,r=null;const s=b1(e);for(;n>=0&&(r=e[n],!(s.timestamp-r.timestamp>Bn(t)));)n--;if(!r)return{x:0,y:0};const i=$n(s.timestamp-r.timestamp);if(i===0)return{x:0,y:0};const o={x:(s.x-r.x)/i,y:(s.y-r.y)/i};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}function Fs(e){return e&&typeof e=="object"&&Object.prototype.hasOwnProperty.call(e,"current")}const E1=1e-4,IR=1-E1,zR=1+E1,P1=.01,BR=0-P1,$R=0+P1;function Dt(e){return e.max-e.min}function UR(e,t,n){return Math.abs(e-t)<=n}function Pg(e,t,n,r=.5){e.origin=r,e.originPoint=be(t.min,t.max,e.origin),e.scale=Dt(n)/Dt(t),e.translate=be(n.min,n.max,e.origin)-e.originPoint,(e.scale>=IR&&e.scale<=zR||isNaN(e.scale))&&(e.scale=1),(e.translate>=BR&&e.translate<=$R||isNaN(e.translate))&&(e.translate=0)}function fo(e,t,n,r){Pg(e.x,t.x,n.x,r?r.originX:void 0),Pg(e.y,t.y,n.y,r?r.originY:void 0)}function Ag(e,t,n){e.min=n.min+t.min,e.max=e.min+Dt(t)}function WR(e,t,n){Ag(e.x,t.x,n.x),Ag(e.y,t.y,n.y)}function Rg(e,t,n){e.min=t.min-n.min,e.max=e.min+Dt(t)}function ho(e,t,n){Rg(e.x,t.x,n.x),Rg(e.y,t.y,n.y)}function HR(e,{min:t,max:n},r){return t!==void 0&&e<t?e=r?be(t,e,r.min):Math.max(e,t):n!==void 0&&e>n&&(e=r?be(n,e,r.max):Math.min(e,n)),e}function Ng(e,t,n){return{min:t!==void 0?e.min+t:void 0,max:n!==void 0?e.max+n-(e.max-e.min):void 0}}function ZR(e,{top:t,left:n,bottom:r,right:s}){return{x:Ng(e.x,n,s),y:Ng(e.y,t,r)}}function Mg(e,t){let n=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([n,r]=[r,n]),{min:n,max:r}}function KR(e,t){return{x:Mg(e.x,t.x),y:Mg(e.y,t.y)}}function qR(e,t){let n=.5;const r=Dt(e),s=Dt(t);return s>r?n=vi(t.min,t.max-r,e.min):r>s&&(n=vi(e.min,e.max-s,t.min)),Gn(0,1,n)}function GR(e,t){const n={};return t.min!==void 0&&(n.min=t.min-e.min),t.max!==void 0&&(n.max=t.max-e.min),n}const lf=.35;function QR(e=lf){return e===!1?e=0:e===!0&&(e=lf),{x:Dg(e,"left","right"),y:Dg(e,"top","bottom")}}function Dg(e,t,n){return{min:Og(e,t),max:Og(e,n)}}function Og(e,t){return typeof e=="number"?e:e[t]||0}const Lg=()=>({translate:0,scale:1,origin:0,originPoint:0}),Is=()=>({x:Lg(),y:Lg()}),jg=()=>({min:0,max:0}),Me=()=>({x:jg(),y:jg()});function Vt(e){return[e("x"),e("y")]}function A1({top:e,left:t,right:n,bottom:r}){return{x:{min:t,max:n},y:{min:e,max:r}}}function YR({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}function XR(e,t){if(!t)return e;const n=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:n.y,left:n.x,bottom:r.y,right:r.x}}function Fc(e){return e===void 0||e===1}function uf({scale:e,scaleX:t,scaleY:n}){return!Fc(e)||!Fc(t)||!Fc(n)}function Hr(e){return uf(e)||R1(e)||e.z||e.rotate||e.rotateX||e.rotateY||e.skewX||e.skewY}function R1(e){return Vg(e.x)||Vg(e.y)}function Vg(e){return e&&e!=="0%"}function Kl(e,t,n){const r=e-n,s=t*r;return n+s}function Fg(e,t,n,r,s){return s!==void 0&&(e=Kl(e,s,r)),Kl(e,n,r)+t}function cf(e,t=0,n=1,r,s){e.min=Fg(e.min,t,n,r,s),e.max=Fg(e.max,t,n,r,s)}function N1(e,{x:t,y:n}){cf(e.x,t.translate,t.scale,t.originPoint),cf(e.y,n.translate,n.scale,n.originPoint)}const Ig=.999999999999,zg=1.0000000000001;function JR(e,t,n,r=!1){const s=n.length;if(!s)return;t.x=t.y=1;let i,o;for(let a=0;a<s;a++){i=n[a],o=i.projectionDelta;const{visualElement:l}=i.options;l&&l.props.style&&l.props.style.display==="contents"||(r&&i.options.layoutScroll&&i.scroll&&i!==i.root&&Bs(e,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),o&&(t.x*=o.x.scale,t.y*=o.y.scale,N1(e,o)),r&&Hr(i.latestValues)&&Bs(e,i.latestValues))}t.x<zg&&t.x>Ig&&(t.x=1),t.y<zg&&t.y>Ig&&(t.y=1)}function zs(e,t){e.min=e.min+t,e.max=e.max+t}function Bg(e,t,n,r,s=.5){const i=be(e.min,e.max,s);cf(e,t,n,i,r)}function Bs(e,t){Bg(e.x,t.x,t.scaleX,t.scale,t.originX),Bg(e.y,t.y,t.scaleY,t.scale,t.originY)}function M1(e,t){return A1(XR(e.getBoundingClientRect(),t))}function eN(e,t,n){const r=M1(e,n),{scroll:s}=t;return s&&(zs(r.x,s.offset.x),zs(r.y,s.offset.y)),r}const D1=({current:e})=>e?e.ownerDocument.defaultView:null,tN=new WeakMap;class nN{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=Me(),this.visualElement=t}start(t,{snapToCursor:n=!1}={}){const{presenceContext:r}=this.visualElement;if(r&&r.isPresent===!1)return;const s=c=>{const{dragSnapToOrigin:d}=this.getProps();d?this.pauseAnimation():this.stopAnimation(),n&&this.snapToCursor(fa(c).point)},i=(c,d)=>{const{drag:f,dragPropagation:g,onDragStart:v}=this.getProps();if(f&&!g&&(this.openDragLock&&this.openDragLock(),this.openDragLock=OR(f),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),Vt(w=>{let p=this.getAxisMotionValue(w).get()||0;if(_n.test(p)){const{projection:h}=this.visualElement;if(h&&h.layout){const m=h.layout.layoutBox[w];m&&(p=Dt(m)*(parseFloat(p)/100))}}this.originPoint[w]=p}),v&&ge.postRender(()=>v(c,d)),of(this.visualElement,"transform");const{animationState:y}=this.visualElement;y&&y.setActive("whileDrag",!0)},o=(c,d)=>{const{dragPropagation:f,dragDirectionLock:g,onDirectionLock:v,onDrag:y}=this.getProps();if(!f&&!this.openDragLock)return;const{offset:w}=d;if(g&&this.currentDirection===null){this.currentDirection=rN(w),this.currentDirection!==null&&v&&v(this.currentDirection);return}this.updateAxis("x",d.point,w),this.updateAxis("y",d.point,w),this.visualElement.render(),y&&y(c,d)},a=(c,d)=>this.stop(c,d),l=()=>Vt(c=>{var d;return this.getAnimationState(c)==="paused"&&((d=this.getAxisMotionValue(c).animation)===null||d===void 0?void 0:d.play())}),{dragSnapToOrigin:u}=this.getProps();this.panSession=new k1(t,{onSessionStart:s,onStart:i,onMove:o,onSessionEnd:a,resumeAnimation:l},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:u,contextWindow:D1(this.visualElement)})}stop(t,n){const r=this.isDragging;if(this.cancel(),!r)return;const{velocity:s}=n;this.startAnimation(s);const{onDragEnd:i}=this.getProps();i&&ge.postRender(()=>i(t,n))}cancel(){this.isDragging=!1;const{projection:t,animationState:n}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:r}=this.getProps();!r&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),n&&n.setActive("whileDrag",!1)}updateAxis(t,n,r){const{drag:s}=this.getProps();if(!r||!$a(t,s,this.currentDirection))return;const i=this.getAxisMotionValue(t);let o=this.originPoint[t]+r[t];this.constraints&&this.constraints[t]&&(o=HR(o,this.constraints[t],this.elastic[t])),i.set(o)}resolveConstraints(){var t;const{dragConstraints:n,dragElastic:r}=this.getProps(),s=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(t=this.visualElement.projection)===null||t===void 0?void 0:t.layout,i=this.constraints;n&&Fs(n)?this.constraints||(this.constraints=this.resolveRefConstraints()):n&&s?this.constraints=ZR(s.layoutBox,n):this.constraints=!1,this.elastic=QR(r),i!==this.constraints&&s&&this.constraints&&!this.hasMutatedConstraints&&Vt(o=>{this.constraints!==!1&&this.getAxisMotionValue(o)&&(this.constraints[o]=GR(s.layoutBox[o],this.constraints[o]))})}resolveRefConstraints(){const{dragConstraints:t,onMeasureDragConstraints:n}=this.getProps();if(!t||!Fs(t))return!1;const r=t.current,{projection:s}=this.visualElement;if(!s||!s.layout)return!1;const i=eN(r,s.root,this.visualElement.getTransformPagePoint());let o=KR(s.layout.layoutBox,i);if(n){const a=n(YR(o));this.hasMutatedConstraints=!!a,a&&(o=A1(a))}return o}startAnimation(t){const{drag:n,dragMomentum:r,dragElastic:s,dragTransition:i,dragSnapToOrigin:o,onDragTransitionEnd:a}=this.getProps(),l=this.constraints||{},u=Vt(c=>{if(!$a(c,n,this.currentDirection))return;let d=l&&l[c]||{};o&&(d={min:0,max:0});const f=s?200:1e6,g=s?40:1e7,v={type:"inertia",velocity:r?t[c]:0,bounceStiffness:f,bounceDamping:g,timeConstant:750,restDelta:1,restSpeed:10,...i,...d};return this.startAxisValueAnimation(c,v)});return Promise.all(u).then(a)}startAxisValueAnimation(t,n){const r=this.getAxisMotionValue(t);return of(this.visualElement,t),r.start(Yh(t,r,0,n,this.visualElement,!1))}stopAnimation(){Vt(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){Vt(t=>{var n;return(n=this.getAxisMotionValue(t).animation)===null||n===void 0?void 0:n.pause()})}getAnimationState(t){var n;return(n=this.getAxisMotionValue(t).animation)===null||n===void 0?void 0:n.state}getAxisMotionValue(t){const n=`_drag${t.toUpperCase()}`,r=this.visualElement.getProps(),s=r[n];return s||this.visualElement.getValue(t,(r.initial?r.initial[t]:void 0)||0)}snapToCursor(t){Vt(n=>{const{drag:r}=this.getProps();if(!$a(n,r,this.currentDirection))return;const{projection:s}=this.visualElement,i=this.getAxisMotionValue(n);if(s&&s.layout){const{min:o,max:a}=s.layout.layoutBox[n];i.set(t[n]-be(o,a,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:t,dragConstraints:n}=this.getProps(),{projection:r}=this.visualElement;if(!Fs(n)||!r||!this.constraints)return;this.stopAnimation();const s={x:0,y:0};Vt(o=>{const a=this.getAxisMotionValue(o);if(a&&this.constraints!==!1){const l=a.get();s[o]=qR({min:l,max:l},this.constraints[o])}});const{transformTemplate:i}=this.visualElement.getProps();this.visualElement.current.style.transform=i?i({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),Vt(o=>{if(!$a(o,t,null))return;const a=this.getAxisMotionValue(o),{min:l,max:u}=this.constraints[o];a.set(be(l,u,s[o]))})}addListeners(){if(!this.visualElement.current)return;tN.set(this.visualElement,this);const t=this.visualElement.current,n=kr(t,"pointerdown",l=>{const{drag:u,dragListener:c=!0}=this.getProps();u&&c&&this.start(l)}),r=()=>{const{dragConstraints:l}=this.getProps();Fs(l)&&l.current&&(this.constraints=this.resolveRefConstraints())},{projection:s}=this.visualElement,i=s.addEventListener("measure",r);s&&!s.layout&&(s.root&&s.root.updateScroll(),s.updateLayout()),ge.read(r);const o=In(window,"resize",()=>this.scalePositionWithinConstraints()),a=s.addEventListener("didUpdate",({delta:l,hasLayoutChanged:u})=>{this.isDragging&&u&&(Vt(c=>{const d=this.getAxisMotionValue(c);d&&(this.originPoint[c]+=l[c].translate,d.set(d.get()+l[c].translate))}),this.visualElement.render())});return()=>{o(),n(),i(),a&&a()}}getProps(){const t=this.visualElement.getProps(),{drag:n=!1,dragDirectionLock:r=!1,dragPropagation:s=!1,dragConstraints:i=!1,dragElastic:o=lf,dragMomentum:a=!0}=t;return{...t,drag:n,dragDirectionLock:r,dragPropagation:s,dragConstraints:i,dragElastic:o,dragMomentum:a}}}function $a(e,t,n){return(t===!0||t===e)&&(n===null||n===e)}function rN(e,t=10){let n=null;return Math.abs(e.y)>t?n="y":Math.abs(e.x)>t&&(n="x"),n}class sN extends zr{constructor(t){super(t),this.removeGroupControls=st,this.removeListeners=st,this.controls=new nN(t)}mount(){const{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||st}unmount(){this.removeGroupControls(),this.removeListeners()}}const $g=e=>(t,n)=>{e&&ge.postRender(()=>e(t,n))};class iN extends zr{constructor(){super(...arguments),this.removePointerDownListener=st}onPointerDown(t){this.session=new k1(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:D1(this.node)})}createPanHandlers(){const{onPanSessionStart:t,onPanStart:n,onPan:r,onPanEnd:s}=this.node.getProps();return{onSessionStart:$g(t),onStart:$g(n),onMove:r,onEnd:(i,o)=>{delete this.session,s&&ge.postRender(()=>s(i,o))}}}mount(){this.removePointerDownListener=kr(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}const np=x.createContext(null);function oN(){const e=x.useContext(np);if(e===null)return[!0,null];const{isPresent:t,onExitComplete:n,register:r}=e,s=x.useId();x.useEffect(()=>r(s),[]);const i=x.useCallback(()=>n&&n(s),[s,n]);return!t&&n?[!1,i]:[!0]}const O1=x.createContext({}),L1=x.createContext({}),ol={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function Ug(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}const Ui={correct:(e,t)=>{if(!t.target)return e;if(typeof e=="string")if(J.test(e))e=parseFloat(e);else return e;const n=Ug(e,t.target.x),r=Ug(e,t.target.y);return`${n}% ${r}%`}},aN={correct:(e,{treeScale:t,projectionDelta:n})=>{const r=e,s=Mr.parse(e);if(s.length>5)return r;const i=Mr.createTransformer(e),o=typeof s[0]!="number"?1:0,a=n.x.scale*t.x,l=n.y.scale*t.y;s[0+o]/=a,s[1+o]/=l;const u=be(a,l,.5);return typeof s[2+o]=="number"&&(s[2+o]/=u),typeof s[3+o]=="number"&&(s[3+o]/=u),i(s)}},ql={};function lN(e){Object.assign(ql,e)}const{schedule:rp,cancel:eO}=jw(queueMicrotask,!1);class uN extends x.Component{componentDidMount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r,layoutId:s}=this.props,{projection:i}=t;lN(cN),i&&(n.group&&n.group.add(i),r&&r.register&&s&&r.register(i),i.root.didUpdate(),i.addEventListener("animationComplete",()=>{this.safeToRemove()}),i.setOptions({...i.options,onExitComplete:()=>this.safeToRemove()})),ol.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){const{layoutDependency:n,visualElement:r,drag:s,isPresent:i}=this.props,o=r.projection;return o&&(o.isPresent=i,s||t.layoutDependency!==n||n===void 0?o.willUpdate():this.safeToRemove(),t.isPresent!==i&&(i?o.promote():o.relegate()||ge.postRender(()=>{const a=o.getStack();(!a||!a.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),rp.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r}=this.props,{projection:s}=t;s&&(s.scheduleCheckAfterUnmount(),n&&n.group&&n.group.remove(s),r&&r.deregister&&r.deregister(s))}safeToRemove(){const{safeToRemove:t}=this.props;t&&t()}render(){return null}}function j1(e){const[t,n]=oN(),r=x.useContext(O1);return S.jsx(uN,{...e,layoutGroup:r,switchLayoutGroup:x.useContext(L1),isPresent:t,safeToRemove:n})}const cN={borderRadius:{...Ui,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:Ui,borderTopRightRadius:Ui,borderBottomLeftRadius:Ui,borderBottomRightRadius:Ui,boxShadow:aN},V1=["TopLeft","TopRight","BottomLeft","BottomRight"],dN=V1.length,Wg=e=>typeof e=="string"?parseFloat(e):e,Hg=e=>typeof e=="number"||J.test(e);function fN(e,t,n,r,s,i){s?(e.opacity=be(0,n.opacity!==void 0?n.opacity:1,hN(r)),e.opacityExit=be(t.opacity!==void 0?t.opacity:1,0,pN(r))):i&&(e.opacity=be(t.opacity!==void 0?t.opacity:1,n.opacity!==void 0?n.opacity:1,r));for(let o=0;o<dN;o++){const a=`border${V1[o]}Radius`;let l=Zg(t,a),u=Zg(n,a);if(l===void 0&&u===void 0)continue;l||(l=0),u||(u=0),l===0||u===0||Hg(l)===Hg(u)?(e[a]=Math.max(be(Wg(l),Wg(u),r),0),(_n.test(u)||_n.test(l))&&(e[a]+="%")):e[a]=u}(t.rotate||n.rotate)&&(e.rotate=be(t.rotate||0,n.rotate||0,r))}function Zg(e,t){return e[t]!==void 0?e[t]:e.borderRadius}const hN=F1(0,.5,Uw),pN=F1(.5,.95,st);function F1(e,t,n){return r=>r<e?0:r>t?1:n(vi(e,t,r))}function Kg(e,t){e.min=t.min,e.max=t.max}function jt(e,t){Kg(e.x,t.x),Kg(e.y,t.y)}function qg(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}function Gg(e,t,n,r,s){return e-=t,e=Kl(e,1/n,r),s!==void 0&&(e=Kl(e,1/s,r)),e}function mN(e,t=0,n=1,r=.5,s,i=e,o=e){if(_n.test(t)&&(t=parseFloat(t),t=be(o.min,o.max,t/100)-o.min),typeof t!="number")return;let a=be(i.min,i.max,r);e===i&&(a-=t),e.min=Gg(e.min,t,n,a,s),e.max=Gg(e.max,t,n,a,s)}function Qg(e,t,[n,r,s],i,o){mN(e,t[n],t[r],t[s],t.scale,i,o)}const gN=["x","scaleX","originX"],yN=["y","scaleY","originY"];function Yg(e,t,n,r){Qg(e.x,t,gN,n?n.x:void 0,r?r.x:void 0),Qg(e.y,t,yN,n?n.y:void 0,r?r.y:void 0)}function Xg(e){return e.translate===0&&e.scale===1}function I1(e){return Xg(e.x)&&Xg(e.y)}function Jg(e,t){return e.min===t.min&&e.max===t.max}function vN(e,t){return Jg(e.x,t.x)&&Jg(e.y,t.y)}function ey(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function z1(e,t){return ey(e.x,t.x)&&ey(e.y,t.y)}function ty(e){return Dt(e.x)/Dt(e.y)}function ny(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}class xN{constructor(){this.members=[]}add(t){Xh(this.members,t),t.scheduleRender()}remove(t){if(Jh(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){const n=this.members[this.members.length-1];n&&this.promote(n)}}relegate(t){const n=this.members.findIndex(s=>t===s);if(n===0)return!1;let r;for(let s=n;s>=0;s--){const i=this.members[s];if(i.isPresent!==!1){r=i;break}}return r?(this.promote(r),!0):!1}promote(t,n){const r=this.lead;if(t!==r&&(this.prevLead=r,this.lead=t,t.show(),r)){r.instance&&r.scheduleRender(),t.scheduleRender(),t.resumeFrom=r,n&&(t.resumeFrom.preserveOpacity=!0),r.snapshot&&(t.snapshot=r.snapshot,t.snapshot.latestValues=r.animationValues||r.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);const{crossfade:s}=t.options;s===!1&&r.hide()}}exitAnimationComplete(){this.members.forEach(t=>{const{options:n,resumingFrom:r}=t;n.onExitComplete&&n.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function wN(e,t,n){let r="";const s=e.x.translate/t.x,i=e.y.translate/t.y,o=(n==null?void 0:n.z)||0;if((s||i||o)&&(r=`translate3d(${s}px, ${i}px, ${o}px) `),(t.x!==1||t.y!==1)&&(r+=`scale(${1/t.x}, ${1/t.y}) `),n){const{transformPerspective:u,rotate:c,rotateX:d,rotateY:f,skewX:g,skewY:v}=n;u&&(r=`perspective(${u}px) ${r}`),c&&(r+=`rotate(${c}deg) `),d&&(r+=`rotateX(${d}deg) `),f&&(r+=`rotateY(${f}deg) `),g&&(r+=`skewX(${g}deg) `),v&&(r+=`skewY(${v}deg) `)}const a=e.x.scale*t.x,l=e.y.scale*t.y;return(a!==1||l!==1)&&(r+=`scale(${a}, ${l})`),r||"none"}const SN=(e,t)=>e.depth-t.depth;class _N{constructor(){this.children=[],this.isDirty=!1}add(t){Xh(this.children,t),this.isDirty=!0}remove(t){Jh(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(SN),this.isDirty=!1,this.children.forEach(t)}}function al(e){const t=rt(e)?e.get():e;return cR(t)?t.toValue():t}function TN(e,t){const n=Tn.now(),r=({timestamp:s})=>{const i=s-n;i>=t&&(Nr(r),e(i-t))};return ge.read(r,!0),()=>Nr(r)}function CN(e){return e instanceof SVGElement&&e.tagName!=="svg"}function kN(e,t,n){const r=rt(e)?e:zo(e);return r.start(Yh("",r,t,n)),r.animation}const Zr={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0},Yi=typeof window<"u"&&window.MotionDebug!==void 0,Ic=["","X","Y","Z"],bN={visibility:"hidden"},ry=1e3;let EN=0;function zc(e,t,n,r){const{latestValues:s}=t;s[e]&&(n[e]=s[e],t.setStaticValue(e,0),r&&(r[e]=0))}function B1(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;const{visualElement:t}=e.options;if(!t)return;const n=w1(t);if(window.MotionHasOptimisedAnimation(n,"transform")){const{layout:s,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(n,"transform",ge,!(s||i))}const{parent:r}=e;r&&!r.hasCheckedOptimisedAppear&&B1(r)}function $1({attachResizeListener:e,defaultParent:t,measureScroll:n,checkIsScrollRoot:r,resetTransform:s}){return class{constructor(o={},a=t==null?void 0:t()){this.id=EN++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,Yi&&(Zr.totalNodes=Zr.resolvedTargetDeltas=Zr.recalculatedProjection=0),this.nodes.forEach(RN),this.nodes.forEach(LN),this.nodes.forEach(jN),this.nodes.forEach(NN),Yi&&window.MotionDebug.record(Zr)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=o,this.root=a?a.root||a:this,this.path=a?[...a.path,a]:[],this.parent=a,this.depth=a?a.depth+1:0;for(let l=0;l<this.path.length;l++)this.path[l].shouldResetTransform=!0;this.root===this&&(this.nodes=new _N)}addEventListener(o,a){return this.eventHandlers.has(o)||this.eventHandlers.set(o,new ep),this.eventHandlers.get(o).add(a)}notifyListeners(o,...a){const l=this.eventHandlers.get(o);l&&l.notify(...a)}hasListeners(o){return this.eventHandlers.has(o)}mount(o,a=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=CN(o),this.instance=o;const{layoutId:l,layout:u,visualElement:c}=this.options;if(c&&!c.current&&c.mount(o),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),a&&(u||l)&&(this.isLayoutDirty=!0),e){let d;const f=()=>this.root.updateBlockedByResize=!1;e(o,()=>{this.root.updateBlockedByResize=!0,d&&d(),d=TN(f,250),ol.hasAnimatedSinceResize&&(ol.hasAnimatedSinceResize=!1,this.nodes.forEach(iy))})}l&&this.root.registerSharedNode(l,this),this.options.animate!==!1&&c&&(l||u)&&this.addEventListener("didUpdate",({delta:d,hasLayoutChanged:f,hasRelativeTargetChanged:g,layout:v})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const y=this.options.transition||c.getDefaultTransition()||BN,{onLayoutAnimationStart:w,onLayoutAnimationComplete:p}=c.getProps(),h=!this.targetLayout||!z1(this.targetLayout,v)||g,m=!f&&g;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||m||f&&(h||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(d,m);const _={...Vh(y,"layout"),onPlay:w,onComplete:p};(c.shouldReduceMotion||this.options.layoutRoot)&&(_.delay=0,_.type=!1),this.startAnimation(_)}else f||iy(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=v})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const o=this.getStack();o&&o.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,Nr(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(VN),this.animationId++)}getTransformTemplate(){const{visualElement:o}=this.options;return o&&o.getProps().transformTemplate}willUpdate(o=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&B1(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let c=0;c<this.path.length;c++){const d=this.path[c];d.shouldResetTransform=!0,d.updateScroll("snapshot"),d.options.layoutRoot&&d.willUpdate(!1)}const{layoutId:a,layout:l}=this.options;if(a===void 0&&!l)return;const u=this.getTransformTemplate();this.prevTransformTemplateValue=u?u(this.latestValues,""):void 0,this.updateSnapshot(),o&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(sy);return}this.isUpdating||this.nodes.forEach(DN),this.isUpdating=!1,this.nodes.forEach(ON),this.nodes.forEach(PN),this.nodes.forEach(AN),this.clearAllSnapshots();const a=Tn.now();Ke.delta=Gn(0,1e3/60,a-Ke.timestamp),Ke.timestamp=a,Ke.isProcessing=!0,Nc.update.process(Ke),Nc.preRender.process(Ke),Nc.render.process(Ke),Ke.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,rp.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(MN),this.sharedNodes.forEach(FN)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,ge.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){ge.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let l=0;l<this.path.length;l++)this.path[l].updateScroll();const o=this.layout;this.layout=this.measure(!1),this.layoutCorrected=Me(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:a}=this.options;a&&a.notify("LayoutMeasure",this.layout.layoutBox,o?o.layoutBox:void 0)}updateScroll(o="measure"){let a=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===o&&(a=!1),a){const l=r(this.instance);this.scroll={animationId:this.root.animationId,phase:o,isRoot:l,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:l}}}resetTransform(){if(!s)return;const o=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,a=this.projectionDelta&&!I1(this.projectionDelta),l=this.getTransformTemplate(),u=l?l(this.latestValues,""):void 0,c=u!==this.prevTransformTemplateValue;o&&(a||Hr(this.latestValues)||c)&&(s(this.instance,u),this.shouldResetTransform=!1,this.scheduleRender())}measure(o=!0){const a=this.measurePageBox();let l=this.removeElementScroll(a);return o&&(l=this.removeTransform(l)),$N(l),{animationId:this.root.animationId,measuredBox:a,layoutBox:l,latestValues:{},source:this.id}}measurePageBox(){var o;const{visualElement:a}=this.options;if(!a)return Me();const l=a.measureViewportBox();if(!(((o=this.scroll)===null||o===void 0?void 0:o.wasRoot)||this.path.some(UN))){const{scroll:c}=this.root;c&&(zs(l.x,c.offset.x),zs(l.y,c.offset.y))}return l}removeElementScroll(o){var a;const l=Me();if(jt(l,o),!((a=this.scroll)===null||a===void 0)&&a.wasRoot)return l;for(let u=0;u<this.path.length;u++){const c=this.path[u],{scroll:d,options:f}=c;c!==this.root&&d&&f.layoutScroll&&(d.wasRoot&&jt(l,o),zs(l.x,d.offset.x),zs(l.y,d.offset.y))}return l}applyTransform(o,a=!1){const l=Me();jt(l,o);for(let u=0;u<this.path.length;u++){const c=this.path[u];!a&&c.options.layoutScroll&&c.scroll&&c!==c.root&&Bs(l,{x:-c.scroll.offset.x,y:-c.scroll.offset.y}),Hr(c.latestValues)&&Bs(l,c.latestValues)}return Hr(this.latestValues)&&Bs(l,this.latestValues),l}removeTransform(o){const a=Me();jt(a,o);for(let l=0;l<this.path.length;l++){const u=this.path[l];if(!u.instance||!Hr(u.latestValues))continue;uf(u.latestValues)&&u.updateSnapshot();const c=Me(),d=u.measurePageBox();jt(c,d),Yg(a,u.latestValues,u.snapshot?u.snapshot.layoutBox:void 0,c)}return Hr(this.latestValues)&&Yg(a,this.latestValues),a}setTargetDelta(o){this.targetDelta=o,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(o){this.options={...this.options,...o,crossfade:o.crossfade!==void 0?o.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==Ke.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(o=!1){var a;const l=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=l.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=l.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=l.isSharedProjectionDirty);const u=!!this.resumingFrom||this!==l;if(!(o||u&&this.isSharedProjectionDirty||this.isProjectionDirty||!((a=this.parent)===null||a===void 0)&&a.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:d,layoutId:f}=this.options;if(!(!this.layout||!(d||f))){if(this.resolvedRelativeTargetAt=Ke.timestamp,!this.targetDelta&&!this.relativeTarget){const g=this.getClosestProjectingParent();g&&g.layout&&this.animationProgress!==1?(this.relativeParent=g,this.forceRelativeParentToResolveTarget(),this.relativeTarget=Me(),this.relativeTargetOrigin=Me(),ho(this.relativeTargetOrigin,this.layout.layoutBox,g.layout.layoutBox),jt(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)){if(this.target||(this.target=Me(),this.targetWithTransforms=Me()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),WR(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):jt(this.target,this.layout.layoutBox),N1(this.target,this.targetDelta)):jt(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const g=this.getClosestProjectingParent();g&&!!g.resumingFrom==!!this.resumingFrom&&!g.options.layoutScroll&&g.target&&this.animationProgress!==1?(this.relativeParent=g,this.forceRelativeParentToResolveTarget(),this.relativeTarget=Me(),this.relativeTargetOrigin=Me(),ho(this.relativeTargetOrigin,this.target,g.target),jt(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}Yi&&Zr.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||uf(this.parent.latestValues)||R1(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var o;const a=this.getLead(),l=!!this.resumingFrom||this!==a;let u=!0;if((this.isProjectionDirty||!((o=this.parent)===null||o===void 0)&&o.isProjectionDirty)&&(u=!1),l&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(u=!1),this.resolvedRelativeTargetAt===Ke.timestamp&&(u=!1),u)return;const{layout:c,layoutId:d}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(c||d))return;jt(this.layoutCorrected,this.layout.layoutBox);const f=this.treeScale.x,g=this.treeScale.y;JR(this.layoutCorrected,this.treeScale,this.path,l),a.layout&&!a.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(a.target=a.layout.layoutBox,a.targetWithTransforms=Me());const{target:v}=a;if(!v){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}!this.projectionDelta||!this.prevProjectionDelta?this.createProjectionDeltas():(qg(this.prevProjectionDelta.x,this.projectionDelta.x),qg(this.prevProjectionDelta.y,this.projectionDelta.y)),fo(this.projectionDelta,this.layoutCorrected,v,this.latestValues),(this.treeScale.x!==f||this.treeScale.y!==g||!ny(this.projectionDelta.x,this.prevProjectionDelta.x)||!ny(this.projectionDelta.y,this.prevProjectionDelta.y))&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",v)),Yi&&Zr.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(o=!0){var a;if((a=this.options.visualElement)===null||a===void 0||a.scheduleRender(),o){const l=this.getStack();l&&l.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=Is(),this.projectionDelta=Is(),this.projectionDeltaWithTransform=Is()}setAnimationOrigin(o,a=!1){const l=this.snapshot,u=l?l.latestValues:{},c={...this.latestValues},d=Is();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!a;const f=Me(),g=l?l.source:void 0,v=this.layout?this.layout.source:void 0,y=g!==v,w=this.getStack(),p=!w||w.members.length<=1,h=!!(y&&!p&&this.options.crossfade===!0&&!this.path.some(zN));this.animationProgress=0;let m;this.mixTargetDelta=_=>{const C=_/1e3;oy(d.x,o.x,C),oy(d.y,o.y,C),this.setTargetDelta(d),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(ho(f,this.layout.layoutBox,this.relativeParent.layout.layoutBox),IN(this.relativeTarget,this.relativeTargetOrigin,f,C),m&&vN(this.relativeTarget,m)&&(this.isProjectionDirty=!1),m||(m=Me()),jt(m,this.relativeTarget)),y&&(this.animationValues=c,fN(c,u,this.latestValues,C,h,p)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=C},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(o){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(Nr(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=ge.update(()=>{ol.hasAnimatedSinceResize=!0,this.currentAnimation=kN(0,ry,{...o,onUpdate:a=>{this.mixTargetDelta(a),o.onUpdate&&o.onUpdate(a)},onComplete:()=>{o.onComplete&&o.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const o=this.getStack();o&&o.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(ry),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const o=this.getLead();let{targetWithTransforms:a,target:l,layout:u,latestValues:c}=o;if(!(!a||!l||!u)){if(this!==o&&this.layout&&u&&U1(this.options.animationType,this.layout.layoutBox,u.layoutBox)){l=this.target||Me();const d=Dt(this.layout.layoutBox.x);l.x.min=o.target.x.min,l.x.max=l.x.min+d;const f=Dt(this.layout.layoutBox.y);l.y.min=o.target.y.min,l.y.max=l.y.min+f}jt(a,l),Bs(a,c),fo(this.projectionDeltaWithTransform,this.layoutCorrected,a,c)}}registerSharedNode(o,a){this.sharedNodes.has(o)||this.sharedNodes.set(o,new xN),this.sharedNodes.get(o).add(a);const u=a.options.initialPromotionConfig;a.promote({transition:u?u.transition:void 0,preserveFollowOpacity:u&&u.shouldPreserveFollowOpacity?u.shouldPreserveFollowOpacity(a):void 0})}isLead(){const o=this.getStack();return o?o.lead===this:!0}getLead(){var o;const{layoutId:a}=this.options;return a?((o=this.getStack())===null||o===void 0?void 0:o.lead)||this:this}getPrevLead(){var o;const{layoutId:a}=this.options;return a?(o=this.getStack())===null||o===void 0?void 0:o.prevLead:void 0}getStack(){const{layoutId:o}=this.options;if(o)return this.root.sharedNodes.get(o)}promote({needsReset:o,transition:a,preserveFollowOpacity:l}={}){const u=this.getStack();u&&u.promote(this,l),o&&(this.projectionDelta=void 0,this.needsReset=!0),a&&this.setOptions({transition:a})}relegate(){const o=this.getStack();return o?o.relegate(this):!1}resetSkewAndRotation(){const{visualElement:o}=this.options;if(!o)return;let a=!1;const{latestValues:l}=o;if((l.z||l.rotate||l.rotateX||l.rotateY||l.rotateZ||l.skewX||l.skewY)&&(a=!0),!a)return;const u={};l.z&&zc("z",o,u,this.animationValues);for(let c=0;c<Ic.length;c++)zc(`rotate${Ic[c]}`,o,u,this.animationValues),zc(`skew${Ic[c]}`,o,u,this.animationValues);o.render();for(const c in u)o.setStaticValue(c,u[c]),this.animationValues&&(this.animationValues[c]=u[c]);o.scheduleRender()}getProjectionStyles(o){var a,l;if(!this.instance||this.isSVG)return;if(!this.isVisible)return bN;const u={visibility:""},c=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,u.opacity="",u.pointerEvents=al(o==null?void 0:o.pointerEvents)||"",u.transform=c?c(this.latestValues,""):"none",u;const d=this.getLead();if(!this.projectionDelta||!this.layout||!d.target){const y={};return this.options.layoutId&&(y.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,y.pointerEvents=al(o==null?void 0:o.pointerEvents)||""),this.hasProjected&&!Hr(this.latestValues)&&(y.transform=c?c({},""):"none",this.hasProjected=!1),y}const f=d.animationValues||d.latestValues;this.applyTransformsToTarget(),u.transform=wN(this.projectionDeltaWithTransform,this.treeScale,f),c&&(u.transform=c(f,u.transform));const{x:g,y:v}=this.projectionDelta;u.transformOrigin=`${g.origin*100}% ${v.origin*100}% 0`,d.animationValues?u.opacity=d===this?(l=(a=f.opacity)!==null&&a!==void 0?a:this.latestValues.opacity)!==null&&l!==void 0?l:1:this.preserveOpacity?this.latestValues.opacity:f.opacityExit:u.opacity=d===this?f.opacity!==void 0?f.opacity:"":f.opacityExit!==void 0?f.opacityExit:0;for(const y in ql){if(f[y]===void 0)continue;const{correct:w,applyTo:p}=ql[y],h=u.transform==="none"?f[y]:w(f[y],d);if(p){const m=p.length;for(let _=0;_<m;_++)u[p[_]]=h}else u[y]=h}return this.options.layoutId&&(u.pointerEvents=d===this?al(o==null?void 0:o.pointerEvents)||"":"none"),u}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(o=>{var a;return(a=o.currentAnimation)===null||a===void 0?void 0:a.stop()}),this.root.nodes.forEach(sy),this.root.sharedNodes.clear()}}}function PN(e){e.updateLayout()}function AN(e){var t;const n=((t=e.resumeFrom)===null||t===void 0?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&n&&e.hasListeners("didUpdate")){const{layoutBox:r,measuredBox:s}=e.layout,{animationType:i}=e.options,o=n.source!==e.layout.source;i==="size"?Vt(d=>{const f=o?n.measuredBox[d]:n.layoutBox[d],g=Dt(f);f.min=r[d].min,f.max=f.min+g}):U1(i,n.layoutBox,r)&&Vt(d=>{const f=o?n.measuredBox[d]:n.layoutBox[d],g=Dt(r[d]);f.max=f.min+g,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[d].max=e.relativeTarget[d].min+g)});const a=Is();fo(a,r,n.layoutBox);const l=Is();o?fo(l,e.applyTransform(s,!0),n.measuredBox):fo(l,r,n.layoutBox);const u=!I1(a);let c=!1;if(!e.resumeFrom){const d=e.getClosestProjectingParent();if(d&&!d.resumeFrom){const{snapshot:f,layout:g}=d;if(f&&g){const v=Me();ho(v,n.layoutBox,f.layoutBox);const y=Me();ho(y,r,g.layoutBox),z1(v,y)||(c=!0),d.options.layoutRoot&&(e.relativeTarget=y,e.relativeTargetOrigin=v,e.relativeParent=d)}}}e.notifyListeners("didUpdate",{layout:r,snapshot:n,delta:l,layoutDelta:a,hasLayoutChanged:u,hasRelativeTargetChanged:c})}else if(e.isLead()){const{onExitComplete:r}=e.options;r&&r()}e.options.transition=void 0}function RN(e){Yi&&Zr.totalNodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function NN(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function MN(e){e.clearSnapshot()}function sy(e){e.clearMeasurements()}function DN(e){e.isLayoutDirty=!1}function ON(e){const{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function iy(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function LN(e){e.resolveTargetDelta()}function jN(e){e.calcProjection()}function VN(e){e.resetSkewAndRotation()}function FN(e){e.removeLeadSnapshot()}function oy(e,t,n){e.translate=be(t.translate,0,n),e.scale=be(t.scale,1,n),e.origin=t.origin,e.originPoint=t.originPoint}function ay(e,t,n,r){e.min=be(t.min,n.min,r),e.max=be(t.max,n.max,r)}function IN(e,t,n,r){ay(e.x,t.x,n.x,r),ay(e.y,t.y,n.y,r)}function zN(e){return e.animationValues&&e.animationValues.opacityExit!==void 0}const BN={duration:.45,ease:[.4,0,.1,1]},ly=e=>typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),uy=ly("applewebkit/")&&!ly("chrome/")?Math.round:st;function cy(e){e.min=uy(e.min),e.max=uy(e.max)}function $N(e){cy(e.x),cy(e.y)}function U1(e,t,n){return e==="position"||e==="preserve-aspect"&&!UR(ty(t),ty(n),.2)}function UN(e){var t;return e!==e.root&&((t=e.scroll)===null||t===void 0?void 0:t.wasRoot)}const WN=$1({attachResizeListener:(e,t)=>In(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),Bc={current:void 0},W1=$1({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!Bc.current){const e=new WN({});e.mount(window),e.setOptions({layoutScroll:!0}),Bc.current=e}return Bc.current},resetTransform:(e,t)=>{e.style.transform=t!==void 0?t:"none"},checkIsScrollRoot:e=>window.getComputedStyle(e).position==="fixed"}),HN={pan:{Feature:iN},drag:{Feature:sN,ProjectionNode:W1,MeasureLayout:j1}};function dy(e,t,n){const{props:r}=e;e.animationState&&r.whileHover&&e.animationState.setActive("whileHover",n);const s=r[n?"onHoverStart":"onHoverEnd"];s&&ge.postRender(()=>s(t,fa(t)))}class ZN extends zr{mount(){const{current:t,props:n}=this.node;t&&(this.unmount=DR(t,r=>(dy(this.node,r,!0),s=>dy(this.node,s,!1)),{passive:!n.onHoverStart&&!n.onHoverEnd}))}unmount(){}}class KN extends zr{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch{t=!0}!t||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=Cr(In(this.node.current,"focus",()=>this.onFocus()),In(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}const H1=(e,t)=>t?e===t?!0:H1(e,t.parentElement):!1;function $c(e,t){if(!t)return;const n=new PointerEvent("pointer"+e);t(n,fa(n))}class qN extends zr{constructor(){super(...arguments),this.removeStartListeners=st,this.removeEndListeners=st,this.removeAccessibleListeners=st,this.startPointerPress=(t,n)=>{if(this.isPressing)return;this.removeEndListeners();const r=this.node.getProps(),i=kr(window,"pointerup",(a,l)=>{if(!this.checkPressEnd())return;const{onTap:u,onTapCancel:c,globalTapTarget:d}=this.node.getProps(),f=!d&&!H1(this.node.current,a.target)?c:u;f&&ge.update(()=>f(a,l))},{passive:!(r.onTap||r.onPointerUp)}),o=kr(window,"pointercancel",(a,l)=>this.cancelPress(a,l),{passive:!(r.onTapCancel||r.onPointerCancel)});this.removeEndListeners=Cr(i,o),this.startPress(t,n)},this.startAccessiblePress=()=>{const t=i=>{if(i.key!=="Enter"||this.isPressing)return;const o=a=>{a.key!=="Enter"||!this.checkPressEnd()||$c("up",(l,u)=>{const{onTap:c}=this.node.getProps();c&&ge.postRender(()=>c(l,u))})};this.removeEndListeners(),this.removeEndListeners=In(this.node.current,"keyup",o),$c("down",(a,l)=>{this.startPress(a,l)})},n=In(this.node.current,"keydown",t),r=()=>{this.isPressing&&$c("cancel",(i,o)=>this.cancelPress(i,o))},s=In(this.node.current,"blur",r);this.removeAccessibleListeners=Cr(n,s)}}startPress(t,n){this.isPressing=!0;const{onTapStart:r,whileTap:s}=this.node.getProps();s&&this.node.animationState&&this.node.animationState.setActive("whileTap",!0),r&&ge.postRender(()=>r(t,n))}checkPressEnd(){return this.removeEndListeners(),this.isPressing=!1,this.node.getProps().whileTap&&this.node.animationState&&this.node.animationState.setActive("whileTap",!1),!T1()}cancelPress(t,n){if(!this.checkPressEnd())return;const{onTapCancel:r}=this.node.getProps();r&&ge.postRender(()=>r(t,n))}mount(){const t=this.node.getProps(),n=kr(t.globalTapTarget?window:this.node.current,"pointerdown",this.startPointerPress,{passive:!(t.onTapStart||t.onPointerStart)}),r=In(this.node.current,"focus",this.startAccessiblePress);this.removeStartListeners=Cr(n,r)}unmount(){this.removeStartListeners(),this.removeEndListeners(),this.removeAccessibleListeners()}}const df=new WeakMap,Uc=new WeakMap,GN=e=>{const t=df.get(e.target);t&&t(e)},QN=e=>{e.forEach(GN)};function YN({root:e,...t}){const n=e||document;Uc.has(n)||Uc.set(n,{});const r=Uc.get(n),s=JSON.stringify(t);return r[s]||(r[s]=new IntersectionObserver(QN,{root:e,...t})),r[s]}function XN(e,t,n){const r=YN(t);return df.set(e,n),r.observe(e),()=>{df.delete(e),r.unobserve(e)}}const JN={some:0,all:1};class e2 extends zr{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:t={}}=this.node.getProps(),{root:n,margin:r,amount:s="some",once:i}=t,o={root:n?n.current:void 0,rootMargin:r,threshold:typeof s=="number"?s:JN[s]},a=l=>{const{isIntersecting:u}=l;if(this.isInView===u||(this.isInView=u,i&&!u&&this.hasEnteredView))return;u&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",u);const{onViewportEnter:c,onViewportLeave:d}=this.node.getProps(),f=u?c:d;f&&f(l)};return XN(this.node.current,o,a)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:t,prevProps:n}=this.node;["amount","margin","root"].some(t2(t,n))&&this.startObserver()}unmount(){}}function t2({viewport:e={}},{viewport:t={}}={}){return n=>e[n]!==t[n]}const n2={inView:{Feature:e2},tap:{Feature:qN},focus:{Feature:KN},hover:{Feature:ZN}},r2={layout:{ProjectionNode:W1,MeasureLayout:j1}},Z1=x.createContext({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"}),zu=x.createContext({}),sp=typeof window<"u",s2=sp?x.useLayoutEffect:x.useEffect,K1=x.createContext({strict:!1});function i2(e,t,n,r,s){var i,o;const{visualElement:a}=x.useContext(zu),l=x.useContext(K1),u=x.useContext(np),c=x.useContext(Z1).reducedMotion,d=x.useRef();r=r||l.renderer,!d.current&&r&&(d.current=r(e,{visualState:t,parent:a,props:n,presenceContext:u,blockInitialAnimation:u?u.initial===!1:!1,reducedMotionConfig:c}));const f=d.current,g=x.useContext(L1);f&&!f.projection&&s&&(f.type==="html"||f.type==="svg")&&o2(d.current,n,s,g);const v=x.useRef(!1);x.useInsertionEffect(()=>{f&&v.current&&f.update(n,u)});const y=n[x1],w=x.useRef(!!y&&!(!((i=window.MotionHandoffIsComplete)===null||i===void 0)&&i.call(window,y))&&((o=window.MotionHasOptimisedAnimation)===null||o===void 0?void 0:o.call(window,y)));return s2(()=>{f&&(v.current=!0,window.MotionIsMounted=!0,f.updateFeatures(),rp.render(f.render),w.current&&f.animationState&&f.animationState.animateChanges())}),x.useEffect(()=>{f&&(!w.current&&f.animationState&&f.animationState.animateChanges(),w.current&&(queueMicrotask(()=>{var p;(p=window.MotionHandoffMarkAsComplete)===null||p===void 0||p.call(window,y)}),w.current=!1))}),f}function o2(e,t,n,r){const{layoutId:s,layout:i,drag:o,dragConstraints:a,layoutScroll:l,layoutRoot:u}=t;e.projection=new n(e.latestValues,t["data-framer-portal-id"]?void 0:q1(e.parent)),e.projection.setOptions({layoutId:s,layout:i,alwaysMeasureLayout:!!o||a&&Fs(a),visualElement:e,animationType:typeof i=="string"?i:"both",initialPromotionConfig:r,layoutScroll:l,layoutRoot:u})}function q1(e){if(e)return e.options.allowProjection!==!1?e.projection:q1(e.parent)}function a2(e,t,n){return x.useCallback(r=>{r&&e.mount&&e.mount(r),t&&(r?t.mount(r):t.unmount()),n&&(typeof n=="function"?n(r):Fs(n)&&(n.current=r))},[t])}function Bu(e){return Vu(e.animate)||jh.some(t=>Vo(e[t]))}function G1(e){return!!(Bu(e)||e.variants)}function l2(e,t){if(Bu(e)){const{initial:n,animate:r}=e;return{initial:n===!1||Vo(n)?n:void 0,animate:Vo(r)?r:void 0}}return e.inherit!==!1?t:{}}function u2(e){const{initial:t,animate:n}=l2(e,x.useContext(zu));return x.useMemo(()=>({initial:t,animate:n}),[fy(t),fy(n)])}function fy(e){return Array.isArray(e)?e.join(" "):e}const hy={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},xi={};for(const e in hy)xi[e]={isEnabled:t=>hy[e].some(n=>!!t[n])};function c2(e){for(const t in e)xi[t]={...xi[t],...e[t]}}const d2=Symbol.for("motionComponentSymbol");function f2({preloadedFeatures:e,createVisualElement:t,useRender:n,useVisualState:r,Component:s}){e&&c2(e);function i(a,l){let u;const c={...x.useContext(Z1),...a,layoutId:h2(a)},{isStatic:d}=c,f=u2(a),g=r(a,d);if(!d&&sp){p2();const v=m2(c);u=v.MeasureLayout,f.visualElement=i2(s,g,c,t,v.ProjectionNode)}return S.jsxs(zu.Provider,{value:f,children:[u&&f.visualElement?S.jsx(u,{visualElement:f.visualElement,...c}):null,n(s,a,a2(g,f.visualElement,l),g,d,f.visualElement)]})}const o=x.forwardRef(i);return o[d2]=s,o}function h2({layoutId:e}){const t=x.useContext(O1).id;return t&&e!==void 0?t+"-"+e:e}function p2(e,t){x.useContext(K1).strict}function m2(e){const{drag:t,layout:n}=xi;if(!t&&!n)return{};const r={...t,...n};return{MeasureLayout:t!=null&&t.isEnabled(e)||n!=null&&n.isEnabled(e)?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}const g2=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function ip(e){return typeof e!="string"||e.includes("-")?!1:!!(g2.indexOf(e)>-1||/[A-Z]/u.test(e))}function Q1(e,{style:t,vars:n},r,s){Object.assign(e.style,t,s&&s.getProjectionStyles(r));for(const i in n)e.style.setProperty(i,n[i])}const Y1=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function X1(e,t,n,r){Q1(e,t,void 0,r);for(const s in t.attrs)e.setAttribute(Y1.has(s)?s:tp(s),t.attrs[s])}function J1(e,{layout:t,layoutId:n}){return vs.has(e)||e.startsWith("origin")||(t||n!==void 0)&&(!!ql[e]||e==="opacity")}function op(e,t,n){var r;const{style:s}=e,i={};for(const o in s)(rt(s[o])||t.style&&rt(t.style[o])||J1(o,e)||((r=n==null?void 0:n.getValue(o))===null||r===void 0?void 0:r.liveStyle)!==void 0)&&(i[o]=s[o]);return i}function eS(e,t,n){const r=op(e,t,n);for(const s in e)if(rt(e[s])||rt(t[s])){const i=ua.indexOf(s)!==-1?"attr"+s.charAt(0).toUpperCase()+s.substring(1):s;r[i]=e[s]}return r}function y2(e){const t=x.useRef(null);return t.current===null&&(t.current=e()),t.current}function v2({scrapeMotionValuesFromProps:e,createRenderState:t,onMount:n},r,s,i){const o={latestValues:x2(r,s,i,e),renderState:t()};return n&&(o.mount=a=>n(r,a,o)),o}const tS=e=>(t,n)=>{const r=x.useContext(zu),s=x.useContext(np),i=()=>v2(e,t,r,s);return n?i():y2(i)};function x2(e,t,n,r){const s={},i=r(e,{});for(const f in i)s[f]=al(i[f]);let{initial:o,animate:a}=e;const l=Bu(e),u=G1(e);t&&u&&!l&&e.inherit!==!1&&(o===void 0&&(o=t.initial),a===void 0&&(a=t.animate));let c=n?n.initial===!1:!1;c=c||o===!1;const d=c?a:o;if(d&&typeof d!="boolean"&&!Vu(d)){const f=Array.isArray(d)?d:[d];for(let g=0;g<f.length;g++){const v=Oh(e,f[g]);if(v){const{transitionEnd:y,transition:w,...p}=v;for(const h in p){let m=p[h];if(Array.isArray(m)){const _=c?m.length-1:0;m=m[_]}m!==null&&(s[h]=m)}for(const h in y)s[h]=y[h]}}}return s}const ap=()=>({style:{},transform:{},transformOrigin:{},vars:{}}),nS=()=>({...ap(),attrs:{}}),rS=(e,t)=>t&&typeof e=="number"?t.transform(e):e,w2={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},S2=ua.length;function _2(e,t,n){let r="",s=!0;for(let i=0;i<S2;i++){const o=ua[i],a=e[o];if(a===void 0)continue;let l=!0;if(typeof a=="number"?l=a===(o.startsWith("scale")?1:0):l=parseFloat(a)===0,!l||n){const u=rS(a,Wh[o]);if(!l){s=!1;const c=w2[o]||o;r+=`${c}(${u}) `}n&&(t[o]=u)}}return r=r.trim(),n?r=n(t,s?"":r):s&&(r="none"),r}function lp(e,t,n){const{style:r,vars:s,transformOrigin:i}=e;let o=!1,a=!1;for(const l in t){const u=t[l];if(vs.has(l)){o=!0;continue}else if(qw(l)){s[l]=u;continue}else{const c=rS(u,Wh[l]);l.startsWith("origin")?(a=!0,i[l]=c):r[l]=c}}if(t.transform||(o||n?r.transform=_2(t,e.transform,n):r.transform&&(r.transform="none")),a){const{originX:l="50%",originY:u="50%",originZ:c=0}=i;r.transformOrigin=`${l} ${u} ${c}`}}function py(e,t,n){return typeof e=="string"?e:J.transform(t+n*e)}function T2(e,t,n){const r=py(t,e.x,e.width),s=py(n,e.y,e.height);return`${r} ${s}`}const C2={offset:"stroke-dashoffset",array:"stroke-dasharray"},k2={offset:"strokeDashoffset",array:"strokeDasharray"};function b2(e,t,n=1,r=0,s=!0){e.pathLength=1;const i=s?C2:k2;e[i.offset]=J.transform(-r);const o=J.transform(t),a=J.transform(n);e[i.array]=`${o} ${a}`}function up(e,{attrX:t,attrY:n,attrScale:r,originX:s,originY:i,pathLength:o,pathSpacing:a=1,pathOffset:l=0,...u},c,d){if(lp(e,u,d),c){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};const{attrs:f,style:g,dimensions:v}=e;f.transform&&(v&&(g.transform=f.transform),delete f.transform),v&&(s!==void 0||i!==void 0||g.transform)&&(g.transformOrigin=T2(v,s!==void 0?s:.5,i!==void 0?i:.5)),t!==void 0&&(f.x=t),n!==void 0&&(f.y=n),r!==void 0&&(f.scale=r),o!==void 0&&b2(f,o,a,l,!1)}const cp=e=>typeof e=="string"&&e.toLowerCase()==="svg",E2={useVisualState:tS({scrapeMotionValuesFromProps:eS,createRenderState:nS,onMount:(e,t,{renderState:n,latestValues:r})=>{ge.read(()=>{try{n.dimensions=typeof t.getBBox=="function"?t.getBBox():t.getBoundingClientRect()}catch{n.dimensions={x:0,y:0,width:0,height:0}}}),ge.render(()=>{up(n,r,cp(t.tagName),e.transformTemplate),X1(t,n)})}})},P2={useVisualState:tS({scrapeMotionValuesFromProps:op,createRenderState:ap})};function sS(e,t,n){for(const r in t)!rt(t[r])&&!J1(r,n)&&(e[r]=t[r])}function A2({transformTemplate:e},t){return x.useMemo(()=>{const n=ap();return lp(n,t,e),Object.assign({},n.vars,n.style)},[t])}function R2(e,t){const n=e.style||{},r={};return sS(r,n,e),Object.assign(r,A2(e,t)),r}function N2(e,t){const n={},r=R2(e,t);return e.drag&&e.dragListener!==!1&&(n.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=e.drag===!0?"none":`pan-${e.drag==="x"?"y":"x"}`),e.tabIndex===void 0&&(e.onTap||e.onTapStart||e.whileTap)&&(n.tabIndex=0),n.style=r,n}const M2=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function Gl(e){return e.startsWith("while")||e.startsWith("drag")&&e!=="draggable"||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||M2.has(e)}let iS=e=>!Gl(e);function D2(e){e&&(iS=t=>t.startsWith("on")?!Gl(t):e(t))}try{D2(require("@emotion/is-prop-valid").default)}catch{}function O2(e,t,n){const r={};for(const s in e)s==="values"&&typeof e.values=="object"||(iS(s)||n===!0&&Gl(s)||!t&&!Gl(s)||e.draggable&&s.startsWith("onDrag"))&&(r[s]=e[s]);return r}function L2(e,t,n,r){const s=x.useMemo(()=>{const i=nS();return up(i,t,cp(r),e.transformTemplate),{...i.attrs,style:{...i.style}}},[t]);if(e.style){const i={};sS(i,e.style,e),s.style={...i,...s.style}}return s}function j2(e=!1){return(n,r,s,{latestValues:i},o)=>{const l=(ip(n)?L2:N2)(r,i,o,n),u=O2(r,typeof n=="string",e),c=n!==x.Fragment?{...u,...l,ref:s}:{},{children:d}=r,f=x.useMemo(()=>rt(d)?d.get():d,[d]);return x.createElement(n,{...c,children:f})}}function V2(e,t){return function(r,{forwardMotionProps:s}={forwardMotionProps:!1}){const o={...ip(r)?E2:P2,preloadedFeatures:e,useRender:j2(s),createVisualElement:t,Component:r};return f2(o)}}const ff={current:null},oS={current:!1};function F2(){if(oS.current=!0,!!sp)if(window.matchMedia){const e=window.matchMedia("(prefers-reduced-motion)"),t=()=>ff.current=e.matches;e.addListener(t),t()}else ff.current=!1}function I2(e,t,n){for(const r in t){const s=t[r],i=n[r];if(rt(s))e.addValue(r,s);else if(rt(i))e.addValue(r,zo(s,{owner:e}));else if(i!==s)if(e.hasValue(r)){const o=e.getValue(r);o.liveStyle===!0?o.jump(s):o.hasAnimated||o.set(s)}else{const o=e.getStaticValue(r);e.addValue(r,zo(o!==void 0?o:s,{owner:e}))}}for(const r in n)t[r]===void 0&&e.removeValue(r);return t}const my=new WeakMap,z2=[...Yw,tt,Mr],B2=e=>z2.find(Qw(e)),gy=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class $2{scrapeMotionValuesFromProps(t,n,r){return{}}constructor({parent:t,props:n,presenceContext:r,reducedMotionConfig:s,blockInitialAnimation:i,visualState:o},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=Bh,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const f=Tn.now();this.renderScheduledAt<f&&(this.renderScheduledAt=f,ge.render(this.render,!1,!0))};const{latestValues:l,renderState:u}=o;this.latestValues=l,this.baseTarget={...l},this.initialValues=n.initial?{...l}:{},this.renderState=u,this.parent=t,this.props=n,this.presenceContext=r,this.depth=t?t.depth+1:0,this.reducedMotionConfig=s,this.options=a,this.blockInitialAnimation=!!i,this.isControllingVariants=Bu(n),this.isVariantNode=G1(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);const{willChange:c,...d}=this.scrapeMotionValuesFromProps(n,{},this);for(const f in d){const g=d[f];l[f]!==void 0&&rt(g)&&g.set(l[f],!1)}}mount(t){this.current=t,my.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((n,r)=>this.bindToMotionValue(r,n)),oS.current||F2(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:ff.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){my.delete(this.current),this.projection&&this.projection.unmount(),Nr(this.notifyUpdate),Nr(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const t in this.events)this.events[t].clear();for(const t in this.features){const n=this.features[t];n&&(n.unmount(),n.isMounted=!1)}this.current=null}bindToMotionValue(t,n){this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();const r=vs.has(t),s=n.on("change",a=>{this.latestValues[t]=a,this.props.onUpdate&&ge.preRender(this.notifyUpdate),r&&this.projection&&(this.projection.isTransformDirty=!0)}),i=n.on("renderRequest",this.scheduleRender);let o;window.MotionCheckAppearSync&&(o=window.MotionCheckAppearSync(this,t,n)),this.valueSubscriptions.set(t,()=>{s(),i(),o&&o(),n.owner&&n.stop()})}sortNodePosition(t){return!this.current||!this.sortInstanceNodePosition||this.type!==t.type?0:this.sortInstanceNodePosition(this.current,t.current)}updateFeatures(){let t="animation";for(t in xi){const n=xi[t];if(!n)continue;const{isEnabled:r,Feature:s}=n;if(!this.features[t]&&s&&r(this.props)&&(this.features[t]=new s(this)),this.features[t]){const i=this.features[t];i.isMounted?i.update():(i.mount(),i.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):Me()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,n){this.latestValues[t]=n}update(t,n){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=n;for(let r=0;r<gy.length;r++){const s=gy[r];this.propEventSubscriptions[s]&&(this.propEventSubscriptions[s](),delete this.propEventSubscriptions[s]);const i="on"+s,o=t[i];o&&(this.propEventSubscriptions[s]=this.on(s,o))}this.prevMotionValues=I2(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){const n=this.getClosestVariantNode();if(n)return n.variantChildren&&n.variantChildren.add(t),()=>n.variantChildren.delete(t)}addValue(t,n){const r=this.values.get(t);n!==r&&(r&&this.removeValue(t),this.bindToMotionValue(t,n),this.values.set(t,n),this.latestValues[t]=n.get())}removeValue(t){this.values.delete(t);const n=this.valueSubscriptions.get(t);n&&(n(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,n){if(this.props.values&&this.props.values[t])return this.props.values[t];let r=this.values.get(t);return r===void 0&&n!==void 0&&(r=zo(n===null?void 0:n,{owner:this}),this.addValue(t,r)),r}readValue(t,n){var r;let s=this.latestValues[t]!==void 0||!this.current?this.latestValues[t]:(r=this.getBaseTargetFromProps(this.props,t))!==null&&r!==void 0?r:this.readValueFromInstance(this.current,t,this.options);return s!=null&&(typeof s=="string"&&(Zw(s)||Hw(s))?s=parseFloat(s):!B2(s)&&Mr.test(n)&&(s=i1(t,n)),this.setBaseTarget(t,rt(s)?s.get():s)),rt(s)?s.get():s}setBaseTarget(t,n){this.baseTarget[t]=n}getBaseTarget(t){var n;const{initial:r}=this.props;let s;if(typeof r=="string"||typeof r=="object"){const o=Oh(this.props,r,(n=this.presenceContext)===null||n===void 0?void 0:n.custom);o&&(s=o[t])}if(r&&s!==void 0)return s;const i=this.getBaseTargetFromProps(this.props,t);return i!==void 0&&!rt(i)?i:this.initialValues[t]!==void 0&&s===void 0?void 0:this.baseTarget[t]}on(t,n){return this.events[t]||(this.events[t]=new ep),this.events[t].add(n)}notify(t,...n){this.events[t]&&this.events[t].notify(...n)}}class aS extends $2{constructor(){super(...arguments),this.KeyframeResolver=o1}sortInstanceNodePosition(t,n){return t.compareDocumentPosition(n)&2?1:-1}getBaseTargetFromProps(t,n){return t.style?t.style[n]:void 0}removeValueFromRenderState(t,{vars:n,style:r}){delete n[t],delete r[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;rt(t)&&(this.childSubscription=t.on("change",n=>{this.current&&(this.current.textContent=`${n}`)}))}}function U2(e){return window.getComputedStyle(e)}class W2 extends aS{constructor(){super(...arguments),this.type="html",this.renderInstance=Q1}readValueFromInstance(t,n){if(vs.has(n)){const r=Hh(n);return r&&r.default||0}else{const r=U2(t),s=(qw(n)?r.getPropertyValue(n):r[n])||0;return typeof s=="string"?s.trim():s}}measureInstanceViewportBox(t,{transformPagePoint:n}){return M1(t,n)}build(t,n,r){lp(t,n,r.transformTemplate)}scrapeMotionValuesFromProps(t,n,r){return op(t,n,r)}}class H2 extends aS{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=Me}getBaseTargetFromProps(t,n){return t[n]}readValueFromInstance(t,n){if(vs.has(n)){const r=Hh(n);return r&&r.default||0}return n=Y1.has(n)?n:tp(n),t.getAttribute(n)}scrapeMotionValuesFromProps(t,n,r){return eS(t,n,r)}build(t,n,r){up(t,n,this.isSVGTag,r.transformTemplate)}renderInstance(t,n,r,s){X1(t,n,r,s)}mount(t){this.isSVGTag=cp(t.tagName),super.mount(t)}}const Z2=(e,t)=>ip(e)?new H2(t):new W2(t,{allowProjection:e!==x.Fragment}),K2=V2({...NR,...n2,...HN,...r2},Z2),ve=AP(K2),Ql=x.forwardRef(({className:e,...t},n)=>S.jsx("div",{ref:n,className:je("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));Ql.displayName="Card";const q2=x.forwardRef(({className:e,...t},n)=>S.jsx("div",{ref:n,className:je("flex flex-col space-y-1.5 p-6",e),...t}));q2.displayName="CardHeader";const G2=x.forwardRef(({className:e,...t},n)=>S.jsx("div",{ref:n,className:je("text-2xl font-semibold leading-none tracking-tight",e),...t}));G2.displayName="CardTitle";const Q2=x.forwardRef(({className:e,...t},n)=>S.jsx("div",{ref:n,className:je("text-sm text-muted-foreground",e),...t}));Q2.displayName="CardDescription";const Yl=x.forwardRef(({className:e,...t},n)=>S.jsx("div",{ref:n,className:je("p-6 pt-0",e),...t}));Yl.displayName="CardContent";const Y2=x.forwardRef(({className:e,...t},n)=>S.jsx("div",{ref:n,className:je("flex items-center p-6 pt-0",e),...t}));Y2.displayName="CardFooter";const X2=Th("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),ll=x.forwardRef(({className:e,variant:t,size:n,asChild:r=!1,...s},i)=>{const o=r?wx:"button";return S.jsx(o,{className:je(X2({variant:t,size:n,className:e})),ref:i,...s})});ll.displayName="Button";const hf=x.forwardRef(({className:e,type:t,...n},r)=>S.jsx("input",{type:t,className:je("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:r,...n}));hf.displayName="Input";const lS=x.forwardRef(({className:e,...t},n)=>S.jsx("textarea",{className:je("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:n,...t}));lS.displayName="Textarea";var ha=e=>e.type==="checkbox",Yr=e=>e instanceof Date,ct=e=>e==null;const uS=e=>typeof e=="object";var Le=e=>!ct(e)&&!Array.isArray(e)&&uS(e)&&!Yr(e),cS=e=>Le(e)&&e.target?ha(e.target)?e.target.checked:e.target.value:e,J2=e=>e.substring(0,e.search(/\.\d+(\.|$)/))||e,dS=(e,t)=>e.has(J2(t)),eM=e=>{const t=e.constructor&&e.constructor.prototype;return Le(t)&&t.hasOwnProperty("isPrototypeOf")},dp=typeof window<"u"&&typeof window.HTMLElement<"u"&&typeof document<"u";function He(e){let t;const n=Array.isArray(e),r=typeof FileList<"u"?e instanceof FileList:!1;if(e instanceof Date)t=new Date(e);else if(e instanceof Set)t=new Set(e);else if(!(dp&&(e instanceof Blob||r))&&(n||Le(e)))if(t=n?[]:{},!n&&!eM(e))t=e;else for(const s in e)e.hasOwnProperty(s)&&(t[s]=He(e[s]));else return e;return t}var $u=e=>Array.isArray(e)?e.filter(Boolean):[],De=e=>e===void 0,B=(e,t,n)=>{if(!t||!Le(e))return n;const r=$u(t.split(/[,[\].]+?/)).reduce((s,i)=>ct(s)?s:s[i],e);return De(r)||r===e?De(e[t])?n:e[t]:r},bt=e=>typeof e=="boolean",fp=e=>/^\w*$/.test(e),fS=e=>$u(e.replace(/["|']|\]/g,"").split(/\.|\[/)),pe=(e,t,n)=>{let r=-1;const s=fp(t)?[t]:fS(t),i=s.length,o=i-1;for(;++r<i;){const a=s[r];let l=n;if(r!==o){const u=e[a];l=Le(u)||Array.isArray(u)?u:isNaN(+s[r+1])?{}:[]}if(a==="__proto__"||a==="constructor"||a==="prototype")return;e[a]=l,e=e[a]}};const Xl={BLUR:"blur",FOCUS_OUT:"focusout",CHANGE:"change"},Jt={onBlur:"onBlur",onChange:"onChange",onSubmit:"onSubmit",onTouched:"onTouched",all:"all"},Nn={max:"max",min:"min",maxLength:"maxLength",minLength:"minLength",pattern:"pattern",required:"required",validate:"validate"},hS=se.createContext(null),Uu=()=>se.useContext(hS),tM=e=>{const{children:t,...n}=e;return se.createElement(hS.Provider,{value:n},t)};var pS=(e,t,n,r=!0)=>{const s={defaultValues:t._defaultValues};for(const i in e)Object.defineProperty(s,i,{get:()=>{const o=i;return t._proxyFormState[o]!==Jt.all&&(t._proxyFormState[o]=!r||Jt.all),n&&(n[o]=!0),e[o]}});return s};function nM(e){const t=Uu(),{control:n=t.control,disabled:r,name:s,exact:i}=e||{},[o,a]=se.useState(n._formState),l=se.useRef({isDirty:!1,isLoading:!1,dirtyFields:!1,touchedFields:!1,validatingFields:!1,isValidating:!1,isValid:!1,errors:!1}),u=se.useRef(s);return u.current=s,se.useEffect(()=>n._subscribe({name:u.current,formState:l.current,exact:i,callback:c=>{!r&&a({...n._formState,...c})}}),[n,r,i]),se.useEffect(()=>{l.current.isValid&&n._setValid(!0)},[n]),se.useMemo(()=>pS(o,n,l.current,!1),[o,n])}var vn=e=>typeof e=="string",mS=(e,t,n,r,s)=>vn(e)?(r&&t.watch.add(e),B(n,e,s)):Array.isArray(e)?e.map(i=>(r&&t.watch.add(i),B(n,i))):(r&&(t.watchAll=!0),n);function rM(e){const t=Uu(),{control:n=t.control,name:r,defaultValue:s,disabled:i,exact:o}=e||{},a=se.useRef(r),l=se.useRef(s);a.current=r,se.useEffect(()=>n._subscribe({name:a.current,formState:{values:!0},exact:o,callback:d=>!i&&c(mS(a.current,n._names,d.values||n._formValues,!1,l.current))}),[n,i,o]);const[u,c]=se.useState(n._getWatch(r,s));return se.useEffect(()=>n._removeUnmounted()),u}function sM(e){const t=Uu(),{name:n,disabled:r,control:s=t.control,shouldUnregister:i}=e,o=dS(s._names.array,n),a=rM({control:s,name:n,defaultValue:B(s._formValues,n,B(s._defaultValues,n,e.defaultValue)),exact:!0}),l=nM({control:s,name:n,exact:!0}),u=se.useRef(e),c=se.useRef(s.register(n,{...e.rules,value:a,...bt(e.disabled)?{disabled:e.disabled}:{}})),d=se.useMemo(()=>Object.defineProperties({},{invalid:{enumerable:!0,get:()=>!!B(l.errors,n)},isDirty:{enumerable:!0,get:()=>!!B(l.dirtyFields,n)},isTouched:{enumerable:!0,get:()=>!!B(l.touchedFields,n)},isValidating:{enumerable:!0,get:()=>!!B(l.validatingFields,n)},error:{enumerable:!0,get:()=>B(l.errors,n)}}),[l,n]),f=se.useCallback(w=>c.current.onChange({target:{value:cS(w),name:n},type:Xl.CHANGE}),[n]),g=se.useCallback(()=>c.current.onBlur({target:{value:B(s._formValues,n),name:n},type:Xl.BLUR}),[n,s._formValues]),v=se.useCallback(w=>{const p=B(s._fields,n);p&&w&&(p._f.ref={focus:()=>w.focus(),select:()=>w.select(),setCustomValidity:h=>w.setCustomValidity(h),reportValidity:()=>w.reportValidity()})},[s._fields,n]),y=se.useMemo(()=>({name:n,value:a,...bt(r)||l.disabled?{disabled:l.disabled||r}:{},onChange:f,onBlur:g,ref:v}),[n,r,l.disabled,f,g,v,a]);return se.useEffect(()=>{const w=s._options.shouldUnregister||i;s.register(n,{...u.current.rules,...bt(u.current.disabled)?{disabled:u.current.disabled}:{}});const p=(h,m)=>{const _=B(s._fields,h);_&&_._f&&(_._f.mount=m)};if(p(n,!0),w){const h=He(B(s._options.defaultValues,n));pe(s._defaultValues,n,h),De(B(s._formValues,n))&&pe(s._formValues,n,h)}return!o&&s.register(n),()=>{(o?w&&!s._state.action:w)?s.unregister(n):p(n,!1)}},[n,s,o,i]),se.useEffect(()=>{s._setDisabledField({disabled:r,name:n})},[r,n,s]),se.useMemo(()=>({field:y,formState:l,fieldState:d}),[y,l,d])}const iM=e=>e.render(sM(e));var gS=(e,t,n,r,s)=>t?{...n[e],types:{...n[e]&&n[e].types?n[e].types:{},[r]:s||!0}}:{},po=e=>Array.isArray(e)?e:[e],yy=()=>{let e=[];return{get observers(){return e},next:s=>{for(const i of e)i.next&&i.next(s)},subscribe:s=>(e.push(s),{unsubscribe:()=>{e=e.filter(i=>i!==s)}}),unsubscribe:()=>{e=[]}}},pf=e=>ct(e)||!uS(e);function lr(e,t){if(pf(e)||pf(t))return e===t;if(Yr(e)&&Yr(t))return e.getTime()===t.getTime();const n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(const s of n){const i=e[s];if(!r.includes(s))return!1;if(s!=="ref"){const o=t[s];if(Yr(i)&&Yr(o)||Le(i)&&Le(o)||Array.isArray(i)&&Array.isArray(o)?!lr(i,o):i!==o)return!1}}return!0}var lt=e=>Le(e)&&!Object.keys(e).length,hp=e=>e.type==="file",en=e=>typeof e=="function",Jl=e=>{if(!dp)return!1;const t=e?e.ownerDocument:0;return e instanceof(t&&t.defaultView?t.defaultView.HTMLElement:HTMLElement)},yS=e=>e.type==="select-multiple",pp=e=>e.type==="radio",oM=e=>pp(e)||ha(e),Wc=e=>Jl(e)&&e.isConnected;function aM(e,t){const n=t.slice(0,-1).length;let r=0;for(;r<n;)e=De(e)?r++:e[t[r++]];return e}function lM(e){for(const t in e)if(e.hasOwnProperty(t)&&!De(e[t]))return!1;return!0}function Ie(e,t){const n=Array.isArray(t)?t:fp(t)?[t]:fS(t),r=n.length===1?e:aM(e,n),s=n.length-1,i=n[s];return r&&delete r[i],s!==0&&(Le(r)&&lt(r)||Array.isArray(r)&&lM(r))&&Ie(e,n.slice(0,-1)),e}var vS=e=>{for(const t in e)if(en(e[t]))return!0;return!1};function eu(e,t={}){const n=Array.isArray(e);if(Le(e)||n)for(const r in e)Array.isArray(e[r])||Le(e[r])&&!vS(e[r])?(t[r]=Array.isArray(e[r])?[]:{},eu(e[r],t[r])):ct(e[r])||(t[r]=!0);return t}function xS(e,t,n){const r=Array.isArray(e);if(Le(e)||r)for(const s in e)Array.isArray(e[s])||Le(e[s])&&!vS(e[s])?De(t)||pf(n[s])?n[s]=Array.isArray(e[s])?eu(e[s],[]):{...eu(e[s])}:xS(e[s],ct(t)?{}:t[s],n[s]):n[s]=!lr(e[s],t[s]);return n}var Wi=(e,t)=>xS(e,t,eu(t));const vy={value:!1,isValid:!1},xy={value:!0,isValid:!0};var wS=e=>{if(Array.isArray(e)){if(e.length>1){const t=e.filter(n=>n&&n.checked&&!n.disabled).map(n=>n.value);return{value:t,isValid:!!t.length}}return e[0].checked&&!e[0].disabled?e[0].attributes&&!De(e[0].attributes.value)?De(e[0].value)||e[0].value===""?xy:{value:e[0].value,isValid:!0}:xy:vy}return vy},SS=(e,{valueAsNumber:t,valueAsDate:n,setValueAs:r})=>De(e)?e:t?e===""?NaN:e&&+e:n&&vn(e)?new Date(e):r?r(e):e;const wy={isValid:!1,value:null};var _S=e=>Array.isArray(e)?e.reduce((t,n)=>n&&n.checked&&!n.disabled?{isValid:!0,value:n.value}:t,wy):wy;function Sy(e){const t=e.ref;return hp(t)?t.files:pp(t)?_S(e.refs).value:yS(t)?[...t.selectedOptions].map(({value:n})=>n):ha(t)?wS(e.refs).value:SS(De(t.value)?e.ref.value:t.value,e)}var uM=(e,t,n,r)=>{const s={};for(const i of e){const o=B(t,i);o&&pe(s,i,o._f)}return{criteriaMode:n,names:[...e],fields:s,shouldUseNativeValidation:r}},tu=e=>e instanceof RegExp,Hi=e=>De(e)?e:tu(e)?e.source:Le(e)?tu(e.value)?e.value.source:e.value:e,_y=e=>({isOnSubmit:!e||e===Jt.onSubmit,isOnBlur:e===Jt.onBlur,isOnChange:e===Jt.onChange,isOnAll:e===Jt.all,isOnTouch:e===Jt.onTouched});const Ty="AsyncFunction";var cM=e=>!!e&&!!e.validate&&!!(en(e.validate)&&e.validate.constructor.name===Ty||Le(e.validate)&&Object.values(e.validate).find(t=>t.constructor.name===Ty)),dM=e=>e.mount&&(e.required||e.min||e.max||e.maxLength||e.minLength||e.pattern||e.validate),Cy=(e,t,n)=>!n&&(t.watchAll||t.watch.has(e)||[...t.watch].some(r=>e.startsWith(r)&&/^\.\w+/.test(e.slice(r.length))));const mo=(e,t,n,r)=>{for(const s of n||Object.keys(e)){const i=B(e,s);if(i){const{_f:o,...a}=i;if(o){if(o.refs&&o.refs[0]&&t(o.refs[0],s)&&!r)return!0;if(o.ref&&t(o.ref,o.name)&&!r)return!0;if(mo(a,t))break}else if(Le(a)&&mo(a,t))break}}};function ky(e,t,n){const r=B(e,n);if(r||fp(n))return{error:r,name:n};const s=n.split(".");for(;s.length;){const i=s.join("."),o=B(t,i),a=B(e,i);if(o&&!Array.isArray(o)&&n!==i)return{name:n};if(a&&a.type)return{name:i,error:a};s.pop()}return{name:n}}var fM=(e,t,n,r)=>{n(e);const{name:s,...i}=e;return lt(i)||Object.keys(i).length>=Object.keys(t).length||Object.keys(i).find(o=>t[o]===(!r||Jt.all))},hM=(e,t,n)=>!e||!t||e===t||po(e).some(r=>r&&(n?r===t:r.startsWith(t)||t.startsWith(r))),pM=(e,t,n,r,s)=>s.isOnAll?!1:!n&&s.isOnTouch?!(t||e):(n?r.isOnBlur:s.isOnBlur)?!e:(n?r.isOnChange:s.isOnChange)?e:!0,mM=(e,t)=>!$u(B(e,t)).length&&Ie(e,t),gM=(e,t,n)=>{const r=po(B(e,n));return pe(r,"root",t[n]),pe(e,n,r),e},ul=e=>vn(e);function by(e,t,n="validate"){if(ul(e)||Array.isArray(e)&&e.every(ul)||bt(e)&&!e)return{type:n,message:ul(e)?e:"",ref:t}}var Ts=e=>Le(e)&&!tu(e)?e:{value:e,message:""},Ey=async(e,t,n,r,s,i)=>{const{ref:o,refs:a,required:l,maxLength:u,minLength:c,min:d,max:f,pattern:g,validate:v,name:y,valueAsNumber:w,mount:p}=e._f,h=B(n,y);if(!p||t.has(y))return{};const m=a?a[0]:o,_=L=>{s&&m.reportValidity&&(m.setCustomValidity(bt(L)?"":L||""),m.reportValidity())},C={},k=pp(o),P=ha(o),b=k||P,F=(w||hp(o))&&De(o.value)&&De(h)||Jl(o)&&o.value===""||h===""||Array.isArray(h)&&!h.length,j=gS.bind(null,y,r,C),Y=(L,U,O,ne=Nn.maxLength,X=Nn.minLength)=>{const Q=L?U:O;C[y]={type:L?ne:X,message:Q,ref:o,...j(L?ne:X,Q)}};if(i?!Array.isArray(h)||!h.length:l&&(!b&&(F||ct(h))||bt(h)&&!h||P&&!wS(a).isValid||k&&!_S(a).isValid)){const{value:L,message:U}=ul(l)?{value:!!l,message:l}:Ts(l);if(L&&(C[y]={type:Nn.required,message:U,ref:m,...j(Nn.required,U)},!r))return _(U),C}if(!F&&(!ct(d)||!ct(f))){let L,U;const O=Ts(f),ne=Ts(d);if(!ct(h)&&!isNaN(h)){const X=o.valueAsNumber||h&&+h;ct(O.value)||(L=X>O.value),ct(ne.value)||(U=X<ne.value)}else{const X=o.valueAsDate||new Date(h),Q=K=>new Date(new Date().toDateString()+" "+K),A=o.type=="time",I=o.type=="week";vn(O.value)&&h&&(L=A?Q(h)>Q(O.value):I?h>O.value:X>new Date(O.value)),vn(ne.value)&&h&&(U=A?Q(h)<Q(ne.value):I?h<ne.value:X<new Date(ne.value))}if((L||U)&&(Y(!!L,O.message,ne.message,Nn.max,Nn.min),!r))return _(C[y].message),C}if((u||c)&&!F&&(vn(h)||i&&Array.isArray(h))){const L=Ts(u),U=Ts(c),O=!ct(L.value)&&h.length>+L.value,ne=!ct(U.value)&&h.length<+U.value;if((O||ne)&&(Y(O,L.message,U.message),!r))return _(C[y].message),C}if(g&&!F&&vn(h)){const{value:L,message:U}=Ts(g);if(tu(L)&&!h.match(L)&&(C[y]={type:Nn.pattern,message:U,ref:o,...j(Nn.pattern,U)},!r))return _(U),C}if(v){if(en(v)){const L=await v(h,n),U=by(L,m);if(U&&(C[y]={...U,...j(Nn.validate,U.message)},!r))return _(U.message),C}else if(Le(v)){let L={};for(const U in v){if(!lt(L)&&!r)break;const O=by(await v[U](h,n),m,U);O&&(L={...O,...j(U,O.message)},_(O.message),r&&(C[y]=L))}if(!lt(L)&&(C[y]={ref:m,...L},!r))return C}}return _(!0),C};const yM={mode:Jt.onSubmit,reValidateMode:Jt.onChange,shouldFocusError:!0};function vM(e={}){let t={...yM,...e},n={submitCount:0,isDirty:!1,isLoading:en(t.defaultValues),isValidating:!1,isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,touchedFields:{},dirtyFields:{},validatingFields:{},errors:t.errors||{},disabled:t.disabled||!1};const r={};let s=Le(t.defaultValues)||Le(t.values)?He(t.values||t.defaultValues)||{}:{},i=t.shouldUnregister?{}:He(s),o={action:!1,mount:!1,watch:!1},a={mount:new Set,disabled:new Set,unMount:new Set,array:new Set,watch:new Set},l,u=0;const c={isDirty:!1,dirtyFields:!1,validatingFields:!1,touchedFields:!1,isValidating:!1,isValid:!1,errors:!1};let d={...c};const f={array:yy(),state:yy()},g=_y(t.mode),v=_y(t.reValidateMode),y=t.criteriaMode===Jt.all,w=T=>E=>{clearTimeout(u),u=setTimeout(T,E)},p=async T=>{if(!t.disabled&&(c.isValid||d.isValid||T)){const E=t.resolver?lt((await F()).errors):await Y(r,!0);E!==n.isValid&&f.state.next({isValid:E})}},h=(T,E)=>{!t.disabled&&(c.isValidating||c.validatingFields||d.isValidating||d.validatingFields)&&((T||Array.from(a.mount)).forEach(R=>{R&&(E?pe(n.validatingFields,R,E):Ie(n.validatingFields,R))}),f.state.next({validatingFields:n.validatingFields,isValidating:!lt(n.validatingFields)}))},m=(T,E=[],R,H,z=!0,V=!0)=>{if(H&&R&&!t.disabled){if(o.action=!0,V&&Array.isArray(B(r,T))){const q=R(B(r,T),H.argA,H.argB);z&&pe(r,T,q)}if(V&&Array.isArray(B(n.errors,T))){const q=R(B(n.errors,T),H.argA,H.argB);z&&pe(n.errors,T,q),mM(n.errors,T)}if((c.touchedFields||d.touchedFields)&&V&&Array.isArray(B(n.touchedFields,T))){const q=R(B(n.touchedFields,T),H.argA,H.argB);z&&pe(n.touchedFields,T,q)}(c.dirtyFields||d.dirtyFields)&&(n.dirtyFields=Wi(s,i)),f.state.next({name:T,isDirty:U(T,E),dirtyFields:n.dirtyFields,errors:n.errors,isValid:n.isValid})}else pe(i,T,E)},_=(T,E)=>{pe(n.errors,T,E),f.state.next({errors:n.errors})},C=T=>{n.errors=T,f.state.next({errors:n.errors,isValid:!1})},k=(T,E,R,H)=>{const z=B(r,T);if(z){const V=B(i,T,De(R)?B(s,T):R);De(V)||H&&H.defaultChecked||E?pe(i,T,E?V:Sy(z._f)):X(T,V),o.mount&&p()}},P=(T,E,R,H,z)=>{let V=!1,q=!1;const fe={name:T};if(!t.disabled){if(!R||H){(c.isDirty||d.isDirty)&&(q=n.isDirty,n.isDirty=fe.isDirty=U(),V=q!==fe.isDirty);const Ve=lr(B(s,T),E);q=!!B(n.dirtyFields,T),Ve?Ie(n.dirtyFields,T):pe(n.dirtyFields,T,!0),fe.dirtyFields=n.dirtyFields,V=V||(c.dirtyFields||d.dirtyFields)&&q!==!Ve}if(R){const Ve=B(n.touchedFields,T);Ve||(pe(n.touchedFields,T,R),fe.touchedFields=n.touchedFields,V=V||(c.touchedFields||d.touchedFields)&&Ve!==R)}V&&z&&f.state.next(fe)}return V?fe:{}},b=(T,E,R,H)=>{const z=B(n.errors,T),V=(c.isValid||d.isValid)&&bt(E)&&n.isValid!==E;if(t.delayError&&R?(l=w(()=>_(T,R)),l(t.delayError)):(clearTimeout(u),l=null,R?pe(n.errors,T,R):Ie(n.errors,T)),(R?!lr(z,R):z)||!lt(H)||V){const q={...H,...V&&bt(E)?{isValid:E}:{},errors:n.errors,name:T};n={...n,...q},f.state.next(q)}},F=async T=>{h(T,!0);const E=await t.resolver(i,t.context,uM(T||a.mount,r,t.criteriaMode,t.shouldUseNativeValidation));return h(T),E},j=async T=>{const{errors:E}=await F(T);if(T)for(const R of T){const H=B(E,R);H?pe(n.errors,R,H):Ie(n.errors,R)}else n.errors=E;return E},Y=async(T,E,R={valid:!0})=>{for(const H in T){const z=T[H];if(z){const{_f:V,...q}=z;if(V){const fe=a.array.has(V.name),Ve=z._f&&cM(z._f);Ve&&c.validatingFields&&h([H],!0);const Qe=await Ey(z,a.disabled,i,y,t.shouldUseNativeValidation&&!E,fe);if(Ve&&c.validatingFields&&h([H]),Qe[V.name]&&(R.valid=!1,E))break;!E&&(B(Qe,V.name)?fe?gM(n.errors,Qe,V.name):pe(n.errors,V.name,Qe[V.name]):Ie(n.errors,V.name))}!lt(q)&&await Y(q,E,R)}}return R.valid},L=()=>{for(const T of a.unMount){const E=B(r,T);E&&(E._f.refs?E._f.refs.every(R=>!Wc(R)):!Wc(E._f.ref))&&xs(T)}a.unMount=new Set},U=(T,E)=>!t.disabled&&(T&&E&&pe(i,T,E),!lr(ye(),s)),O=(T,E,R)=>mS(T,a,{...o.mount?i:De(E)?s:vn(T)?{[T]:E}:E},R,E),ne=T=>$u(B(o.mount?i:s,T,t.shouldUnregister?B(s,T,[]):[])),X=(T,E,R={})=>{const H=B(r,T);let z=E;if(H){const V=H._f;V&&(!V.disabled&&pe(i,T,SS(E,V)),z=Jl(V.ref)&&ct(E)?"":E,yS(V.ref)?[...V.ref.options].forEach(q=>q.selected=z.includes(q.value)):V.refs?ha(V.ref)?V.refs.length>1?V.refs.forEach(q=>(!q.defaultChecked||!q.disabled)&&(q.checked=Array.isArray(z)?!!z.find(fe=>fe===q.value):z===q.value)):V.refs[0]&&(V.refs[0].checked=!!z):V.refs.forEach(q=>q.checked=q.value===z):hp(V.ref)?V.ref.value="":(V.ref.value=z,V.ref.type||f.state.next({name:T,values:He(i)})))}(R.shouldDirty||R.shouldTouch)&&P(T,z,R.shouldTouch,R.shouldDirty,!0),R.shouldValidate&&re(T)},Q=(T,E,R)=>{for(const H in E){const z=E[H],V=`${T}.${H}`,q=B(r,V);(a.array.has(T)||Le(z)||q&&!q._f)&&!Yr(z)?Q(V,z,R):X(V,z,R)}},A=(T,E,R={})=>{const H=B(r,T),z=a.array.has(T),V=He(E);pe(i,T,V),z?(f.array.next({name:T,values:He(i)}),(c.isDirty||c.dirtyFields||d.isDirty||d.dirtyFields)&&R.shouldDirty&&f.state.next({name:T,dirtyFields:Wi(s,i),isDirty:U(T,V)})):H&&!H._f&&!ct(V)?Q(T,V,R):X(T,V,R),Cy(T,a)&&f.state.next({...n}),f.state.next({name:o.mount?T:void 0,values:He(i)})},I=async T=>{o.mount=!0;const E=T.target;let R=E.name,H=!0;const z=B(r,R),V=q=>{H=Number.isNaN(q)||Yr(q)&&isNaN(q.getTime())||lr(q,B(i,R,q))};if(z){let q,fe;const Ve=E.type?Sy(z._f):cS(T),Qe=T.type===Xl.BLUR||T.type===Xl.FOCUS_OUT,BS=!dM(z._f)&&!t.resolver&&!B(n.errors,R)&&!z._f.deps||pM(Qe,B(n.touchedFields,R),n.isSubmitted,v,g),qu=Cy(R,a,Qe);pe(i,R,Ve),Qe?(z._f.onBlur&&z._f.onBlur(T),l&&l(0)):z._f.onChange&&z._f.onChange(T);const Gu=P(R,Ve,Qe),$S=!lt(Gu)||qu;if(!Qe&&f.state.next({name:R,type:T.type,values:He(i)}),BS)return(c.isValid||d.isValid)&&(t.mode==="onBlur"?Qe&&p():Qe||p()),$S&&f.state.next({name:R,...qu?{}:Gu});if(!Qe&&qu&&f.state.next({...n}),t.resolver){const{errors:yp}=await F([R]);if(V(Ve),H){const US=ky(n.errors,r,R),vp=ky(yp,r,US.name||R);q=vp.error,R=vp.name,fe=lt(yp)}}else h([R],!0),q=(await Ey(z,a.disabled,i,y,t.shouldUseNativeValidation))[R],h([R]),V(Ve),H&&(q?fe=!1:(c.isValid||d.isValid)&&(fe=await Y(r,!0)));H&&(z._f.deps&&re(z._f.deps),b(R,fe,q,Gu))}},K=(T,E)=>{if(B(n.errors,E)&&T.focus)return T.focus(),1},re=async(T,E={})=>{let R,H;const z=po(T);if(t.resolver){const V=await j(De(T)?T:z);R=lt(V),H=T?!z.some(q=>B(V,q)):R}else T?(H=(await Promise.all(z.map(async V=>{const q=B(r,V);return await Y(q&&q._f?{[V]:q}:q)}))).every(Boolean),!(!H&&!n.isValid)&&p()):H=R=await Y(r);return f.state.next({...!vn(T)||(c.isValid||d.isValid)&&R!==n.isValid?{}:{name:T},...t.resolver||!T?{isValid:R}:{},errors:n.errors}),E.shouldFocus&&!H&&mo(r,K,T?z:a.mount),H},ye=T=>{const E={...o.mount?i:s};return De(T)?E:vn(T)?B(E,T):T.map(R=>B(E,R))},yt=(T,E)=>({invalid:!!B((E||n).errors,T),isDirty:!!B((E||n).dirtyFields,T),error:B((E||n).errors,T),isValidating:!!B(n.validatingFields,T),isTouched:!!B((E||n).touchedFields,T)}),vt=T=>{T&&po(T).forEach(E=>Ie(n.errors,E)),f.state.next({errors:T?n.errors:{}})},Xn=(T,E,R)=>{const H=(B(r,T,{_f:{}})._f||{}).ref,z=B(n.errors,T)||{},{ref:V,message:q,type:fe,...Ve}=z;pe(n.errors,T,{...Ve,...E,ref:H}),f.state.next({name:T,errors:n.errors,isValid:!1}),R&&R.shouldFocus&&H&&H.focus&&H.focus()},Ht=(T,E)=>en(T)?f.state.subscribe({next:R=>T(O(void 0,E),R)}):O(T,E,!0),dn=T=>f.state.subscribe({next:E=>{hM(T.name,E.name,T.exact)&&fM(E,T.formState||c,Ss,T.reRenderRoot)&&T.callback({values:{...i},...n,...E})}}).unsubscribe,ma=T=>(o.mount=!0,d={...d,...T.formState},dn({...T,formState:d})),xs=(T,E={})=>{for(const R of T?po(T):a.mount)a.mount.delete(R),a.array.delete(R),E.keepValue||(Ie(r,R),Ie(i,R)),!E.keepError&&Ie(n.errors,R),!E.keepDirty&&Ie(n.dirtyFields,R),!E.keepTouched&&Ie(n.touchedFields,R),!E.keepIsValidating&&Ie(n.validatingFields,R),!t.shouldUnregister&&!E.keepDefaultValue&&Ie(s,R);f.state.next({values:He(i)}),f.state.next({...n,...E.keepDirty?{isDirty:U()}:{}}),!E.keepIsValid&&p()},Ri=({disabled:T,name:E})=>{(bt(T)&&o.mount||T||a.disabled.has(E))&&(T?a.disabled.add(E):a.disabled.delete(E))},ws=(T,E={})=>{let R=B(r,T);const H=bt(E.disabled)||bt(t.disabled);return pe(r,T,{...R||{},_f:{...R&&R._f?R._f:{ref:{name:T}},name:T,mount:!0,...E}}),a.mount.add(T),R?Ri({disabled:bt(E.disabled)?E.disabled:t.disabled,name:T}):k(T,!0,E.value),{...H?{disabled:E.disabled||t.disabled}:{},...t.progressive?{required:!!E.required,min:Hi(E.min),max:Hi(E.max),minLength:Hi(E.minLength),maxLength:Hi(E.maxLength),pattern:Hi(E.pattern)}:{},name:T,onChange:I,onBlur:I,ref:z=>{if(z){ws(T,E),R=B(r,T);const V=De(z.value)&&z.querySelectorAll&&z.querySelectorAll("input,select,textarea")[0]||z,q=oM(V),fe=R._f.refs||[];if(q?fe.find(Ve=>Ve===V):V===R._f.ref)return;pe(r,T,{_f:{...R._f,...q?{refs:[...fe.filter(Wc),V,...Array.isArray(B(s,T))?[{}]:[]],ref:{type:V.type,name:T}}:{ref:V}}}),k(T,!1,void 0,V)}else R=B(r,T,{}),R._f&&(R._f.mount=!1),(t.shouldUnregister||E.shouldUnregister)&&!(dS(a.array,T)&&o.action)&&a.unMount.add(T)}}},Ni=()=>t.shouldFocusError&&mo(r,K,a.mount),Br=T=>{bt(T)&&(f.state.next({disabled:T}),mo(r,(E,R)=>{const H=B(r,R);H&&(E.disabled=H._f.disabled||T,Array.isArray(H._f.refs)&&H._f.refs.forEach(z=>{z.disabled=H._f.disabled||T}))},0,!1))},Mi=(T,E)=>async R=>{let H;R&&(R.preventDefault&&R.preventDefault(),R.persist&&R.persist());let z=He(i);if(f.state.next({isSubmitting:!0}),t.resolver){const{errors:V,values:q}=await F();n.errors=V,z=q}else await Y(r);if(a.disabled.size)for(const V of a.disabled)pe(z,V,void 0);if(Ie(n.errors,"root"),lt(n.errors)){f.state.next({errors:{}});try{await T(z,R)}catch(V){H=V}}else E&&await E({...n.errors},R),Ni(),setTimeout(Ni);if(f.state.next({isSubmitted:!0,isSubmitting:!1,isSubmitSuccessful:lt(n.errors)&&!H,submitCount:n.submitCount+1,errors:n.errors}),H)throw H},Zu=(T,E={})=>{B(r,T)&&(De(E.defaultValue)?A(T,He(B(s,T))):(A(T,E.defaultValue),pe(s,T,He(E.defaultValue))),E.keepTouched||Ie(n.touchedFields,T),E.keepDirty||(Ie(n.dirtyFields,T),n.isDirty=E.defaultValue?U(T,He(B(s,T))):U()),E.keepError||(Ie(n.errors,T),c.isValid&&p()),f.state.next({...n}))},ga=(T,E={})=>{const R=T?He(T):s,H=He(R),z=lt(T),V=z?s:H;if(E.keepDefaultValues||(s=R),!E.keepValues){if(E.keepDirtyValues){const q=new Set([...a.mount,...Object.keys(Wi(s,i))]);for(const fe of Array.from(q))B(n.dirtyFields,fe)?pe(V,fe,B(i,fe)):A(fe,B(V,fe))}else{if(dp&&De(T))for(const q of a.mount){const fe=B(r,q);if(fe&&fe._f){const Ve=Array.isArray(fe._f.refs)?fe._f.refs[0]:fe._f.ref;if(Jl(Ve)){const Qe=Ve.closest("form");if(Qe){Qe.reset();break}}}}for(const q of a.mount)A(q,B(V,q))}i=He(V),f.array.next({values:{...V}}),f.state.next({values:{...V}})}a={mount:E.keepDirtyValues?a.mount:new Set,unMount:new Set,array:new Set,disabled:new Set,watch:new Set,watchAll:!1,focus:""},o.mount=!c.isValid||!!E.keepIsValid||!!E.keepDirtyValues,o.watch=!!t.shouldUnregister,f.state.next({submitCount:E.keepSubmitCount?n.submitCount:0,isDirty:z?!1:E.keepDirty?n.isDirty:!!(E.keepDefaultValues&&!lr(T,s)),isSubmitted:E.keepIsSubmitted?n.isSubmitted:!1,dirtyFields:z?{}:E.keepDirtyValues?E.keepDefaultValues&&i?Wi(s,i):n.dirtyFields:E.keepDefaultValues&&T?Wi(s,T):E.keepDirty?n.dirtyFields:{},touchedFields:E.keepTouched?n.touchedFields:{},errors:E.keepErrors?n.errors:{},isSubmitSuccessful:E.keepIsSubmitSuccessful?n.isSubmitSuccessful:!1,isSubmitting:!1})},ya=(T,E)=>ga(en(T)?T(i):T,E),Ku=(T,E={})=>{const R=B(r,T),H=R&&R._f;if(H){const z=H.refs?H.refs[0]:H.ref;z.focus&&(z.focus(),E.shouldSelect&&en(z.select)&&z.select())}},Ss=T=>{n={...n,...T}},gp={control:{register:ws,unregister:xs,getFieldState:yt,handleSubmit:Mi,setError:Xn,_subscribe:dn,_runSchema:F,_getWatch:O,_getDirty:U,_setValid:p,_setFieldArray:m,_setDisabledField:Ri,_setErrors:C,_getFieldArray:ne,_reset:ga,_resetDefaultValues:()=>en(t.defaultValues)&&t.defaultValues().then(T=>{ya(T,t.resetOptions),f.state.next({isLoading:!1})}),_removeUnmounted:L,_disableForm:Br,_subjects:f,_proxyFormState:c,get _fields(){return r},get _formValues(){return i},get _state(){return o},set _state(T){o=T},get _defaultValues(){return s},get _names(){return a},set _names(T){a=T},get _formState(){return n},get _options(){return t},set _options(T){t={...t,...T}}},subscribe:ma,trigger:re,register:ws,handleSubmit:Mi,watch:Ht,setValue:A,getValues:ye,reset:ya,resetField:Zu,clearErrors:vt,unregister:xs,setError:Xn,setFocus:Ku,getFieldState:yt};return{...gp,formControl:gp}}function xM(e={}){const t=se.useRef(void 0),n=se.useRef(void 0),[r,s]=se.useState({isDirty:!1,isValidating:!1,isLoading:en(e.defaultValues),isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:e.errors||{},disabled:e.disabled||!1,defaultValues:en(e.defaultValues)?void 0:e.defaultValues});t.current||(t.current={...e.formControl?e.formControl:vM(e),formState:r},e.formControl&&e.defaultValues&&!en(e.defaultValues)&&e.formControl.reset(e.defaultValues,e.resetOptions));const i=t.current.control;return i._options=e,se.useLayoutEffect(()=>i._subscribe({formState:i._proxyFormState,callback:()=>s({...i._formState}),reRenderRoot:!0}),[i]),se.useEffect(()=>i._disableForm(e.disabled),[i,e.disabled]),se.useEffect(()=>{if(i._proxyFormState.isDirty){const o=i._getDirty();o!==r.isDirty&&i._subjects.state.next({isDirty:o})}},[i,r.isDirty]),se.useEffect(()=>{e.values&&!lr(e.values,n.current)?(i._reset(e.values,i._options.resetOptions),n.current=e.values,s(o=>({...o}))):i._resetDefaultValues()},[e.values,i]),se.useEffect(()=>{e.errors&&!lt(e.errors)&&i._setErrors(e.errors)},[e.errors,i]),se.useEffect(()=>{i._state.mount||(i._setValid(),i._state.mount=!0),i._state.watch&&(i._state.watch=!1,i._subjects.state.next({...i._formState})),i._removeUnmounted()}),se.useEffect(()=>{e.shouldUnregister&&i._subjects.state.next({values:i._getWatch()})},[e.shouldUnregister,i]),t.current.formState=pS(r,i),t.current}const Py=(e,t,n)=>{if(e&&"reportValidity"in e){const r=B(n,t);e.setCustomValidity(r&&r.message||""),e.reportValidity()}},TS=(e,t)=>{for(const n in t.fields){const r=t.fields[n];r&&r.ref&&"reportValidity"in r.ref?Py(r.ref,n,e):r.refs&&r.refs.forEach(s=>Py(s,n,e))}},wM=(e,t)=>{t.shouldUseNativeValidation&&TS(e,t);const n={};for(const r in e){const s=B(t.fields,r),i=Object.assign(e[r]||{},{ref:s&&s.ref});if(SM(t.names||Object.keys(e),r)){const o=Object.assign({},B(n,r));pe(o,"root",i),pe(n,r,o)}else pe(n,r,i)}return n},SM=(e,t)=>e.some(n=>n.startsWith(t+"."));var _M=function(e,t){for(var n={};e.length;){var r=e[0],s=r.code,i=r.message,o=r.path.join(".");if(!n[o])if("unionErrors"in r){var a=r.unionErrors[0].errors[0];n[o]={message:a.message,type:a.code}}else n[o]={message:i,type:s};if("unionErrors"in r&&r.unionErrors.forEach(function(c){return c.errors.forEach(function(d){return e.push(d)})}),t){var l=n[o].types,u=l&&l[r.code];n[o]=gS(o,t,n,s,u?[].concat(u,r.message):r.message)}e.shift()}return n},TM=function(e,t,n){return n===void 0&&(n={}),function(r,s,i){try{return Promise.resolve(function(o,a){try{var l=Promise.resolve(e[n.mode==="sync"?"parse":"parseAsync"](r,t)).then(function(u){return i.shouldUseNativeValidation&&TS({},i),{errors:{},values:n.raw?r:u}})}catch(u){return a(u)}return l&&l.then?l.then(void 0,a):l}(0,function(o){if(function(a){return Array.isArray(a==null?void 0:a.errors)}(o))return{values:{},errors:wM(_M(o.errors,!i.shouldUseNativeValidation&&i.criteriaMode==="all"),i)};throw o}))}catch(o){return Promise.reject(o)}}},ce;(function(e){e.assertEqual=s=>s;function t(s){}e.assertIs=t;function n(s){throw new Error}e.assertNever=n,e.arrayToEnum=s=>{const i={};for(const o of s)i[o]=o;return i},e.getValidEnumValues=s=>{const i=e.objectKeys(s).filter(a=>typeof s[s[a]]!="number"),o={};for(const a of i)o[a]=s[a];return e.objectValues(o)},e.objectValues=s=>e.objectKeys(s).map(function(i){return s[i]}),e.objectKeys=typeof Object.keys=="function"?s=>Object.keys(s):s=>{const i=[];for(const o in s)Object.prototype.hasOwnProperty.call(s,o)&&i.push(o);return i},e.find=(s,i)=>{for(const o of s)if(i(o))return o},e.isInteger=typeof Number.isInteger=="function"?s=>Number.isInteger(s):s=>typeof s=="number"&&isFinite(s)&&Math.floor(s)===s;function r(s,i=" | "){return s.map(o=>typeof o=="string"?`'${o}'`:o).join(i)}e.joinValues=r,e.jsonStringifyReplacer=(s,i)=>typeof i=="bigint"?i.toString():i})(ce||(ce={}));var mf;(function(e){e.mergeShapes=(t,n)=>({...t,...n})})(mf||(mf={}));const W=ce.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),jn=e=>{switch(typeof e){case"undefined":return W.undefined;case"string":return W.string;case"number":return isNaN(e)?W.nan:W.number;case"boolean":return W.boolean;case"function":return W.function;case"bigint":return W.bigint;case"symbol":return W.symbol;case"object":return Array.isArray(e)?W.array:e===null?W.null:e.then&&typeof e.then=="function"&&e.catch&&typeof e.catch=="function"?W.promise:typeof Map<"u"&&e instanceof Map?W.map:typeof Set<"u"&&e instanceof Set?W.set:typeof Date<"u"&&e instanceof Date?W.date:W.object;default:return W.unknown}},M=ce.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]),CM=e=>JSON.stringify(e,null,2).replace(/"([^"]+)":/g,"$1:");class Nt extends Error{get errors(){return this.issues}constructor(t){super(),this.issues=[],this.addIssue=r=>{this.issues=[...this.issues,r]},this.addIssues=(r=[])=>{this.issues=[...this.issues,...r]};const n=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,n):this.__proto__=n,this.name="ZodError",this.issues=t}format(t){const n=t||function(i){return i.message},r={_errors:[]},s=i=>{for(const o of i.issues)if(o.code==="invalid_union")o.unionErrors.map(s);else if(o.code==="invalid_return_type")s(o.returnTypeError);else if(o.code==="invalid_arguments")s(o.argumentsError);else if(o.path.length===0)r._errors.push(n(o));else{let a=r,l=0;for(;l<o.path.length;){const u=o.path[l];l===o.path.length-1?(a[u]=a[u]||{_errors:[]},a[u]._errors.push(n(o))):a[u]=a[u]||{_errors:[]},a=a[u],l++}}};return s(this),r}static assert(t){if(!(t instanceof Nt))throw new Error(`Not a ZodError: ${t}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,ce.jsonStringifyReplacer,2)}get isEmpty(){return this.issues.length===0}flatten(t=n=>n.message){const n={},r=[];for(const s of this.issues)s.path.length>0?(n[s.path[0]]=n[s.path[0]]||[],n[s.path[0]].push(t(s))):r.push(t(s));return{formErrors:r,fieldErrors:n}}get formErrors(){return this.flatten()}}Nt.create=e=>new Nt(e);const wi=(e,t)=>{let n;switch(e.code){case M.invalid_type:e.received===W.undefined?n="Required":n=`Expected ${e.expected}, received ${e.received}`;break;case M.invalid_literal:n=`Invalid literal value, expected ${JSON.stringify(e.expected,ce.jsonStringifyReplacer)}`;break;case M.unrecognized_keys:n=`Unrecognized key(s) in object: ${ce.joinValues(e.keys,", ")}`;break;case M.invalid_union:n="Invalid input";break;case M.invalid_union_discriminator:n=`Invalid discriminator value. Expected ${ce.joinValues(e.options)}`;break;case M.invalid_enum_value:n=`Invalid enum value. Expected ${ce.joinValues(e.options)}, received '${e.received}'`;break;case M.invalid_arguments:n="Invalid function arguments";break;case M.invalid_return_type:n="Invalid function return type";break;case M.invalid_date:n="Invalid date";break;case M.invalid_string:typeof e.validation=="object"?"includes"in e.validation?(n=`Invalid input: must include "${e.validation.includes}"`,typeof e.validation.position=="number"&&(n=`${n} at one or more positions greater than or equal to ${e.validation.position}`)):"startsWith"in e.validation?n=`Invalid input: must start with "${e.validation.startsWith}"`:"endsWith"in e.validation?n=`Invalid input: must end with "${e.validation.endsWith}"`:ce.assertNever(e.validation):e.validation!=="regex"?n=`Invalid ${e.validation}`:n="Invalid";break;case M.too_small:e.type==="array"?n=`Array must contain ${e.exact?"exactly":e.inclusive?"at least":"more than"} ${e.minimum} element(s)`:e.type==="string"?n=`String must contain ${e.exact?"exactly":e.inclusive?"at least":"over"} ${e.minimum} character(s)`:e.type==="number"?n=`Number must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${e.minimum}`:e.type==="date"?n=`Date must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(e.minimum))}`:n="Invalid input";break;case M.too_big:e.type==="array"?n=`Array must contain ${e.exact?"exactly":e.inclusive?"at most":"less than"} ${e.maximum} element(s)`:e.type==="string"?n=`String must contain ${e.exact?"exactly":e.inclusive?"at most":"under"} ${e.maximum} character(s)`:e.type==="number"?n=`Number must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:e.type==="bigint"?n=`BigInt must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:e.type==="date"?n=`Date must be ${e.exact?"exactly":e.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(e.maximum))}`:n="Invalid input";break;case M.custom:n="Invalid input";break;case M.invalid_intersection_types:n="Intersection results could not be merged";break;case M.not_multiple_of:n=`Number must be a multiple of ${e.multipleOf}`;break;case M.not_finite:n="Number must be finite";break;default:n=t.defaultError,ce.assertNever(e)}return{message:n}};let CS=wi;function kM(e){CS=e}function nu(){return CS}const ru=e=>{const{data:t,path:n,errorMaps:r,issueData:s}=e,i=[...n,...s.path||[]],o={...s,path:i};if(s.message!==void 0)return{...s,path:i,message:s.message};let a="";const l=r.filter(u=>!!u).slice().reverse();for(const u of l)a=u(o,{data:t,defaultError:a}).message;return{...s,path:i,message:a}},bM=[];function $(e,t){const n=nu(),r=ru({issueData:t,data:e.data,path:e.path,errorMaps:[e.common.contextualErrorMap,e.schemaErrorMap,n,n===wi?void 0:wi].filter(s=>!!s)});e.common.issues.push(r)}class ot{constructor(){this.value="valid"}dirty(){this.value==="valid"&&(this.value="dirty")}abort(){this.value!=="aborted"&&(this.value="aborted")}static mergeArray(t,n){const r=[];for(const s of n){if(s.status==="aborted")return te;s.status==="dirty"&&t.dirty(),r.push(s.value)}return{status:t.value,value:r}}static async mergeObjectAsync(t,n){const r=[];for(const s of n){const i=await s.key,o=await s.value;r.push({key:i,value:o})}return ot.mergeObjectSync(t,r)}static mergeObjectSync(t,n){const r={};for(const s of n){const{key:i,value:o}=s;if(i.status==="aborted"||o.status==="aborted")return te;i.status==="dirty"&&t.dirty(),o.status==="dirty"&&t.dirty(),i.value!=="__proto__"&&(typeof o.value<"u"||s.alwaysSet)&&(r[i.value]=o.value)}return{status:t.value,value:r}}}const te=Object.freeze({status:"aborted"}),$s=e=>({status:"dirty",value:e}),ht=e=>({status:"valid",value:e}),gf=e=>e.status==="aborted",yf=e=>e.status==="dirty",hs=e=>e.status==="valid",Bo=e=>typeof Promise<"u"&&e instanceof Promise;function su(e,t,n,r){if(typeof t=="function"?e!==t||!r:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return t.get(e)}function kS(e,t,n,r,s){if(typeof t=="function"?e!==t||!s:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return t.set(e,n),n}var G;(function(e){e.errToObj=t=>typeof t=="string"?{message:t}:t||{},e.toString=t=>typeof t=="string"?t:t==null?void 0:t.message})(G||(G={}));var Xi,Ji;class En{constructor(t,n,r,s){this._cachedPath=[],this.parent=t,this.data=n,this._path=r,this._key=s}get path(){return this._cachedPath.length||(this._key instanceof Array?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}const Ay=(e,t)=>{if(hs(t))return{success:!0,data:t.value};if(!e.common.issues.length)throw new Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;const n=new Nt(e.common.issues);return this._error=n,this._error}}};function ie(e){if(!e)return{};const{errorMap:t,invalid_type_error:n,required_error:r,description:s}=e;if(t&&(n||r))throw new Error(`Can't use "invalid_type_error" or "required_error" in conjunction with custom error map.`);return t?{errorMap:t,description:s}:{errorMap:(o,a)=>{var l,u;const{message:c}=e;return o.code==="invalid_enum_value"?{message:c??a.defaultError}:typeof a.data>"u"?{message:(l=c??r)!==null&&l!==void 0?l:a.defaultError}:o.code!=="invalid_type"?{message:a.defaultError}:{message:(u=c??n)!==null&&u!==void 0?u:a.defaultError}},description:s}}class le{get description(){return this._def.description}_getType(t){return jn(t.data)}_getOrReturnCtx(t,n){return n||{common:t.parent.common,data:t.data,parsedType:jn(t.data),schemaErrorMap:this._def.errorMap,path:t.path,parent:t.parent}}_processInputParams(t){return{status:new ot,ctx:{common:t.parent.common,data:t.data,parsedType:jn(t.data),schemaErrorMap:this._def.errorMap,path:t.path,parent:t.parent}}}_parseSync(t){const n=this._parse(t);if(Bo(n))throw new Error("Synchronous parse encountered promise.");return n}_parseAsync(t){const n=this._parse(t);return Promise.resolve(n)}parse(t,n){const r=this.safeParse(t,n);if(r.success)return r.data;throw r.error}safeParse(t,n){var r;const s={common:{issues:[],async:(r=n==null?void 0:n.async)!==null&&r!==void 0?r:!1,contextualErrorMap:n==null?void 0:n.errorMap},path:(n==null?void 0:n.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:t,parsedType:jn(t)},i=this._parseSync({data:t,path:s.path,parent:s});return Ay(s,i)}"~validate"(t){var n,r;const s={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:t,parsedType:jn(t)};if(!this["~standard"].async)try{const i=this._parseSync({data:t,path:[],parent:s});return hs(i)?{value:i.value}:{issues:s.common.issues}}catch(i){!((r=(n=i==null?void 0:i.message)===null||n===void 0?void 0:n.toLowerCase())===null||r===void 0)&&r.includes("encountered")&&(this["~standard"].async=!0),s.common={issues:[],async:!0}}return this._parseAsync({data:t,path:[],parent:s}).then(i=>hs(i)?{value:i.value}:{issues:s.common.issues})}async parseAsync(t,n){const r=await this.safeParseAsync(t,n);if(r.success)return r.data;throw r.error}async safeParseAsync(t,n){const r={common:{issues:[],contextualErrorMap:n==null?void 0:n.errorMap,async:!0},path:(n==null?void 0:n.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:t,parsedType:jn(t)},s=this._parse({data:t,path:r.path,parent:r}),i=await(Bo(s)?s:Promise.resolve(s));return Ay(r,i)}refine(t,n){const r=s=>typeof n=="string"||typeof n>"u"?{message:n}:typeof n=="function"?n(s):n;return this._refinement((s,i)=>{const o=t(s),a=()=>i.addIssue({code:M.custom,...r(s)});return typeof Promise<"u"&&o instanceof Promise?o.then(l=>l?!0:(a(),!1)):o?!0:(a(),!1)})}refinement(t,n){return this._refinement((r,s)=>t(r)?!0:(s.addIssue(typeof n=="function"?n(r,s):n),!1))}_refinement(t){return new cn({schema:this,typeName:ee.ZodEffects,effect:{type:"refinement",refinement:t}})}superRefine(t){return this._refinement(t)}constructor(t){this.spa=this.safeParseAsync,this._def=t,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:n=>this["~validate"](n)}}optional(){return Cn.create(this,this._def)}nullable(){return jr.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return sn.create(this)}promise(){return _i.create(this,this._def)}or(t){return Ho.create([this,t],this._def)}and(t){return Zo.create(this,t,this._def)}transform(t){return new cn({...ie(this._def),schema:this,typeName:ee.ZodEffects,effect:{type:"transform",transform:t}})}default(t){const n=typeof t=="function"?t:()=>t;return new Yo({...ie(this._def),innerType:this,defaultValue:n,typeName:ee.ZodDefault})}brand(){return new mp({typeName:ee.ZodBranded,type:this,...ie(this._def)})}catch(t){const n=typeof t=="function"?t:()=>t;return new Xo({...ie(this._def),innerType:this,catchValue:n,typeName:ee.ZodCatch})}describe(t){const n=this.constructor;return new n({...this._def,description:t})}pipe(t){return pa.create(this,t)}readonly(){return Jo.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}const EM=/^c[^\s-]{8,}$/i,PM=/^[0-9a-z]+$/,AM=/^[0-9A-HJKMNP-TV-Z]{26}$/i,RM=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,NM=/^[a-z0-9_-]{21}$/i,MM=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,DM=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,OM=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i,LM="^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$";let Hc;const jM=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,VM=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,FM=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,IM=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,zM=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,BM=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,bS="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",$M=new RegExp(`^${bS}$`);function ES(e){let t="([01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d";return e.precision?t=`${t}\\.\\d{${e.precision}}`:e.precision==null&&(t=`${t}(\\.\\d+)?`),t}function UM(e){return new RegExp(`^${ES(e)}$`)}function PS(e){let t=`${bS}T${ES(e)}`;const n=[];return n.push(e.local?"Z?":"Z"),e.offset&&n.push("([+-]\\d{2}:?\\d{2})"),t=`${t}(${n.join("|")})`,new RegExp(`^${t}$`)}function WM(e,t){return!!((t==="v4"||!t)&&jM.test(e)||(t==="v6"||!t)&&FM.test(e))}function HM(e,t){if(!MM.test(e))return!1;try{const[n]=e.split("."),r=n.replace(/-/g,"+").replace(/_/g,"/").padEnd(n.length+(4-n.length%4)%4,"="),s=JSON.parse(atob(r));return!(typeof s!="object"||s===null||!s.typ||!s.alg||t&&s.alg!==t)}catch{return!1}}function ZM(e,t){return!!((t==="v4"||!t)&&VM.test(e)||(t==="v6"||!t)&&IM.test(e))}class tn extends le{_parse(t){if(this._def.coerce&&(t.data=String(t.data)),this._getType(t)!==W.string){const i=this._getOrReturnCtx(t);return $(i,{code:M.invalid_type,expected:W.string,received:i.parsedType}),te}const r=new ot;let s;for(const i of this._def.checks)if(i.kind==="min")t.data.length<i.value&&(s=this._getOrReturnCtx(t,s),$(s,{code:M.too_small,minimum:i.value,type:"string",inclusive:!0,exact:!1,message:i.message}),r.dirty());else if(i.kind==="max")t.data.length>i.value&&(s=this._getOrReturnCtx(t,s),$(s,{code:M.too_big,maximum:i.value,type:"string",inclusive:!0,exact:!1,message:i.message}),r.dirty());else if(i.kind==="length"){const o=t.data.length>i.value,a=t.data.length<i.value;(o||a)&&(s=this._getOrReturnCtx(t,s),o?$(s,{code:M.too_big,maximum:i.value,type:"string",inclusive:!0,exact:!0,message:i.message}):a&&$(s,{code:M.too_small,minimum:i.value,type:"string",inclusive:!0,exact:!0,message:i.message}),r.dirty())}else if(i.kind==="email")OM.test(t.data)||(s=this._getOrReturnCtx(t,s),$(s,{validation:"email",code:M.invalid_string,message:i.message}),r.dirty());else if(i.kind==="emoji")Hc||(Hc=new RegExp(LM,"u")),Hc.test(t.data)||(s=this._getOrReturnCtx(t,s),$(s,{validation:"emoji",code:M.invalid_string,message:i.message}),r.dirty());else if(i.kind==="uuid")RM.test(t.data)||(s=this._getOrReturnCtx(t,s),$(s,{validation:"uuid",code:M.invalid_string,message:i.message}),r.dirty());else if(i.kind==="nanoid")NM.test(t.data)||(s=this._getOrReturnCtx(t,s),$(s,{validation:"nanoid",code:M.invalid_string,message:i.message}),r.dirty());else if(i.kind==="cuid")EM.test(t.data)||(s=this._getOrReturnCtx(t,s),$(s,{validation:"cuid",code:M.invalid_string,message:i.message}),r.dirty());else if(i.kind==="cuid2")PM.test(t.data)||(s=this._getOrReturnCtx(t,s),$(s,{validation:"cuid2",code:M.invalid_string,message:i.message}),r.dirty());else if(i.kind==="ulid")AM.test(t.data)||(s=this._getOrReturnCtx(t,s),$(s,{validation:"ulid",code:M.invalid_string,message:i.message}),r.dirty());else if(i.kind==="url")try{new URL(t.data)}catch{s=this._getOrReturnCtx(t,s),$(s,{validation:"url",code:M.invalid_string,message:i.message}),r.dirty()}else i.kind==="regex"?(i.regex.lastIndex=0,i.regex.test(t.data)||(s=this._getOrReturnCtx(t,s),$(s,{validation:"regex",code:M.invalid_string,message:i.message}),r.dirty())):i.kind==="trim"?t.data=t.data.trim():i.kind==="includes"?t.data.includes(i.value,i.position)||(s=this._getOrReturnCtx(t,s),$(s,{code:M.invalid_string,validation:{includes:i.value,position:i.position},message:i.message}),r.dirty()):i.kind==="toLowerCase"?t.data=t.data.toLowerCase():i.kind==="toUpperCase"?t.data=t.data.toUpperCase():i.kind==="startsWith"?t.data.startsWith(i.value)||(s=this._getOrReturnCtx(t,s),$(s,{code:M.invalid_string,validation:{startsWith:i.value},message:i.message}),r.dirty()):i.kind==="endsWith"?t.data.endsWith(i.value)||(s=this._getOrReturnCtx(t,s),$(s,{code:M.invalid_string,validation:{endsWith:i.value},message:i.message}),r.dirty()):i.kind==="datetime"?PS(i).test(t.data)||(s=this._getOrReturnCtx(t,s),$(s,{code:M.invalid_string,validation:"datetime",message:i.message}),r.dirty()):i.kind==="date"?$M.test(t.data)||(s=this._getOrReturnCtx(t,s),$(s,{code:M.invalid_string,validation:"date",message:i.message}),r.dirty()):i.kind==="time"?UM(i).test(t.data)||(s=this._getOrReturnCtx(t,s),$(s,{code:M.invalid_string,validation:"time",message:i.message}),r.dirty()):i.kind==="duration"?DM.test(t.data)||(s=this._getOrReturnCtx(t,s),$(s,{validation:"duration",code:M.invalid_string,message:i.message}),r.dirty()):i.kind==="ip"?WM(t.data,i.version)||(s=this._getOrReturnCtx(t,s),$(s,{validation:"ip",code:M.invalid_string,message:i.message}),r.dirty()):i.kind==="jwt"?HM(t.data,i.alg)||(s=this._getOrReturnCtx(t,s),$(s,{validation:"jwt",code:M.invalid_string,message:i.message}),r.dirty()):i.kind==="cidr"?ZM(t.data,i.version)||(s=this._getOrReturnCtx(t,s),$(s,{validation:"cidr",code:M.invalid_string,message:i.message}),r.dirty()):i.kind==="base64"?zM.test(t.data)||(s=this._getOrReturnCtx(t,s),$(s,{validation:"base64",code:M.invalid_string,message:i.message}),r.dirty()):i.kind==="base64url"?BM.test(t.data)||(s=this._getOrReturnCtx(t,s),$(s,{validation:"base64url",code:M.invalid_string,message:i.message}),r.dirty()):ce.assertNever(i);return{status:r.value,value:t.data}}_regex(t,n,r){return this.refinement(s=>t.test(s),{validation:n,code:M.invalid_string,...G.errToObj(r)})}_addCheck(t){return new tn({...this._def,checks:[...this._def.checks,t]})}email(t){return this._addCheck({kind:"email",...G.errToObj(t)})}url(t){return this._addCheck({kind:"url",...G.errToObj(t)})}emoji(t){return this._addCheck({kind:"emoji",...G.errToObj(t)})}uuid(t){return this._addCheck({kind:"uuid",...G.errToObj(t)})}nanoid(t){return this._addCheck({kind:"nanoid",...G.errToObj(t)})}cuid(t){return this._addCheck({kind:"cuid",...G.errToObj(t)})}cuid2(t){return this._addCheck({kind:"cuid2",...G.errToObj(t)})}ulid(t){return this._addCheck({kind:"ulid",...G.errToObj(t)})}base64(t){return this._addCheck({kind:"base64",...G.errToObj(t)})}base64url(t){return this._addCheck({kind:"base64url",...G.errToObj(t)})}jwt(t){return this._addCheck({kind:"jwt",...G.errToObj(t)})}ip(t){return this._addCheck({kind:"ip",...G.errToObj(t)})}cidr(t){return this._addCheck({kind:"cidr",...G.errToObj(t)})}datetime(t){var n,r;return typeof t=="string"?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:t}):this._addCheck({kind:"datetime",precision:typeof(t==null?void 0:t.precision)>"u"?null:t==null?void 0:t.precision,offset:(n=t==null?void 0:t.offset)!==null&&n!==void 0?n:!1,local:(r=t==null?void 0:t.local)!==null&&r!==void 0?r:!1,...G.errToObj(t==null?void 0:t.message)})}date(t){return this._addCheck({kind:"date",message:t})}time(t){return typeof t=="string"?this._addCheck({kind:"time",precision:null,message:t}):this._addCheck({kind:"time",precision:typeof(t==null?void 0:t.precision)>"u"?null:t==null?void 0:t.precision,...G.errToObj(t==null?void 0:t.message)})}duration(t){return this._addCheck({kind:"duration",...G.errToObj(t)})}regex(t,n){return this._addCheck({kind:"regex",regex:t,...G.errToObj(n)})}includes(t,n){return this._addCheck({kind:"includes",value:t,position:n==null?void 0:n.position,...G.errToObj(n==null?void 0:n.message)})}startsWith(t,n){return this._addCheck({kind:"startsWith",value:t,...G.errToObj(n)})}endsWith(t,n){return this._addCheck({kind:"endsWith",value:t,...G.errToObj(n)})}min(t,n){return this._addCheck({kind:"min",value:t,...G.errToObj(n)})}max(t,n){return this._addCheck({kind:"max",value:t,...G.errToObj(n)})}length(t,n){return this._addCheck({kind:"length",value:t,...G.errToObj(n)})}nonempty(t){return this.min(1,G.errToObj(t))}trim(){return new tn({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new tn({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new tn({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(t=>t.kind==="datetime")}get isDate(){return!!this._def.checks.find(t=>t.kind==="date")}get isTime(){return!!this._def.checks.find(t=>t.kind==="time")}get isDuration(){return!!this._def.checks.find(t=>t.kind==="duration")}get isEmail(){return!!this._def.checks.find(t=>t.kind==="email")}get isURL(){return!!this._def.checks.find(t=>t.kind==="url")}get isEmoji(){return!!this._def.checks.find(t=>t.kind==="emoji")}get isUUID(){return!!this._def.checks.find(t=>t.kind==="uuid")}get isNANOID(){return!!this._def.checks.find(t=>t.kind==="nanoid")}get isCUID(){return!!this._def.checks.find(t=>t.kind==="cuid")}get isCUID2(){return!!this._def.checks.find(t=>t.kind==="cuid2")}get isULID(){return!!this._def.checks.find(t=>t.kind==="ulid")}get isIP(){return!!this._def.checks.find(t=>t.kind==="ip")}get isCIDR(){return!!this._def.checks.find(t=>t.kind==="cidr")}get isBase64(){return!!this._def.checks.find(t=>t.kind==="base64")}get isBase64url(){return!!this._def.checks.find(t=>t.kind==="base64url")}get minLength(){let t=null;for(const n of this._def.checks)n.kind==="min"&&(t===null||n.value>t)&&(t=n.value);return t}get maxLength(){let t=null;for(const n of this._def.checks)n.kind==="max"&&(t===null||n.value<t)&&(t=n.value);return t}}tn.create=e=>{var t;return new tn({checks:[],typeName:ee.ZodString,coerce:(t=e==null?void 0:e.coerce)!==null&&t!==void 0?t:!1,...ie(e)})};function KM(e,t){const n=(e.toString().split(".")[1]||"").length,r=(t.toString().split(".")[1]||"").length,s=n>r?n:r,i=parseInt(e.toFixed(s).replace(".","")),o=parseInt(t.toFixed(s).replace(".",""));return i%o/Math.pow(10,s)}class Dr extends le{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(t){if(this._def.coerce&&(t.data=Number(t.data)),this._getType(t)!==W.number){const i=this._getOrReturnCtx(t);return $(i,{code:M.invalid_type,expected:W.number,received:i.parsedType}),te}let r;const s=new ot;for(const i of this._def.checks)i.kind==="int"?ce.isInteger(t.data)||(r=this._getOrReturnCtx(t,r),$(r,{code:M.invalid_type,expected:"integer",received:"float",message:i.message}),s.dirty()):i.kind==="min"?(i.inclusive?t.data<i.value:t.data<=i.value)&&(r=this._getOrReturnCtx(t,r),$(r,{code:M.too_small,minimum:i.value,type:"number",inclusive:i.inclusive,exact:!1,message:i.message}),s.dirty()):i.kind==="max"?(i.inclusive?t.data>i.value:t.data>=i.value)&&(r=this._getOrReturnCtx(t,r),$(r,{code:M.too_big,maximum:i.value,type:"number",inclusive:i.inclusive,exact:!1,message:i.message}),s.dirty()):i.kind==="multipleOf"?KM(t.data,i.value)!==0&&(r=this._getOrReturnCtx(t,r),$(r,{code:M.not_multiple_of,multipleOf:i.value,message:i.message}),s.dirty()):i.kind==="finite"?Number.isFinite(t.data)||(r=this._getOrReturnCtx(t,r),$(r,{code:M.not_finite,message:i.message}),s.dirty()):ce.assertNever(i);return{status:s.value,value:t.data}}gte(t,n){return this.setLimit("min",t,!0,G.toString(n))}gt(t,n){return this.setLimit("min",t,!1,G.toString(n))}lte(t,n){return this.setLimit("max",t,!0,G.toString(n))}lt(t,n){return this.setLimit("max",t,!1,G.toString(n))}setLimit(t,n,r,s){return new Dr({...this._def,checks:[...this._def.checks,{kind:t,value:n,inclusive:r,message:G.toString(s)}]})}_addCheck(t){return new Dr({...this._def,checks:[...this._def.checks,t]})}int(t){return this._addCheck({kind:"int",message:G.toString(t)})}positive(t){return this._addCheck({kind:"min",value:0,inclusive:!1,message:G.toString(t)})}negative(t){return this._addCheck({kind:"max",value:0,inclusive:!1,message:G.toString(t)})}nonpositive(t){return this._addCheck({kind:"max",value:0,inclusive:!0,message:G.toString(t)})}nonnegative(t){return this._addCheck({kind:"min",value:0,inclusive:!0,message:G.toString(t)})}multipleOf(t,n){return this._addCheck({kind:"multipleOf",value:t,message:G.toString(n)})}finite(t){return this._addCheck({kind:"finite",message:G.toString(t)})}safe(t){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:G.toString(t)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:G.toString(t)})}get minValue(){let t=null;for(const n of this._def.checks)n.kind==="min"&&(t===null||n.value>t)&&(t=n.value);return t}get maxValue(){let t=null;for(const n of this._def.checks)n.kind==="max"&&(t===null||n.value<t)&&(t=n.value);return t}get isInt(){return!!this._def.checks.find(t=>t.kind==="int"||t.kind==="multipleOf"&&ce.isInteger(t.value))}get isFinite(){let t=null,n=null;for(const r of this._def.checks){if(r.kind==="finite"||r.kind==="int"||r.kind==="multipleOf")return!0;r.kind==="min"?(n===null||r.value>n)&&(n=r.value):r.kind==="max"&&(t===null||r.value<t)&&(t=r.value)}return Number.isFinite(n)&&Number.isFinite(t)}}Dr.create=e=>new Dr({checks:[],typeName:ee.ZodNumber,coerce:(e==null?void 0:e.coerce)||!1,...ie(e)});class Or extends le{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(t){if(this._def.coerce)try{t.data=BigInt(t.data)}catch{return this._getInvalidInput(t)}if(this._getType(t)!==W.bigint)return this._getInvalidInput(t);let r;const s=new ot;for(const i of this._def.checks)i.kind==="min"?(i.inclusive?t.data<i.value:t.data<=i.value)&&(r=this._getOrReturnCtx(t,r),$(r,{code:M.too_small,type:"bigint",minimum:i.value,inclusive:i.inclusive,message:i.message}),s.dirty()):i.kind==="max"?(i.inclusive?t.data>i.value:t.data>=i.value)&&(r=this._getOrReturnCtx(t,r),$(r,{code:M.too_big,type:"bigint",maximum:i.value,inclusive:i.inclusive,message:i.message}),s.dirty()):i.kind==="multipleOf"?t.data%i.value!==BigInt(0)&&(r=this._getOrReturnCtx(t,r),$(r,{code:M.not_multiple_of,multipleOf:i.value,message:i.message}),s.dirty()):ce.assertNever(i);return{status:s.value,value:t.data}}_getInvalidInput(t){const n=this._getOrReturnCtx(t);return $(n,{code:M.invalid_type,expected:W.bigint,received:n.parsedType}),te}gte(t,n){return this.setLimit("min",t,!0,G.toString(n))}gt(t,n){return this.setLimit("min",t,!1,G.toString(n))}lte(t,n){return this.setLimit("max",t,!0,G.toString(n))}lt(t,n){return this.setLimit("max",t,!1,G.toString(n))}setLimit(t,n,r,s){return new Or({...this._def,checks:[...this._def.checks,{kind:t,value:n,inclusive:r,message:G.toString(s)}]})}_addCheck(t){return new Or({...this._def,checks:[...this._def.checks,t]})}positive(t){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:G.toString(t)})}negative(t){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:G.toString(t)})}nonpositive(t){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:G.toString(t)})}nonnegative(t){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:G.toString(t)})}multipleOf(t,n){return this._addCheck({kind:"multipleOf",value:t,message:G.toString(n)})}get minValue(){let t=null;for(const n of this._def.checks)n.kind==="min"&&(t===null||n.value>t)&&(t=n.value);return t}get maxValue(){let t=null;for(const n of this._def.checks)n.kind==="max"&&(t===null||n.value<t)&&(t=n.value);return t}}Or.create=e=>{var t;return new Or({checks:[],typeName:ee.ZodBigInt,coerce:(t=e==null?void 0:e.coerce)!==null&&t!==void 0?t:!1,...ie(e)})};class $o extends le{_parse(t){if(this._def.coerce&&(t.data=!!t.data),this._getType(t)!==W.boolean){const r=this._getOrReturnCtx(t);return $(r,{code:M.invalid_type,expected:W.boolean,received:r.parsedType}),te}return ht(t.data)}}$o.create=e=>new $o({typeName:ee.ZodBoolean,coerce:(e==null?void 0:e.coerce)||!1,...ie(e)});class ps extends le{_parse(t){if(this._def.coerce&&(t.data=new Date(t.data)),this._getType(t)!==W.date){const i=this._getOrReturnCtx(t);return $(i,{code:M.invalid_type,expected:W.date,received:i.parsedType}),te}if(isNaN(t.data.getTime())){const i=this._getOrReturnCtx(t);return $(i,{code:M.invalid_date}),te}const r=new ot;let s;for(const i of this._def.checks)i.kind==="min"?t.data.getTime()<i.value&&(s=this._getOrReturnCtx(t,s),$(s,{code:M.too_small,message:i.message,inclusive:!0,exact:!1,minimum:i.value,type:"date"}),r.dirty()):i.kind==="max"?t.data.getTime()>i.value&&(s=this._getOrReturnCtx(t,s),$(s,{code:M.too_big,message:i.message,inclusive:!0,exact:!1,maximum:i.value,type:"date"}),r.dirty()):ce.assertNever(i);return{status:r.value,value:new Date(t.data.getTime())}}_addCheck(t){return new ps({...this._def,checks:[...this._def.checks,t]})}min(t,n){return this._addCheck({kind:"min",value:t.getTime(),message:G.toString(n)})}max(t,n){return this._addCheck({kind:"max",value:t.getTime(),message:G.toString(n)})}get minDate(){let t=null;for(const n of this._def.checks)n.kind==="min"&&(t===null||n.value>t)&&(t=n.value);return t!=null?new Date(t):null}get maxDate(){let t=null;for(const n of this._def.checks)n.kind==="max"&&(t===null||n.value<t)&&(t=n.value);return t!=null?new Date(t):null}}ps.create=e=>new ps({checks:[],coerce:(e==null?void 0:e.coerce)||!1,typeName:ee.ZodDate,...ie(e)});class iu extends le{_parse(t){if(this._getType(t)!==W.symbol){const r=this._getOrReturnCtx(t);return $(r,{code:M.invalid_type,expected:W.symbol,received:r.parsedType}),te}return ht(t.data)}}iu.create=e=>new iu({typeName:ee.ZodSymbol,...ie(e)});class Uo extends le{_parse(t){if(this._getType(t)!==W.undefined){const r=this._getOrReturnCtx(t);return $(r,{code:M.invalid_type,expected:W.undefined,received:r.parsedType}),te}return ht(t.data)}}Uo.create=e=>new Uo({typeName:ee.ZodUndefined,...ie(e)});class Wo extends le{_parse(t){if(this._getType(t)!==W.null){const r=this._getOrReturnCtx(t);return $(r,{code:M.invalid_type,expected:W.null,received:r.parsedType}),te}return ht(t.data)}}Wo.create=e=>new Wo({typeName:ee.ZodNull,...ie(e)});class Si extends le{constructor(){super(...arguments),this._any=!0}_parse(t){return ht(t.data)}}Si.create=e=>new Si({typeName:ee.ZodAny,...ie(e)});class is extends le{constructor(){super(...arguments),this._unknown=!0}_parse(t){return ht(t.data)}}is.create=e=>new is({typeName:ee.ZodUnknown,...ie(e)});class Qn extends le{_parse(t){const n=this._getOrReturnCtx(t);return $(n,{code:M.invalid_type,expected:W.never,received:n.parsedType}),te}}Qn.create=e=>new Qn({typeName:ee.ZodNever,...ie(e)});class ou extends le{_parse(t){if(this._getType(t)!==W.undefined){const r=this._getOrReturnCtx(t);return $(r,{code:M.invalid_type,expected:W.void,received:r.parsedType}),te}return ht(t.data)}}ou.create=e=>new ou({typeName:ee.ZodVoid,...ie(e)});class sn extends le{_parse(t){const{ctx:n,status:r}=this._processInputParams(t),s=this._def;if(n.parsedType!==W.array)return $(n,{code:M.invalid_type,expected:W.array,received:n.parsedType}),te;if(s.exactLength!==null){const o=n.data.length>s.exactLength.value,a=n.data.length<s.exactLength.value;(o||a)&&($(n,{code:o?M.too_big:M.too_small,minimum:a?s.exactLength.value:void 0,maximum:o?s.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:s.exactLength.message}),r.dirty())}if(s.minLength!==null&&n.data.length<s.minLength.value&&($(n,{code:M.too_small,minimum:s.minLength.value,type:"array",inclusive:!0,exact:!1,message:s.minLength.message}),r.dirty()),s.maxLength!==null&&n.data.length>s.maxLength.value&&($(n,{code:M.too_big,maximum:s.maxLength.value,type:"array",inclusive:!0,exact:!1,message:s.maxLength.message}),r.dirty()),n.common.async)return Promise.all([...n.data].map((o,a)=>s.type._parseAsync(new En(n,o,n.path,a)))).then(o=>ot.mergeArray(r,o));const i=[...n.data].map((o,a)=>s.type._parseSync(new En(n,o,n.path,a)));return ot.mergeArray(r,i)}get element(){return this._def.type}min(t,n){return new sn({...this._def,minLength:{value:t,message:G.toString(n)}})}max(t,n){return new sn({...this._def,maxLength:{value:t,message:G.toString(n)}})}length(t,n){return new sn({...this._def,exactLength:{value:t,message:G.toString(n)}})}nonempty(t){return this.min(1,t)}}sn.create=(e,t)=>new sn({type:e,minLength:null,maxLength:null,exactLength:null,typeName:ee.ZodArray,...ie(t)});function Cs(e){if(e instanceof Ce){const t={};for(const n in e.shape){const r=e.shape[n];t[n]=Cn.create(Cs(r))}return new Ce({...e._def,shape:()=>t})}else return e instanceof sn?new sn({...e._def,type:Cs(e.element)}):e instanceof Cn?Cn.create(Cs(e.unwrap())):e instanceof jr?jr.create(Cs(e.unwrap())):e instanceof Pn?Pn.create(e.items.map(t=>Cs(t))):e}class Ce extends le{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(this._cached!==null)return this._cached;const t=this._def.shape(),n=ce.objectKeys(t);return this._cached={shape:t,keys:n}}_parse(t){if(this._getType(t)!==W.object){const u=this._getOrReturnCtx(t);return $(u,{code:M.invalid_type,expected:W.object,received:u.parsedType}),te}const{status:r,ctx:s}=this._processInputParams(t),{shape:i,keys:o}=this._getCached(),a=[];if(!(this._def.catchall instanceof Qn&&this._def.unknownKeys==="strip"))for(const u in s.data)o.includes(u)||a.push(u);const l=[];for(const u of o){const c=i[u],d=s.data[u];l.push({key:{status:"valid",value:u},value:c._parse(new En(s,d,s.path,u)),alwaysSet:u in s.data})}if(this._def.catchall instanceof Qn){const u=this._def.unknownKeys;if(u==="passthrough")for(const c of a)l.push({key:{status:"valid",value:c},value:{status:"valid",value:s.data[c]}});else if(u==="strict")a.length>0&&($(s,{code:M.unrecognized_keys,keys:a}),r.dirty());else if(u!=="strip")throw new Error("Internal ZodObject error: invalid unknownKeys value.")}else{const u=this._def.catchall;for(const c of a){const d=s.data[c];l.push({key:{status:"valid",value:c},value:u._parse(new En(s,d,s.path,c)),alwaysSet:c in s.data})}}return s.common.async?Promise.resolve().then(async()=>{const u=[];for(const c of l){const d=await c.key,f=await c.value;u.push({key:d,value:f,alwaysSet:c.alwaysSet})}return u}).then(u=>ot.mergeObjectSync(r,u)):ot.mergeObjectSync(r,l)}get shape(){return this._def.shape()}strict(t){return G.errToObj,new Ce({...this._def,unknownKeys:"strict",...t!==void 0?{errorMap:(n,r)=>{var s,i,o,a;const l=(o=(i=(s=this._def).errorMap)===null||i===void 0?void 0:i.call(s,n,r).message)!==null&&o!==void 0?o:r.defaultError;return n.code==="unrecognized_keys"?{message:(a=G.errToObj(t).message)!==null&&a!==void 0?a:l}:{message:l}}}:{}})}strip(){return new Ce({...this._def,unknownKeys:"strip"})}passthrough(){return new Ce({...this._def,unknownKeys:"passthrough"})}extend(t){return new Ce({...this._def,shape:()=>({...this._def.shape(),...t})})}merge(t){return new Ce({unknownKeys:t._def.unknownKeys,catchall:t._def.catchall,shape:()=>({...this._def.shape(),...t._def.shape()}),typeName:ee.ZodObject})}setKey(t,n){return this.augment({[t]:n})}catchall(t){return new Ce({...this._def,catchall:t})}pick(t){const n={};return ce.objectKeys(t).forEach(r=>{t[r]&&this.shape[r]&&(n[r]=this.shape[r])}),new Ce({...this._def,shape:()=>n})}omit(t){const n={};return ce.objectKeys(this.shape).forEach(r=>{t[r]||(n[r]=this.shape[r])}),new Ce({...this._def,shape:()=>n})}deepPartial(){return Cs(this)}partial(t){const n={};return ce.objectKeys(this.shape).forEach(r=>{const s=this.shape[r];t&&!t[r]?n[r]=s:n[r]=s.optional()}),new Ce({...this._def,shape:()=>n})}required(t){const n={};return ce.objectKeys(this.shape).forEach(r=>{if(t&&!t[r])n[r]=this.shape[r];else{let i=this.shape[r];for(;i instanceof Cn;)i=i._def.innerType;n[r]=i}}),new Ce({...this._def,shape:()=>n})}keyof(){return AS(ce.objectKeys(this.shape))}}Ce.create=(e,t)=>new Ce({shape:()=>e,unknownKeys:"strip",catchall:Qn.create(),typeName:ee.ZodObject,...ie(t)});Ce.strictCreate=(e,t)=>new Ce({shape:()=>e,unknownKeys:"strict",catchall:Qn.create(),typeName:ee.ZodObject,...ie(t)});Ce.lazycreate=(e,t)=>new Ce({shape:e,unknownKeys:"strip",catchall:Qn.create(),typeName:ee.ZodObject,...ie(t)});class Ho extends le{_parse(t){const{ctx:n}=this._processInputParams(t),r=this._def.options;function s(i){for(const a of i)if(a.result.status==="valid")return a.result;for(const a of i)if(a.result.status==="dirty")return n.common.issues.push(...a.ctx.common.issues),a.result;const o=i.map(a=>new Nt(a.ctx.common.issues));return $(n,{code:M.invalid_union,unionErrors:o}),te}if(n.common.async)return Promise.all(r.map(async i=>{const o={...n,common:{...n.common,issues:[]},parent:null};return{result:await i._parseAsync({data:n.data,path:n.path,parent:o}),ctx:o}})).then(s);{let i;const o=[];for(const l of r){const u={...n,common:{...n.common,issues:[]},parent:null},c=l._parseSync({data:n.data,path:n.path,parent:u});if(c.status==="valid")return c;c.status==="dirty"&&!i&&(i={result:c,ctx:u}),u.common.issues.length&&o.push(u.common.issues)}if(i)return n.common.issues.push(...i.ctx.common.issues),i.result;const a=o.map(l=>new Nt(l));return $(n,{code:M.invalid_union,unionErrors:a}),te}}get options(){return this._def.options}}Ho.create=(e,t)=>new Ho({options:e,typeName:ee.ZodUnion,...ie(t)});const Dn=e=>e instanceof qo?Dn(e.schema):e instanceof cn?Dn(e.innerType()):e instanceof Go?[e.value]:e instanceof Lr?e.options:e instanceof Qo?ce.objectValues(e.enum):e instanceof Yo?Dn(e._def.innerType):e instanceof Uo?[void 0]:e instanceof Wo?[null]:e instanceof Cn?[void 0,...Dn(e.unwrap())]:e instanceof jr?[null,...Dn(e.unwrap())]:e instanceof mp||e instanceof Jo?Dn(e.unwrap()):e instanceof Xo?Dn(e._def.innerType):[];class Wu extends le{_parse(t){const{ctx:n}=this._processInputParams(t);if(n.parsedType!==W.object)return $(n,{code:M.invalid_type,expected:W.object,received:n.parsedType}),te;const r=this.discriminator,s=n.data[r],i=this.optionsMap.get(s);return i?n.common.async?i._parseAsync({data:n.data,path:n.path,parent:n}):i._parseSync({data:n.data,path:n.path,parent:n}):($(n,{code:M.invalid_union_discriminator,options:Array.from(this.optionsMap.keys()),path:[r]}),te)}get discriminator(){return this._def.discriminator}get options(){return this._def.options}get optionsMap(){return this._def.optionsMap}static create(t,n,r){const s=new Map;for(const i of n){const o=Dn(i.shape[t]);if(!o.length)throw new Error(`A discriminator value for key \`${t}\` could not be extracted from all schema options`);for(const a of o){if(s.has(a))throw new Error(`Discriminator property ${String(t)} has duplicate value ${String(a)}`);s.set(a,i)}}return new Wu({typeName:ee.ZodDiscriminatedUnion,discriminator:t,options:n,optionsMap:s,...ie(r)})}}function vf(e,t){const n=jn(e),r=jn(t);if(e===t)return{valid:!0,data:e};if(n===W.object&&r===W.object){const s=ce.objectKeys(t),i=ce.objectKeys(e).filter(a=>s.indexOf(a)!==-1),o={...e,...t};for(const a of i){const l=vf(e[a],t[a]);if(!l.valid)return{valid:!1};o[a]=l.data}return{valid:!0,data:o}}else if(n===W.array&&r===W.array){if(e.length!==t.length)return{valid:!1};const s=[];for(let i=0;i<e.length;i++){const o=e[i],a=t[i],l=vf(o,a);if(!l.valid)return{valid:!1};s.push(l.data)}return{valid:!0,data:s}}else return n===W.date&&r===W.date&&+e==+t?{valid:!0,data:e}:{valid:!1}}class Zo extends le{_parse(t){const{status:n,ctx:r}=this._processInputParams(t),s=(i,o)=>{if(gf(i)||gf(o))return te;const a=vf(i.value,o.value);return a.valid?((yf(i)||yf(o))&&n.dirty(),{status:n.value,value:a.data}):($(r,{code:M.invalid_intersection_types}),te)};return r.common.async?Promise.all([this._def.left._parseAsync({data:r.data,path:r.path,parent:r}),this._def.right._parseAsync({data:r.data,path:r.path,parent:r})]).then(([i,o])=>s(i,o)):s(this._def.left._parseSync({data:r.data,path:r.path,parent:r}),this._def.right._parseSync({data:r.data,path:r.path,parent:r}))}}Zo.create=(e,t,n)=>new Zo({left:e,right:t,typeName:ee.ZodIntersection,...ie(n)});class Pn extends le{_parse(t){const{status:n,ctx:r}=this._processInputParams(t);if(r.parsedType!==W.array)return $(r,{code:M.invalid_type,expected:W.array,received:r.parsedType}),te;if(r.data.length<this._def.items.length)return $(r,{code:M.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),te;!this._def.rest&&r.data.length>this._def.items.length&&($(r,{code:M.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),n.dirty());const i=[...r.data].map((o,a)=>{const l=this._def.items[a]||this._def.rest;return l?l._parse(new En(r,o,r.path,a)):null}).filter(o=>!!o);return r.common.async?Promise.all(i).then(o=>ot.mergeArray(n,o)):ot.mergeArray(n,i)}get items(){return this._def.items}rest(t){return new Pn({...this._def,rest:t})}}Pn.create=(e,t)=>{if(!Array.isArray(e))throw new Error("You must pass an array of schemas to z.tuple([ ... ])");return new Pn({items:e,typeName:ee.ZodTuple,rest:null,...ie(t)})};class Ko extends le{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(t){const{status:n,ctx:r}=this._processInputParams(t);if(r.parsedType!==W.object)return $(r,{code:M.invalid_type,expected:W.object,received:r.parsedType}),te;const s=[],i=this._def.keyType,o=this._def.valueType;for(const a in r.data)s.push({key:i._parse(new En(r,a,r.path,a)),value:o._parse(new En(r,r.data[a],r.path,a)),alwaysSet:a in r.data});return r.common.async?ot.mergeObjectAsync(n,s):ot.mergeObjectSync(n,s)}get element(){return this._def.valueType}static create(t,n,r){return n instanceof le?new Ko({keyType:t,valueType:n,typeName:ee.ZodRecord,...ie(r)}):new Ko({keyType:tn.create(),valueType:t,typeName:ee.ZodRecord,...ie(n)})}}class au extends le{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(t){const{status:n,ctx:r}=this._processInputParams(t);if(r.parsedType!==W.map)return $(r,{code:M.invalid_type,expected:W.map,received:r.parsedType}),te;const s=this._def.keyType,i=this._def.valueType,o=[...r.data.entries()].map(([a,l],u)=>({key:s._parse(new En(r,a,r.path,[u,"key"])),value:i._parse(new En(r,l,r.path,[u,"value"]))}));if(r.common.async){const a=new Map;return Promise.resolve().then(async()=>{for(const l of o){const u=await l.key,c=await l.value;if(u.status==="aborted"||c.status==="aborted")return te;(u.status==="dirty"||c.status==="dirty")&&n.dirty(),a.set(u.value,c.value)}return{status:n.value,value:a}})}else{const a=new Map;for(const l of o){const u=l.key,c=l.value;if(u.status==="aborted"||c.status==="aborted")return te;(u.status==="dirty"||c.status==="dirty")&&n.dirty(),a.set(u.value,c.value)}return{status:n.value,value:a}}}}au.create=(e,t,n)=>new au({valueType:t,keyType:e,typeName:ee.ZodMap,...ie(n)});class ms extends le{_parse(t){const{status:n,ctx:r}=this._processInputParams(t);if(r.parsedType!==W.set)return $(r,{code:M.invalid_type,expected:W.set,received:r.parsedType}),te;const s=this._def;s.minSize!==null&&r.data.size<s.minSize.value&&($(r,{code:M.too_small,minimum:s.minSize.value,type:"set",inclusive:!0,exact:!1,message:s.minSize.message}),n.dirty()),s.maxSize!==null&&r.data.size>s.maxSize.value&&($(r,{code:M.too_big,maximum:s.maxSize.value,type:"set",inclusive:!0,exact:!1,message:s.maxSize.message}),n.dirty());const i=this._def.valueType;function o(l){const u=new Set;for(const c of l){if(c.status==="aborted")return te;c.status==="dirty"&&n.dirty(),u.add(c.value)}return{status:n.value,value:u}}const a=[...r.data.values()].map((l,u)=>i._parse(new En(r,l,r.path,u)));return r.common.async?Promise.all(a).then(l=>o(l)):o(a)}min(t,n){return new ms({...this._def,minSize:{value:t,message:G.toString(n)}})}max(t,n){return new ms({...this._def,maxSize:{value:t,message:G.toString(n)}})}size(t,n){return this.min(t,n).max(t,n)}nonempty(t){return this.min(1,t)}}ms.create=(e,t)=>new ms({valueType:e,minSize:null,maxSize:null,typeName:ee.ZodSet,...ie(t)});class Ys extends le{constructor(){super(...arguments),this.validate=this.implement}_parse(t){const{ctx:n}=this._processInputParams(t);if(n.parsedType!==W.function)return $(n,{code:M.invalid_type,expected:W.function,received:n.parsedType}),te;function r(a,l){return ru({data:a,path:n.path,errorMaps:[n.common.contextualErrorMap,n.schemaErrorMap,nu(),wi].filter(u=>!!u),issueData:{code:M.invalid_arguments,argumentsError:l}})}function s(a,l){return ru({data:a,path:n.path,errorMaps:[n.common.contextualErrorMap,n.schemaErrorMap,nu(),wi].filter(u=>!!u),issueData:{code:M.invalid_return_type,returnTypeError:l}})}const i={errorMap:n.common.contextualErrorMap},o=n.data;if(this._def.returns instanceof _i){const a=this;return ht(async function(...l){const u=new Nt([]),c=await a._def.args.parseAsync(l,i).catch(g=>{throw u.addIssue(r(l,g)),u}),d=await Reflect.apply(o,this,c);return await a._def.returns._def.type.parseAsync(d,i).catch(g=>{throw u.addIssue(s(d,g)),u})})}else{const a=this;return ht(function(...l){const u=a._def.args.safeParse(l,i);if(!u.success)throw new Nt([r(l,u.error)]);const c=Reflect.apply(o,this,u.data),d=a._def.returns.safeParse(c,i);if(!d.success)throw new Nt([s(c,d.error)]);return d.data})}}parameters(){return this._def.args}returnType(){return this._def.returns}args(...t){return new Ys({...this._def,args:Pn.create(t).rest(is.create())})}returns(t){return new Ys({...this._def,returns:t})}implement(t){return this.parse(t)}strictImplement(t){return this.parse(t)}static create(t,n,r){return new Ys({args:t||Pn.create([]).rest(is.create()),returns:n||is.create(),typeName:ee.ZodFunction,...ie(r)})}}class qo extends le{get schema(){return this._def.getter()}_parse(t){const{ctx:n}=this._processInputParams(t);return this._def.getter()._parse({data:n.data,path:n.path,parent:n})}}qo.create=(e,t)=>new qo({getter:e,typeName:ee.ZodLazy,...ie(t)});class Go extends le{_parse(t){if(t.data!==this._def.value){const n=this._getOrReturnCtx(t);return $(n,{received:n.data,code:M.invalid_literal,expected:this._def.value}),te}return{status:"valid",value:t.data}}get value(){return this._def.value}}Go.create=(e,t)=>new Go({value:e,typeName:ee.ZodLiteral,...ie(t)});function AS(e,t){return new Lr({values:e,typeName:ee.ZodEnum,...ie(t)})}class Lr extends le{constructor(){super(...arguments),Xi.set(this,void 0)}_parse(t){if(typeof t.data!="string"){const n=this._getOrReturnCtx(t),r=this._def.values;return $(n,{expected:ce.joinValues(r),received:n.parsedType,code:M.invalid_type}),te}if(su(this,Xi)||kS(this,Xi,new Set(this._def.values)),!su(this,Xi).has(t.data)){const n=this._getOrReturnCtx(t),r=this._def.values;return $(n,{received:n.data,code:M.invalid_enum_value,options:r}),te}return ht(t.data)}get options(){return this._def.values}get enum(){const t={};for(const n of this._def.values)t[n]=n;return t}get Values(){const t={};for(const n of this._def.values)t[n]=n;return t}get Enum(){const t={};for(const n of this._def.values)t[n]=n;return t}extract(t,n=this._def){return Lr.create(t,{...this._def,...n})}exclude(t,n=this._def){return Lr.create(this.options.filter(r=>!t.includes(r)),{...this._def,...n})}}Xi=new WeakMap;Lr.create=AS;class Qo extends le{constructor(){super(...arguments),Ji.set(this,void 0)}_parse(t){const n=ce.getValidEnumValues(this._def.values),r=this._getOrReturnCtx(t);if(r.parsedType!==W.string&&r.parsedType!==W.number){const s=ce.objectValues(n);return $(r,{expected:ce.joinValues(s),received:r.parsedType,code:M.invalid_type}),te}if(su(this,Ji)||kS(this,Ji,new Set(ce.getValidEnumValues(this._def.values))),!su(this,Ji).has(t.data)){const s=ce.objectValues(n);return $(r,{received:r.data,code:M.invalid_enum_value,options:s}),te}return ht(t.data)}get enum(){return this._def.values}}Ji=new WeakMap;Qo.create=(e,t)=>new Qo({values:e,typeName:ee.ZodNativeEnum,...ie(t)});class _i extends le{unwrap(){return this._def.type}_parse(t){const{ctx:n}=this._processInputParams(t);if(n.parsedType!==W.promise&&n.common.async===!1)return $(n,{code:M.invalid_type,expected:W.promise,received:n.parsedType}),te;const r=n.parsedType===W.promise?n.data:Promise.resolve(n.data);return ht(r.then(s=>this._def.type.parseAsync(s,{path:n.path,errorMap:n.common.contextualErrorMap})))}}_i.create=(e,t)=>new _i({type:e,typeName:ee.ZodPromise,...ie(t)});class cn extends le{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===ee.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(t){const{status:n,ctx:r}=this._processInputParams(t),s=this._def.effect||null,i={addIssue:o=>{$(r,o),o.fatal?n.abort():n.dirty()},get path(){return r.path}};if(i.addIssue=i.addIssue.bind(i),s.type==="preprocess"){const o=s.transform(r.data,i);if(r.common.async)return Promise.resolve(o).then(async a=>{if(n.value==="aborted")return te;const l=await this._def.schema._parseAsync({data:a,path:r.path,parent:r});return l.status==="aborted"?te:l.status==="dirty"||n.value==="dirty"?$s(l.value):l});{if(n.value==="aborted")return te;const a=this._def.schema._parseSync({data:o,path:r.path,parent:r});return a.status==="aborted"?te:a.status==="dirty"||n.value==="dirty"?$s(a.value):a}}if(s.type==="refinement"){const o=a=>{const l=s.refinement(a,i);if(r.common.async)return Promise.resolve(l);if(l instanceof Promise)throw new Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return a};if(r.common.async===!1){const a=this._def.schema._parseSync({data:r.data,path:r.path,parent:r});return a.status==="aborted"?te:(a.status==="dirty"&&n.dirty(),o(a.value),{status:n.value,value:a.value})}else return this._def.schema._parseAsync({data:r.data,path:r.path,parent:r}).then(a=>a.status==="aborted"?te:(a.status==="dirty"&&n.dirty(),o(a.value).then(()=>({status:n.value,value:a.value}))))}if(s.type==="transform")if(r.common.async===!1){const o=this._def.schema._parseSync({data:r.data,path:r.path,parent:r});if(!hs(o))return o;const a=s.transform(o.value,i);if(a instanceof Promise)throw new Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:n.value,value:a}}else return this._def.schema._parseAsync({data:r.data,path:r.path,parent:r}).then(o=>hs(o)?Promise.resolve(s.transform(o.value,i)).then(a=>({status:n.value,value:a})):o);ce.assertNever(s)}}cn.create=(e,t,n)=>new cn({schema:e,typeName:ee.ZodEffects,effect:t,...ie(n)});cn.createWithPreprocess=(e,t,n)=>new cn({schema:t,effect:{type:"preprocess",transform:e},typeName:ee.ZodEffects,...ie(n)});class Cn extends le{_parse(t){return this._getType(t)===W.undefined?ht(void 0):this._def.innerType._parse(t)}unwrap(){return this._def.innerType}}Cn.create=(e,t)=>new Cn({innerType:e,typeName:ee.ZodOptional,...ie(t)});class jr extends le{_parse(t){return this._getType(t)===W.null?ht(null):this._def.innerType._parse(t)}unwrap(){return this._def.innerType}}jr.create=(e,t)=>new jr({innerType:e,typeName:ee.ZodNullable,...ie(t)});class Yo extends le{_parse(t){const{ctx:n}=this._processInputParams(t);let r=n.data;return n.parsedType===W.undefined&&(r=this._def.defaultValue()),this._def.innerType._parse({data:r,path:n.path,parent:n})}removeDefault(){return this._def.innerType}}Yo.create=(e,t)=>new Yo({innerType:e,typeName:ee.ZodDefault,defaultValue:typeof t.default=="function"?t.default:()=>t.default,...ie(t)});class Xo extends le{_parse(t){const{ctx:n}=this._processInputParams(t),r={...n,common:{...n.common,issues:[]}},s=this._def.innerType._parse({data:r.data,path:r.path,parent:{...r}});return Bo(s)?s.then(i=>({status:"valid",value:i.status==="valid"?i.value:this._def.catchValue({get error(){return new Nt(r.common.issues)},input:r.data})})):{status:"valid",value:s.status==="valid"?s.value:this._def.catchValue({get error(){return new Nt(r.common.issues)},input:r.data})}}removeCatch(){return this._def.innerType}}Xo.create=(e,t)=>new Xo({innerType:e,typeName:ee.ZodCatch,catchValue:typeof t.catch=="function"?t.catch:()=>t.catch,...ie(t)});class lu extends le{_parse(t){if(this._getType(t)!==W.nan){const r=this._getOrReturnCtx(t);return $(r,{code:M.invalid_type,expected:W.nan,received:r.parsedType}),te}return{status:"valid",value:t.data}}}lu.create=e=>new lu({typeName:ee.ZodNaN,...ie(e)});const qM=Symbol("zod_brand");class mp extends le{_parse(t){const{ctx:n}=this._processInputParams(t),r=n.data;return this._def.type._parse({data:r,path:n.path,parent:n})}unwrap(){return this._def.type}}class pa extends le{_parse(t){const{status:n,ctx:r}=this._processInputParams(t);if(r.common.async)return(async()=>{const i=await this._def.in._parseAsync({data:r.data,path:r.path,parent:r});return i.status==="aborted"?te:i.status==="dirty"?(n.dirty(),$s(i.value)):this._def.out._parseAsync({data:i.value,path:r.path,parent:r})})();{const s=this._def.in._parseSync({data:r.data,path:r.path,parent:r});return s.status==="aborted"?te:s.status==="dirty"?(n.dirty(),{status:"dirty",value:s.value}):this._def.out._parseSync({data:s.value,path:r.path,parent:r})}}static create(t,n){return new pa({in:t,out:n,typeName:ee.ZodPipeline})}}class Jo extends le{_parse(t){const n=this._def.innerType._parse(t),r=s=>(hs(s)&&(s.value=Object.freeze(s.value)),s);return Bo(n)?n.then(s=>r(s)):r(n)}unwrap(){return this._def.innerType}}Jo.create=(e,t)=>new Jo({innerType:e,typeName:ee.ZodReadonly,...ie(t)});function Ry(e,t){const n=typeof e=="function"?e(t):typeof e=="string"?{message:e}:e;return typeof n=="string"?{message:n}:n}function RS(e,t={},n){return e?Si.create().superRefine((r,s)=>{var i,o;const a=e(r);if(a instanceof Promise)return a.then(l=>{var u,c;if(!l){const d=Ry(t,r),f=(c=(u=d.fatal)!==null&&u!==void 0?u:n)!==null&&c!==void 0?c:!0;s.addIssue({code:"custom",...d,fatal:f})}});if(!a){const l=Ry(t,r),u=(o=(i=l.fatal)!==null&&i!==void 0?i:n)!==null&&o!==void 0?o:!0;s.addIssue({code:"custom",...l,fatal:u})}}):Si.create()}const GM={object:Ce.lazycreate};var ee;(function(e){e.ZodString="ZodString",e.ZodNumber="ZodNumber",e.ZodNaN="ZodNaN",e.ZodBigInt="ZodBigInt",e.ZodBoolean="ZodBoolean",e.ZodDate="ZodDate",e.ZodSymbol="ZodSymbol",e.ZodUndefined="ZodUndefined",e.ZodNull="ZodNull",e.ZodAny="ZodAny",e.ZodUnknown="ZodUnknown",e.ZodNever="ZodNever",e.ZodVoid="ZodVoid",e.ZodArray="ZodArray",e.ZodObject="ZodObject",e.ZodUnion="ZodUnion",e.ZodDiscriminatedUnion="ZodDiscriminatedUnion",e.ZodIntersection="ZodIntersection",e.ZodTuple="ZodTuple",e.ZodRecord="ZodRecord",e.ZodMap="ZodMap",e.ZodSet="ZodSet",e.ZodFunction="ZodFunction",e.ZodLazy="ZodLazy",e.ZodLiteral="ZodLiteral",e.ZodEnum="ZodEnum",e.ZodEffects="ZodEffects",e.ZodNativeEnum="ZodNativeEnum",e.ZodOptional="ZodOptional",e.ZodNullable="ZodNullable",e.ZodDefault="ZodDefault",e.ZodCatch="ZodCatch",e.ZodPromise="ZodPromise",e.ZodBranded="ZodBranded",e.ZodPipeline="ZodPipeline",e.ZodReadonly="ZodReadonly"})(ee||(ee={}));const QM=(e,t={message:`Input not instance of ${e.name}`})=>RS(n=>n instanceof e,t),NS=tn.create,MS=Dr.create,YM=lu.create,XM=Or.create,DS=$o.create,JM=ps.create,eD=iu.create,tD=Uo.create,nD=Wo.create,rD=Si.create,sD=is.create,iD=Qn.create,oD=ou.create,aD=sn.create,lD=Ce.create,uD=Ce.strictCreate,cD=Ho.create,dD=Wu.create,fD=Zo.create,hD=Pn.create,pD=Ko.create,mD=au.create,gD=ms.create,yD=Ys.create,vD=qo.create,xD=Go.create,wD=Lr.create,SD=Qo.create,_D=_i.create,Ny=cn.create,TD=Cn.create,CD=jr.create,kD=cn.createWithPreprocess,bD=pa.create,ED=()=>NS().optional(),PD=()=>MS().optional(),AD=()=>DS().optional(),RD={string:e=>tn.create({...e,coerce:!0}),number:e=>Dr.create({...e,coerce:!0}),boolean:e=>$o.create({...e,coerce:!0}),bigint:e=>Or.create({...e,coerce:!0}),date:e=>ps.create({...e,coerce:!0})},ND=te;var Ua=Object.freeze({__proto__:null,defaultErrorMap:wi,setErrorMap:kM,getErrorMap:nu,makeIssue:ru,EMPTY_PATH:bM,addIssueToContext:$,ParseStatus:ot,INVALID:te,DIRTY:$s,OK:ht,isAborted:gf,isDirty:yf,isValid:hs,isAsync:Bo,get util(){return ce},get objectUtil(){return mf},ZodParsedType:W,getParsedType:jn,ZodType:le,datetimeRegex:PS,ZodString:tn,ZodNumber:Dr,ZodBigInt:Or,ZodBoolean:$o,ZodDate:ps,ZodSymbol:iu,ZodUndefined:Uo,ZodNull:Wo,ZodAny:Si,ZodUnknown:is,ZodNever:Qn,ZodVoid:ou,ZodArray:sn,ZodObject:Ce,ZodUnion:Ho,ZodDiscriminatedUnion:Wu,ZodIntersection:Zo,ZodTuple:Pn,ZodRecord:Ko,ZodMap:au,ZodSet:ms,ZodFunction:Ys,ZodLazy:qo,ZodLiteral:Go,ZodEnum:Lr,ZodNativeEnum:Qo,ZodPromise:_i,ZodEffects:cn,ZodTransformer:cn,ZodOptional:Cn,ZodNullable:jr,ZodDefault:Yo,ZodCatch:Xo,ZodNaN:lu,BRAND:qM,ZodBranded:mp,ZodPipeline:pa,ZodReadonly:Jo,custom:RS,Schema:le,ZodSchema:le,late:GM,get ZodFirstPartyTypeKind(){return ee},coerce:RD,any:rD,array:aD,bigint:XM,boolean:DS,date:JM,discriminatedUnion:dD,effect:Ny,enum:wD,function:yD,instanceof:QM,intersection:fD,lazy:vD,literal:xD,map:mD,nan:YM,nativeEnum:SD,never:iD,null:nD,nullable:CD,number:MS,object:lD,oboolean:AD,onumber:PD,optional:TD,ostring:ED,pipeline:bD,preprocess:kD,promise:_D,record:pD,set:gD,strictObject:uD,string:NS,symbol:eD,transformer:Ny,tuple:hD,undefined:tD,union:cD,unknown:sD,void:oD,NEVER:ND,ZodIssueCode:M,quotelessJson:CM,ZodError:Nt}),MD="Label",OS=x.forwardRef((e,t)=>S.jsx(gt.label,{...e,ref:t,onMouseDown:n=>{var s;n.target.closest("button, input, select, textarea")||((s=e.onMouseDown)==null||s.call(e,n),!n.defaultPrevented&&n.detail>1&&n.preventDefault())}}));OS.displayName=MD;var LS=OS;const DD=Th("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),jS=x.forwardRef(({className:e,...t},n)=>S.jsx(LS,{ref:n,className:je(DD(),e),...t}));jS.displayName=LS.displayName;const OD=tM,VS=x.createContext({}),Zc=({...e})=>S.jsx(VS.Provider,{value:{name:e.name},children:S.jsx(iM,{...e})}),Hu=()=>{const e=x.useContext(VS),t=x.useContext(FS),{getFieldState:n,formState:r}=Uu(),s=n(e.name,r);if(!e)throw new Error("useFormField should be used within <FormField>");const{id:i}=t;return{id:i,name:e.name,formItemId:`${i}-form-item`,formDescriptionId:`${i}-form-item-description`,formMessageId:`${i}-form-item-message`,...s}},FS=x.createContext({}),cl=x.forwardRef(({className:e,...t},n)=>{const r=x.useId();return S.jsx(FS.Provider,{value:{id:r},children:S.jsx("div",{ref:n,className:je("space-y-2",e),...t})})});cl.displayName="FormItem";const LD=x.forwardRef(({className:e,...t},n)=>{const{error:r,formItemId:s}=Hu();return S.jsx(jS,{ref:n,className:je(r&&"text-destructive",e),htmlFor:s,...t})});LD.displayName="FormLabel";const dl=x.forwardRef(({...e},t)=>{const{error:n,formItemId:r,formDescriptionId:s,formMessageId:i}=Hu();return S.jsx(wx,{ref:t,id:r,"aria-describedby":n?`${s} ${i}`:`${s}`,"aria-invalid":!!n,...e})});dl.displayName="FormControl";const jD=x.forwardRef(({className:e,...t},n)=>{const{formDescriptionId:r}=Hu();return S.jsx("p",{ref:n,id:r,className:je("text-sm text-muted-foreground",e),...t})});jD.displayName="FormDescription";const fl=x.forwardRef(({className:e,children:t,...n},r)=>{const{error:s,formMessageId:i}=Hu(),o=s?String((s==null?void 0:s.message)??""):t;return o?S.jsx("p",{ref:r,id:i,className:je("text-sm font-medium text-destructive",e),...n,children:o}):null});fl.displayName="FormMessage";function VD(){return x.useEffect(()=>{typeof window<"u"&&window.particlesJS&&window.particlesJS("particles-js",{particles:{number:{value:50},color:{value:["#ff0055","#00ffaa","#ffffff"]},shape:{type:["circle","triangle","polygon"],polygon:{nb_sides:6}},opacity:{value:.3,random:!0},size:{value:3,random:!0,anim:{enable:!0,speed:2,size_min:.3}},move:{enable:!0,speed:1,direction:"top",random:!0,straight:!1,out_mode:"out",bounce:!1}},interactivity:{detect_on:"canvas",events:{onhover:{enable:!0,mode:"repulse"},onclick:{enable:!0,mode:"push"}},modes:{repulse:{distance:100,duration:.4},push:{particles_nb:4}}},retina_detect:!0})},[]),S.jsx("div",{id:"particles-js",className:"fixed inset-0 z-0 pointer-events-none",style:{background:"transparent"}})}function IS(){const[e,t]=x.useState(!1),n=x.useRef(null),r=x.useRef(null),s=x.useCallback(()=>{if(!e)try{n.current=new(window.AudioContext||window.webkitAudioContext),r.current=new Audio,r.current.loop=!0,r.current.volume=.3,t(!0),console.log("Audio initialized")}catch(a){console.log("Audio initialization failed:",a)}},[e]),i=x.useCallback(a=>{if(!(!e||!n.current))try{const l=n.current,u=l.createOscillator(),c=l.createGain();u.connect(c),c.connect(l.destination);const d={nfc:800,hover:400,success:600,fail:200};u.frequency.setValueAtTime(d[a],l.currentTime),u.type="sine",c.gain.setValueAtTime(.1,l.currentTime),c.gain.exponentialRampToValueAtTime(.01,l.currentTime+.3),u.start(l.currentTime),u.stop(l.currentTime+.3)}catch(l){console.log("Sound play failed:",l)}},[e]),o=x.useCallback(()=>{r.current&&(r.current.paused?r.current.play().catch(console.log):r.current.pause())},[]);return{isInitialized:e,initializeAudio:s,playSound:i,toggleBackgroundMusic:o}}function FD(){const{initializeAudio:e}=IS();return x.useEffect(()=>{const t=()=>{e(),document.removeEventListener("click",t),document.removeEventListener("keydown",t)};return document.addEventListener("click",t),document.addEventListener("keydown",t),()=>{document.removeEventListener("click",t),document.removeEventListener("keydown",t)}},[e]),null}function zS(){const[e,t]=x.useState(!1),[n,r]=x.useState(!1),s=x.useCallback(()=>{const a="NDEFReader"in window;return t(a),a},[]),i=x.useCallback(async()=>{if(!e)return!1;try{const a=await navigator.permissions.query({name:"nfc"}),l=a.state==="granted"||a.state==="prompt";return r(l),l}catch(a){return console.log("NFC permission request failed:",a),!1}},[e]),o=x.useCallback(async()=>{if(!e||!n)throw new Error("NFC not supported or permission denied");try{const a=new window.NDEFReader;return await a.scan(),console.log("NFC scan started successfully"),new Promise((l,u)=>{a.addEventListener("reading",c=>{console.log("NFC tag detected:",c),l(c)}),a.addEventListener("readingerror",c=>{console.log("NFC reading error:",c),u(c)}),setTimeout(()=>{u(new Error("NFC scan timeout"))},1e4)})}catch(a){throw console.log("NFC scan failed:",a),a}},[e,n]);return x.useState(()=>{s()}),{isNFCSupported:e,hasNFCPermission:n,requestNFCPermission:i,scanNFC:o}}function ID({onNFCDetected:e}){const{isNFCSupported:t,requestNFCPermission:n}=zS();return x.useEffect(()=>{t&&n()},[t,n]),x.useEffect(()=>{"serviceWorker"in navigator&&navigator.serviceWorker.register("/sw.js").then(r=>{console.log("Service Worker registered:",r)}).catch(r=>{console.log("Service Worker registration failed:",r)})},[]),null}const zD=Ua.object({name:Ua.string().min(2,"Name must be at least 2 characters"),email:Ua.string().email("Invalid email address"),message:Ua.string().min(10,"Message must be at least 10 characters")}),BD=[{id:1,name:"JASHORESELLBAZAR",description:"E-COMMERCE PLATFORM",playerNumber:"618",status:"active",details:"Full-stack e-commerce solution with modern UI/UX and payment integration.",url:"https://jashoresellbazar.com",color:"from-pink-600 to-red-600"},{id:2,name:"ATHLETEBAZAAR",description:"SPORTS MARKETPLACE",playerNumber:"322",status:"active",details:"Sports equipment marketplace with real-time inventory and athlete profiles.",url:"https://athletebazaar.com",color:"from-green-400 to-blue-400"},{id:3,name:"SHADHINALO",description:"ELIMINATED",playerNumber:"198",status:"eliminated",details:"This project has been eliminated from the current games.",url:"#",color:"from-red-600 to-black"}],$D=[{name:"Facebook",icon:"📘",url:"https://www.facebook.com/fahim.hasan.santo.2024"},{name:"Instagram",icon:"📷",url:"https://www.instagram.com/_fahimsanto/"},{name:"LinkedIn",icon:"💼",url:"https://www.linkedin.com/in/fahim-hasan-santo-583987267/"}];function UD(){const[e,t]=x.useState("456"),[n,r]=x.useState(!1),[s,i]=x.useState(0),[o,a]=x.useState(!1),l=x.useRef(null),{toast:u}=vx(),{playSound:c,toggleBackgroundMusic:d}=IS(),{isNFCSupported:f,scanNFC:g}=zS(),v=xM({resolver:TM(zD),defaultValues:{name:"",email:"",message:""}});x.useEffect(()=>{const m=_=>{l.current&&(l.current.style.left=`${_.clientX}px`,l.current.style.top=`${_.clientY}px`)};return document.addEventListener("mousemove",m),()=>document.removeEventListener("mousemove",m)},[]),x.useEffect(()=>{const m=()=>{const _=window.scrollY/(document.documentElement.scrollHeight-window.innerHeight)*100;i(_)};return window.addEventListener("scroll",m),()=>window.removeEventListener("scroll",m)},[]),x.useEffect(()=>{t((Math.floor(Math.random()*456)+1).toString().padStart(3,"0"))},[]);const y=()=>{r(!n),document.documentElement.classList.toggle("dark")},w=async()=>{try{await g(),c("nfc"),u({title:"NFC Detected",description:"Welcome to Player 456's portfolio!"})}catch(m){console.log("NFC scan failed:",m)}},p=async m=>{try{if(m.name.toLowerCase().includes("test")){a(!0),c("fail"),v.setError("name",{message:"Invalid name detected!"});return}console.log("Contact form submitted:",m),c("success"),a(!1),u({title:"Message Sent!",description:"Your message has been successfully transmitted."}),v.reset()}catch{a(!0),c("fail"),u({title:"Transmission Failed",description:"Please try again later.",variant:"destructive"})}},h=()=>{c("hover")};return S.jsxs("div",{className:"min-h-screen bg-black text-white relative overflow-x-hidden",children:[S.jsx("div",{ref:l,className:"squid-cursor"}),S.jsx("div",{className:"scroll-progress",style:{width:`${s}%`}}),S.jsx(VD,{}),S.jsx(FD,{}),S.jsx(ID,{onNFCDetected:w}),S.jsx("nav",{className:"fixed top-0 left-0 right-0 z-50 p-4",children:S.jsxs("div",{className:"flex justify-between items-center max-w-7xl mx-auto",children:[S.jsxs("div",{className:"flex space-x-4 text-squid-pink text-2xl font-bold",children:[S.jsx(ve.span,{className:"hover:animate-glitch cursor-pointer",whileHover:{scale:1.1},onClick:h,children:"○"}),S.jsx(ve.span,{className:"hover:animate-glitch cursor-pointer",whileHover:{scale:1.1},onClick:h,children:"△"}),S.jsx(ve.span,{className:"hover:animate-glitch cursor-pointer",whileHover:{scale:1.1},onClick:h,children:"□"})]}),S.jsx(ll,{onClick:y,className:"honeycomb bg-squid-pink hover:bg-squid-green w-12 h-12 text-white font-bold transition-all duration-300 hover:animate-liquid",children:n?"☀️":"🌙"})]})}),S.jsxs("section",{className:"min-h-screen flex flex-col justify-center items-center relative z-10 px-4",children:[S.jsxs("div",{className:"absolute top-20 right-4 bg-squid-pink text-white px-4 py-2 rounded-lg font-bold text-xl animate-number-flip",children:["PLAYER ",S.jsx("span",{children:e})]}),S.jsx(ve.div,{className:"squid-shape mb-8",animate:{rotate:[45,50,45]},transition:{duration:3,repeat:1/0}}),S.jsxs("div",{className:"text-center mb-8",children:[S.jsx(ve.h1,{className:"font-bold text-6xl md:text-8xl lg:text-9xl text-white mb-4 glitch-text relative",initial:{opacity:0,y:50},animate:{opacity:1,y:0},transition:{duration:1},"data-text":"FAHIM",children:"FAHIM"}),S.jsx(ve.div,{className:"bg-squid-green text-black px-6 py-3 rounded-lg font-bold text-xl md:text-2xl inline-block hover:animate-pulse-glow mb-8",initial:{opacity:0,scale:0},animate:{opacity:1,scale:1},transition:{delay:.5,duration:.8},children:"PLAYER 456"}),S.jsxs(ve.div,{className:"flex justify-center space-x-6 mt-8",initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{delay:1,duration:.8},children:[S.jsx(ve.a,{href:"https://www.facebook.com/fahim.hasan.santo.2024",target:"_blank",rel:"noopener noreferrer",className:"w-12 h-12 bg-squid-pink rounded-full flex items-center justify-center text-white text-xl hover:bg-squid-green transition-colors duration-300",whileHover:{scale:1.1},whileTap:{scale:.95},children:S.jsx("svg",{className:"w-6 h-6",fill:"currentColor",viewBox:"0 0 24 24",children:S.jsx("path",{d:"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"})})}),S.jsx(ve.a,{href:"https://www.linkedin.com/in/fahim-hasan-santo-583987267/",target:"_blank",rel:"noopener noreferrer",className:"w-12 h-12 bg-squid-pink rounded-full flex items-center justify-center text-white text-xl hover:bg-squid-green transition-colors duration-300",whileHover:{scale:1.1},whileTap:{scale:.95},children:S.jsx("svg",{className:"w-6 h-6",fill:"currentColor",viewBox:"0 0 24 24",children:S.jsx("path",{d:"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"})})}),S.jsx(ve.a,{href:"https://www.instagram.com/_fahimsanto/",target:"_blank",rel:"noopener noreferrer",className:"w-12 h-12 bg-squid-pink rounded-full flex items-center justify-center text-white text-xl hover:bg-squid-green transition-colors duration-300",whileHover:{scale:1.1},whileTap:{scale:.95},children:S.jsx("svg",{className:"w-6 h-6",fill:"currentColor",viewBox:"0 0 24 24",children:S.jsx("path",{d:"M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"})})})]}),S.jsx(ve.p,{className:"text-squid-green font-medium text-lg mt-4 animate-float",initial:{opacity:0},animate:{opacity:1},transition:{delay:1,duration:1},children:"WEB DEVELOPER"})]}),f&&S.jsx(ve.div,{className:"nfc-pulse bg-squid-red rounded-full w-16 h-16 flex items-center justify-center mb-8 cursor-pointer hover:animate-bounce",onClick:w,whileHover:{scale:1.1},whileTap:{scale:.9},children:S.jsx("div",{className:"w-8 h-8 bg-white rounded-full"})}),S.jsx("div",{className:"flex space-x-6 mb-12",children:$D.map((m,_)=>S.jsx(ve.a,{href:m.url,target:"_blank",rel:"noopener noreferrer",className:"honeycomb bg-squid-pink hover:bg-squid-green w-16 h-16 flex items-center justify-center text-white text-2xl transition-all duration-300 hover:animate-liquid cursor-pointer",onMouseEnter:h,whileHover:{scale:1.1},initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:1.2+_*.1},children:m.icon},m.name))}),S.jsx(ve.div,{className:"absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce",initial:{opacity:0},animate:{opacity:1},transition:{delay:2},children:S.jsx("div",{className:"w-6 h-10 border-2 border-squid-green rounded-full flex justify-center",children:S.jsx("div",{className:"w-1 h-3 bg-squid-green rounded-full mt-2 animate-pulse"})})})]}),S.jsx("section",{className:"min-h-screen py-20 px-4 relative z-10",children:S.jsxs("div",{className:"max-w-7xl mx-auto",children:[S.jsx(ve.h2,{className:"font-bold text-4xl md:text-6xl text-squid-green text-center mb-16 hover:animate-glitch",initial:{opacity:0,y:50},whileInView:{opacity:1,y:0},transition:{duration:1},children:"MY PROJECTS"}),S.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16",children:BD.map((m,_)=>S.jsxs(ve.div,{className:"card-3d h-80 relative cursor-pointer",initial:{opacity:0,y:50},whileInView:{opacity:1,y:0},transition:{delay:_*.2,duration:.8},onMouseEnter:h,children:[S.jsx(Ql,{className:`card-face bg-gradient-to-br ${m.color} rounded-xl border-0`,children:S.jsxs(Yl,{className:"p-6 flex flex-col justify-center items-center text-center h-full relative",children:[S.jsxs("div",{className:"absolute top-4 left-4 text-squid-green font-bold text-2xl",children:["PLAYER",S.jsx("br",{}),m.playerNumber]}),S.jsx("h3",{className:"font-bold text-3xl text-white mb-4",children:m.name}),S.jsx("p",{className:"text-white font-medium",children:m.description}),S.jsx("div",{className:`mt-4 w-full h-2 rounded-full ${m.status==="active"?"bg-squid-green":"bg-squid-red opacity-50"}`})]})}),S.jsx(Ql,{className:`card-face card-back bg-gradient-to-br ${m.status==="active"?"from-squid-green to-blue-400 text-black":"from-black to-gray-900 text-white border-squid-red border-2"} rounded-xl`,children:S.jsxs(Yl,{className:"p-6 flex flex-col justify-center items-center text-center h-full",children:[S.jsx("h3",{className:"font-bold text-2xl mb-4",children:m.status==="active"?"PROJECT DETAILS":"PROJECT ELIMINATED"}),S.jsx("p",{className:"font-medium text-sm mb-4",children:m.details}),S.jsx(ll,{className:`${m.status==="active"?"bg-black text-white hover:animate-pulse":"bg-squid-red text-white opacity-50 cursor-not-allowed"} px-4 py-2 rounded-lg font-bold`,onClick:()=>m.status==="active"&&window.open(m.url,"_blank"),disabled:m.status!=="active",children:m.status==="active"?"VIEW PROJECT":"ELIMINATED"})]})})]},m.id))}),S.jsx("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[{number:"528",status:"active"},{number:"105",status:"active"},{number:"067",status:"eliminated"},{number:"194",status:"eliminated"}].map((m,_)=>S.jsxs(ve.div,{className:`bg-black border-2 ${m.status==="active"?"border-squid-green":"border-squid-red"} rounded-lg p-4 text-center hover:animate-float ${m.status==="eliminated"?"opacity-60":""}`,initial:{opacity:0,scale:0},whileInView:{opacity:m.status==="eliminated"?.6:1,scale:1},transition:{delay:_*.1,duration:.5},onMouseEnter:h,children:[S.jsxs("div",{className:`${m.status==="active"?"text-squid-green":"text-squid-red"} font-bold text-lg`,children:["PLAYER",S.jsx("br",{}),m.number]}),m.status==="eliminated"&&S.jsx("div",{className:"text-squid-red font-medium text-xs mt-1",children:"ELIMINATED"}),S.jsx("div",{className:`mt-2 w-full h-1 ${m.status==="active"?"bg-squid-green":"bg-squid-red"} rounded-full`})]},m.number))})]})}),S.jsx("section",{className:"min-h-screen py-20 px-4 relative z-10",children:S.jsxs("div",{className:"max-w-7xl mx-auto",children:[S.jsx(ve.h2,{className:"font-bold text-4xl md:text-6xl text-squid-green text-center mb-16 hover:animate-glitch",initial:{opacity:0,y:50},whileInView:{opacity:1,y:0},transition:{duration:1},children:"CONTACT"}),S.jsxs("div",{className:"lg:hidden flex justify-center items-center gap-8 mb-8",children:[S.jsx(ve.div,{className:"guard-figure-mobile animate-guard-sway",initial:{opacity:0,x:-30,y:-20},whileInView:{opacity:1,x:0,y:0},transition:{duration:1},children:S.jsx("div",{className:"guard-body-mobile",children:S.jsx("div",{className:"guard-mask-triangular-mobile",children:S.jsxs("div",{className:"guard-eyes-mobile",children:[S.jsx("div",{className:"guard-eye-left-mobile"}),S.jsx("div",{className:"guard-eye-right-mobile"})]})})})}),S.jsx(ve.div,{className:"guard-figure-mobile animate-guard-sway",style:{animationDelay:"1s"},initial:{opacity:0,x:30,y:-20},whileInView:{opacity:1,x:0,y:0},transition:{duration:1,delay:.4},children:S.jsx("div",{className:"guard-body-mobile",children:S.jsx("div",{className:"guard-mask-round-mobile",children:S.jsxs("div",{className:"guard-eyes-mobile",children:[S.jsx("div",{className:"guard-eye-left-mobile"}),S.jsx("div",{className:"guard-eye-right-mobile"})]})})})})]}),S.jsxs("div",{className:"flex flex-col lg:flex-row items-start justify-center space-y-8 lg:space-y-0 lg:space-x-12 relative",children:[S.jsx(ve.div,{className:"hidden lg:block guard-figure animate-guard-sway",initial:{opacity:0,x:-50},whileInView:{opacity:1,x:0},transition:{duration:1},children:S.jsx("div",{className:"guard-body",children:S.jsx("div",{className:"guard-mask-triangular",children:S.jsxs("div",{className:"guard-eyes",children:[S.jsx("div",{className:"guard-eye-left"}),S.jsx("div",{className:"guard-eye-right"})]})})})}),S.jsxs(ve.div,{className:"bg-gray-800 border-2 border-squid-pink rounded-lg p-6 md:p-8 w-full max-w-md mx-auto shadow-2xl relative",initial:{opacity:0,y:50,scale:.9},whileInView:{opacity:1,y:0,scale:1},transition:{duration:1,delay:.2,type:"spring",bounce:.2},children:[S.jsxs("div",{className:"text-center mb-6",children:[S.jsx("div",{className:"text-squid-red text-3xl font-bold mb-2",children:"FAILED"}),S.jsx("div",{className:"text-gray-400 text-sm",children:"Complete the form to continue the game"})]}),S.jsx(OD,{...v,children:S.jsxs("form",{onSubmit:v.handleSubmit(p),className:"space-y-4",children:[S.jsxs("div",{children:[S.jsx("label",{className:"block text-squid-green text-sm font-medium mb-2",children:"Name"}),S.jsx(Zc,{control:v.control,name:"name",render:({field:m})=>S.jsxs(cl,{children:[S.jsx(dl,{children:S.jsx(hf,{placeholder:"Enter your player name",className:`w-full bg-gray-900 border ${v.formState.errors.name?"border-squid-red animate-shake":"border-gray-600"} rounded px-4 py-3 text-white placeholder:text-gray-500 focus:outline-none focus:border-squid-green transition-colors`,...m})}),S.jsx(fl,{className:"text-squid-red text-sm mt-1"})]})})]}),S.jsxs("div",{children:[S.jsx("label",{className:"block text-squid-green text-sm font-medium mb-2",children:"Email"}),S.jsx(Zc,{control:v.control,name:"email",render:({field:m})=>S.jsxs(cl,{children:[S.jsx(dl,{children:S.jsx(hf,{type:"email",placeholder:"Enter your email address",className:`w-full bg-gray-900 border ${v.formState.errors.email?"border-squid-red animate-shake":"border-gray-600"} rounded px-4 py-3 text-white placeholder:text-gray-500 focus:outline-none focus:border-squid-green transition-colors`,...m})}),S.jsx(fl,{className:"text-squid-red text-sm mt-1"})]})})]}),S.jsxs("div",{children:[S.jsx("label",{className:"block text-squid-green text-sm font-medium mb-2",children:"Message"}),S.jsx(Zc,{control:v.control,name:"message",render:({field:m})=>S.jsxs(cl,{children:[S.jsx(dl,{children:S.jsx(lS,{placeholder:"Tell me about your project...",rows:4,className:`w-full bg-gray-900 border ${v.formState.errors.message?"border-squid-red animate-shake":"border-gray-600"} rounded px-4 py-3 text-white placeholder:text-gray-500 focus:outline-none focus:border-squid-green transition-colors resize-none`,...m})}),S.jsx(fl,{className:"text-squid-red text-sm mt-1"})]})})]}),S.jsx(ll,{type:"submit",className:"w-full bg-squid-pink hover:bg-squid-green text-white font-bold text-sm py-3 rounded transition-colors disabled:opacity-50 disabled:cursor-not-allowed",disabled:v.formState.isSubmitting,children:v.formState.isSubmitting?"SENDING...":"SUBMIT GAME REQUEST"})]})})]}),S.jsx(ve.div,{className:"hidden lg:block guard-figure animate-guard-sway",style:{animationDelay:"1s"},initial:{opacity:0,x:50},whileInView:{opacity:1,x:0},transition:{duration:1,delay:.4},children:S.jsx("div",{className:"guard-body",children:S.jsx("div",{className:"guard-mask-round",children:S.jsxs("div",{className:"guard-eyes",children:[S.jsx("div",{className:"guard-eye-left"}),S.jsx("div",{className:"guard-eye-right"})]})})})})]})]})}),S.jsx("footer",{className:"py-12 px-4 bg-black border-t-2 border-squid-pink relative z-10",children:S.jsxs("div",{className:"max-w-7xl mx-auto text-center",children:[S.jsxs(ve.div,{className:"flex justify-center items-center space-x-4 mb-8",initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:1},children:[S.jsx("div",{className:"w-8 h-8 bg-squid-pink rounded-full"}),S.jsx("div",{className:"w-8 h-8 bg-white"}),S.jsx("div",{className:"w-8 h-8 bg-squid-green",style:{clipPath:"polygon(50% 0%, 0% 100%, 100% 100%)"}})]}),S.jsx(ve.p,{className:"text-squid-green font-medium text-lg mb-2",initial:{opacity:0},whileInView:{opacity:1},transition:{delay:.2,duration:1},children:"© 2025 Fahim ~ Player 456. All games completed successfully."}),S.jsx(ve.p,{className:"text-gray-400 text-sm",initial:{opacity:0},whileInView:{opacity:1},transition:{delay:.4,duration:1},children:"Designed with Squid Game aesthetics for maximum impact."})]})})]})}function WD(){return S.jsx("div",{className:"min-h-screen w-full flex items-center justify-center bg-gray-50",children:S.jsx(Ql,{className:"w-full max-w-md mx-4",children:S.jsxs(Yl,{className:"pt-6",children:[S.jsxs("div",{className:"flex mb-4 gap-2",children:[S.jsx(vb,{className:"h-8 w-8 text-red-500"}),S.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"404 Page Not Found"})]}),S.jsx("p",{className:"mt-4 text-sm text-gray-600",children:"Did you forget to add the page to the router?"})]})})})}function HD(){return S.jsxs(KC,{children:[S.jsx(Om,{path:"/",component:UD}),S.jsx(Om,{component:WD})]})}function ZD(){return S.jsx(yk,{client:wk,children:S.jsxs(EP,{children:[S.jsx(tE,{}),S.jsx(HD,{})]})})}J0(document.getElementById("root")).render(S.jsx(ZD,{}));
