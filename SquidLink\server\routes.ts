import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";

export async function registerRoutes(app: Express): Promise<Server> {
  // Contact form submission endpoint
  app.post('/api/contact', async (req, res) => {
    try {
      const { name, email, message } = req.body;
      
      // Basic validation
      if (!name || !email || !message) {
        return res.status(400).json({ 
          success: false, 
          message: 'All fields are required' 
        });
      }
      
      // Email validation
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        return res.status(400).json({ 
          success: false, 
          message: 'Invalid email address' 
        });
      }
      
      // Simulate contact form processing
      console.log('Contact form submission:', { name, email, message });
      
      // In a real application, you would:
      // 1. Save to database
      // 2. Send email notification
      // 3. Send confirmation email to user
      
      res.json({ 
        success: true, 
        message: 'Message sent successfully!' 
      });
    } catch (error) {
      console.error('Contact form error:', error);
      res.status(500).json({ 
        success: false, 
        message: 'Failed to send message' 
      });
    }
  });

  // Get player stats endpoint
  app.get('/api/player/:number', async (req, res) => {
    try {
      const playerNumber = req.params.number;
      
      // Mock player data
      const playerData = {
        number: playerNumber,
        status: playerNumber === '456' ? 'active' : 'unknown',
        games_completed: playerNumber === '456' ? 6 : 0,
        rank: playerNumber === '456' ? 1 : null
      };
      
      res.json(playerData);
    } catch (error) {
      console.error('Player stats error:', error);
      res.status(500).json({ 
        success: false, 
        message: 'Failed to get player stats' 
      });
    }
  });

  // NFC detection logging endpoint
  app.post('/api/nfc-detected', async (req, res) => {
    try {
      const { timestamp, userAgent } = req.body;
      
      console.log('NFC detection logged:', { timestamp, userAgent });
      
      res.json({ 
        success: true, 
        message: 'NFC detection logged' 
      });
    } catch (error) {
      console.error('NFC logging error:', error);
      res.status(500).json({ 
        success: false, 
        message: 'Failed to log NFC detection' 
      });
    }
  });

  const httpServer = createServer(app);

  return httpServer;
}
